!function(e){e.fn.asmSelect=function(t){var i={listType:"ol",sortable:!1,highlight:!1,animate:!1,addItemTarget:"bottom",hideWhenAdded:!1,debugMode:!1,removeLabel:"remove",highlightAddedLabel:"Added: ",highlightRemovedLabel:"Removed: ",containerClass:"asmContainer",selectClass:"asmSelect",optionDisabledClass:"asmOptionDisabled",listClass:"asmList",listSortableClass:"asmListSortable",listItemClass:"asmListItem",listItemLabelClass:"asmListItemLabel",removeClass:"asmListItemRemove",highlightClass:"asmHighlight"};return e.extend(i,t),this.each(function(t){var a,s,l,n=e(this),d=!1,o=!1;function r(){d=!0,s.prepend("<option value=''>"+n.attr("title")+"</option>"),n.children("option").each(function(i){var a,s=e(this);s.attr("id")||s.attr("id","asm"+t+"option"+i),a=s.attr("id"),s.is(":selected")?(m(a),h(a,!0)):h(a)}),i.debugMode||n.hide(),c(),d=!1}function h(t,i){if(void 0==i)var i=!1;var a=e("#"+t),l=e("<option>"+a.text()+"</option>").val(a.val()).attr("rel",t);i&&p(l),s.append(l)}function c(){s.children(":eq(0)").prop("selected",!0)}function p(e){e.addClass(i.optionDisabledClass).attr("selected",!1).attr("disabled",!0),i.hideWhenAdded&&e.hide(),s.hide().show()}function m(t){var a=e("#"+t);if(a){var o,r=e("<a></a>").attr("href","#").addClass(i.removeClass).prepend(i.removeLabel).click(function(){return function(t,a){if(void 0==a)var n,o,a=!0;e("#"+t).attr("selected",!1),n=$item=l.children("li[rel="+t+"]"),i.animate&&!d?($prevItem=n.prev("li"),n.animate({opacity:"hide",height:"hide"},100,"linear",function(){$prevItem.animate({height:"-=2px"},50,"swing",function(){$prevItem.animate({height:"+=2px"},100,"swing")}),n.remove()})):n.remove(),(o=e("[rel="+t+"]",i.removeWhenAdded?$selectRemoved:s)).removeClass(i.optionDisabledClass).attr("disabled",!1),i.hideWhenAdded&&o.show(),s.hide().show(),a&&f($item,i.highlightRemovedLabel),g(t,"drop")}(e(this).parent("li").attr("rel")),!1}),h=e("<span></span>").addClass(i.listItemLabelClass).html(a.html()),m=e("<li></li>").attr("rel",t).addClass(i.listItemClass).append(h).append(r).hide();if(!d){if(a.is(":selected"))return;a.attr("selected",!0)}"top"!=i.addItemTarget||d?(l.append(m),i.sortable&&n.append(a)):(l.prepend(m),i.sortable&&n.prepend(a)),o=m,i.animate&&!d?o.animate({opacity:"show",height:"show"},100,"swing",function(){o.animate({height:"+=2px"},50,"swing",function(){o.animate({height:"-=2px"},25,"swing")})}):o.show(),p(e("[rel="+t+"]",s)),!d&&(f(m,i.highlightAddedLabel),c(),i.sortable&&l.sortable("refresh"))}}function f(a,l){if(i.highlight){s.next("#"+i.highlightClass+t).remove();var n=e("<span></span>").hide().addClass(i.highlightClass).attr("id",i.highlightClass+t).html(l+a.children("."+i.listItemLabelClass).slice(0,1).text());s.after(n),n.fadeIn("fast",function(){setTimeout(function(){n.fadeOut("slow")},50)})}}function g(t,i){o=!0,$option=e("#"+t),n.trigger("change",[{option:$option,value:$option.val(),id:t,item:l.children("[rel="+t+"]"),type:i}])}for(;e("#"+i.containerClass+t).length>0;)t++;s=e("<select></select>").addClass(i.selectClass).attr("name",i.selectClass+t).attr("id",i.selectClass+t),$selectRemoved=e("<select></select>"),l=e("<"+i.listType+"></"+i.listType+">").addClass(i.listClass).attr("id",i.listClass+t),a=e("<div></div>").addClass(i.containerClass).attr("id",i.containerClass+t),r(),s.change(function(t){var i=e(this).children("option:selected").slice(0,1).attr("rel");m(i),g(i,"add")}).click(function(){}),n.change(function(e){if(o){o=!1;return}s.empty(),l.empty(),r(),l.hide().fadeIn("fast")}).wrap(a).before(s).before(l),i.sortable&&l.sortable({items:"li."+i.listItemClass,handle:"."+i.listItemLabelClass,axis:"y",update:function(t,i){var a;e(this).children("li").each(function(t){if($option=e("#"+e(this).attr("rel")),e(this).is(".ui-sortable-helper")){a=$option.attr("id");return}n.append($option)}),a&&g(a,"sort")}}).addClass(i.listSortableClass)})}}(jQuery);