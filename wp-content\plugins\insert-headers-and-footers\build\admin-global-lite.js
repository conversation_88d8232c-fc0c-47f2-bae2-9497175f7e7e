(()=>{var e={744:()=>{jQuery(function(e){e(".wpcode-admin-tabs-navigation").on("click",".wpcode-admin-tabs button",function(t){t.preventDefault();const r=e(this).data("target");e(this).addClass("active").parent().siblings().find("button").removeClass("active"),e(document.getElementById(r)).addClass("active").siblings().removeClass("active"),e(document).trigger("wpcode-tab-change",r),window.wpcode_editor&&jQuery.each(window.wpcode_editor,function(e,t){t.codemirror.refresh()})})})}},t={};function r(o){var n=t[o];if(void 0!==n)return n.exports;var a=t[o]={exports:{}};return e[o](a,a.exports,r),a.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var o in t)r.o(t,o)&&!r.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";r(744)})()})();