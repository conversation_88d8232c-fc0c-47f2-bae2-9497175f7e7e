(()=>{var e={18:()=>{!function(e,t,n){const o={l10n:wpcode,isAjaxInProgress:!1,init:function(){o.should_init()&&(o.init_status_toggle(),o.move_screen_options(),o.init_copy(),o.init_type_filter(),o.init_pagination(),o.init_code_type_mobile())},init_type_filter:function(){n("#wpcode-snippet-type-buttons a").click(function(e){if(n(this).hasClass("active")||n(this).hasClass("wpcode_pro_type_lite"))return;e.preventDefault();const t=n(this).data("type");n(this).closest("ul").find("a").removeClass("active"),n(this).addClass("active"),o.show_loader(),o.update_url_and_filter(t)}),n("#wpcode-snippet-type-buttons a.wpcode_pro_type_lite").click(function(e){e.preventDefault(),"blocks"===n(this).data("type")?WPCodeAdminNotices.show_pro_notice(o.l10n.blocks_title,o.l10n.blocks_description,o.l10n.blocks_snippet_upgrade_url,o.l10n.button_text):WPCodeAdminNotices.show_pro_notice(o.l10n.scss_title,o.l10n.scss_description,o.l10n.scss_snippet_upgrade_url,o.l10n.button_text)})},show_loader(){try{const e=n("#wpcode-code-snippets-table .wp-list-table");if(0===e.length)throw new Error("Table element not found");const t=e.offset().top-n("#wpadminbar").outerHeight(),o=e.outerHeight();n("#wpcode-loader .wpcode-loader-overlay").css({top:t,height:o}),n("#wpcode-loader").show()}catch(e){console.error("Failed to show loader:",e.message)}},init_pagination:function(){n(e).on("click",".pagination-links a",function(e){e.preventDefault();const t=n(this).attr("href").split("paged=")[1];o.changePage(t)}),n(e).on("blur","#current-page-selector",function(){const e=parseInt(n(this).val(),10);isNaN(e)||o.changePage(e)}),n(e).on("keypress","#current-page-selector",function(e){"Enter"===e.key&&(e.preventDefault(),n(this).blur())})},changePage:function(e){const n=new URLSearchParams(t.location.search).get("type")||"",i=new URLSearchParams(t.location.search).get("location")||"",a=new URLSearchParams(t.location.search).get("s")||"";o.show_loader(),o.filter_by_type(n,i,a,e).done(function(n){if(n.success){const n=new URL(t.location);n.searchParams.set("paged",e),history.replaceState({},"",n)}})},update_url_and_filter:function(e){const i=new URL(t.location),a=new URLSearchParams(t.location.search),r=n("#total_pages").val(),s=a.get("s")||"",c=a.get("location")||"",l=a.get("view")||"all",d=a.get("filter_action")||"Filter";e?i.searchParams.set("type",e):i.searchParams.delete("type"),parseInt(r)>1?i.searchParams.set("paged",a.get("paged")||"1"):i.searchParams.delete("paged"),i.searchParams.delete("action"),i.searchParams.delete("action2"),i.searchParams.set("page","wpcode"),i.searchParams.set("view",l),i.searchParams.set("filter_action",d),i.searchParams.set("s",s),i.searchParams.set("location",c),history.pushState({},"",i),o.filter_by_type(e,c,s)},show_button_spinner(e){t.WPCodeSpinner.show_button_spinner(e)},filter_by_type:function(e,t,i,a="1"){if(!o.isAjaxInProgress)return o.isAjaxInProgress=!0,n.post(ajaxurl,{_wpnonce:wpcode.nonce,action:"wpcode_filter_snippets_by_type",snippet_type:e,location:t,s:i,paged:a}).done(function(e){n("#wpcode-loader").hide(),e.success?(n("#wpbody-content #wpcode-code-snippets-table").html(e.data.html),n("#wpbody-content .wpcode-content > table").remove()):console.error("Failed to update snippets:",e.data)}).fail(function(e,t,o){n("#wpcode-loader").hide(),console.error("AJAX request failed:",t,o)}).always(function(){o.isAjaxInProgress=!1})},should_init:function(){return null!==e.getElementById("wpcode-code-snippets-table")},init_status_toggle:function(){n("#wpbody-content").on("change",".wpcode-status-toggle",function(){const e=n(this),t=e.is(":checked"),i=e.data("id");o.update_status(t,i).fail(function(){e.prop("checked",!1)}).done(function(n){!1===n.success&&(e.prop("checked",!t),n.data.message&&WPCodeAdminNotices.add_notice(n.data.message,"error"))}).fail(function(e){e.responseText&&WPCodeAdminNotices.add_notice(e.responseText,"error")})})},update_status:function(e,t){return n.post(ajaxurl,{_wpnonce:o.l10n.nonce,action:"wpcode_update_snippet_status",snippet_id:t,active:e,multisite:o.l10n.multisite})},move_screen_options:function(){n("#screen-meta-links, #screen-meta").prependTo("#wpcode-header-between").show()},init_copy:function(){n("#wpbody-content").on("click",".wpcode-copy",function(e){e.preventDefault();const t=n(this).data("copy-value"),o=n(this);t&&(navigator.clipboard.writeText(t),o.addClass("wpcode-show-success-icon"),setTimeout(function(){o.removeClass("wpcode-show-success-icon")},500))})},init_code_type_mobile:function(){n(".wpcode-mobile-dropdown-toggle").on("click",function(){const e=n(this).data("target");n(e).toggleClass("wpcode-open"),n(e).one("click","a",function(){n(e).removeClass("wpcode-open")})})}};t.WPCodeSnippetsTable=o,o.init()}(document,window,jQuery)},75:()=>{jconfirm.defaults={closeIcon:!1,backgroundDismiss:!1,escapeKey:!0,animationBounce:1,useBootstrap:!1,theme:"modern",boxWidth:"560px",type:"blue",animateFromElement:!1,scrollToPreviousElement:!1}},132:()=>{(window.WPCodeLibraryTabs||function(e,t,n){const o={init:function(){o.find_elements(),o.should_init()&&o.add_events()},find_elements:function(){o.tabs=n(".wpcode-library-tab"),o.buttons=n(".wpcode-library-tab-button")},should_init:function(){return o.tabs.length>0},add_events:function(){o.buttons.on("click",function(e){e.preventDefault(),o.switch_tab(n(this))})},switch_tab:function(e){const t=e.data("tab");o.buttons.removeClass("wpcode-library-tab-button-active"),e.addClass("wpcode-library-tab-button-active"),o.tabs.removeClass("wpcode-library-tab-active"),o.tabs.filter('[data-tab="'+t+'"]').addClass("wpcode-library-tab-active")}};return o}(document,window,jQuery)).init()},145:()=>{(window.WPCodeAdminCodeEditor||function(e,t,n){const o={l18n:wpcode,init(){t.WPCodeAdminCodeEditor=o},switch_code_mode(e,t){const n=o.get_editor(e);if(void 0===n)return!1;n.setOption("mode",o.get_mime_for_code_type(t)),n.setOption("lint",o.get_lint_for_code_type(t)),jQuery(n.getTextArea()).closest(".wpcode-code-textarea").attr("data-code-type",t)},get_editor(e){if("undefined"!=typeof wpcode_editor)return wpcode_editor[e]?wpcode_editor[e].codemirror:void 0},set_value(e,t){const n=o.get_editor(e);if(void 0===n)return!1;n.getDoc().setValue(t)},get_mime_for_code_type:e=>o.l18n.code_type_options[e].mime,get_lint_for_code_type:e=>o.l18n.code_type_options[e].lint,refresh(e){"undefined"!=typeof wpcode_editor&&o.get_editor(e).refresh()},get_value:e=>void 0===o.get_editor(e)?n("#"+e).val():o.get_editor(e).getValue(),refresh_all(){"undefined"!=typeof wpcode_editor&&n.each(wpcode_editor,function(e,t){t.codemirror.refresh()})}};return o}(document,window,jQuery)).init()},197:()=>{window.WPCodeItemsList||function(e,t,n){const o=function(e){this.container=n(e),this.category="*",this.search_term="",this.categories_list=this.container.find(".wpcode-items-filters"),this.search_input=this.container.find(".wpcode-items-search-input"),this.items=this.container.find(".wpcode-list-item"),this.banner=null,this.init()};o.prototype={init:function(){this.should_init()&&(this.init_category_switch(),this.init_search(),this.show_connect_banner(),this.init_custom_event_handlers())},init_custom_event_handlers(){this.container.on("wpcode_reset_items",()=>{this.reset_items()}),this.container.on("wpcode_select_item",(e,t)=>{this.set_item(t)})},set_item(e){this.reset_items();const t=this.items.filter(function(){return n(this).data("id")===e});this.items.removeClass("wpcode-list-item-selected"),t.addClass("wpcode-list-item-selected");const o=t.data("categories"),i=o.length>0?o[0]:"*";this.switch_to_category(i);const a=this.categories_list.find(`button[data-category="${i}"]`);this.switch_category_button(a);const r=t.find('input[type="radio"]');r.length>0&&r.prop("checked",!0)},reset_items(){this.search_input.val(""),this.search_term="";const e=this.categories_list.find("button").first();this.switch_to_category(e.data("category")),this.switch_category_button(e)},should_init:function(){return this.categories_list.length>0},init_category_switch:function(){const e=this;this.categories_list.on("click","button",function(){const t=n(this);t.hasClass("wpcode-active")||(e.switch_to_category(t.data("category")),e.switch_category_button(t))})},switch_category_button:function(e){this.categories_list.find("button").removeClass("wpcode-active"),e.addClass("wpcode-active")},switch_to_category:function(e){this.category=e,this.filter_items()},filter_items:function(){let e;const t=this,o=this.items.filter(function(){return"*"===t.category||n(this).data("categories").indexOf(t.category)>-1});if(t.search_term.length>2){const o=t.search_term.toLowerCase();e=this.items.filter(function(){return n(this).text().toLowerCase().indexOf(o)>-1})}else e=o;t.items.hide(),e.show(),this.update_banner_position()},init_search:function(){const e=this;this.search_input.on("keyup change search",function(){const t=n(this).val();e.search_term=t.length<3?"":t,e.filter_items()})},show_connect_banner:function(){const e=n("#tmpl-wpcode-library-connect-banner");if(!e.length)return;const t=this.container.find(".wpcode-items-list-category .wpcode-list-item:visible");t.length>5?t.eq(5).after(e.html()):t.last().after(e.html()),this.banner=this.container.find("#wpcode-library-connect-banner")},update_banner_position:function(){const e=this.container.find(".wpcode-items-list-category .wpcode-list-item:visible");this.banner&&this.banner.length>0&&(e.length>5?this.banner.insertAfter(e.eq(5)):this.banner.insertAfter(e.last()))}},n(e).ready(function(){n(".wpcode-items-metabox").each(function(){new o(this)})})}(document,window,jQuery)},209:()=>{(window.WPCodeAdminNotices||function(e,t,n){const o={l10n:wpcode,init:function(){t.WPCodeAdminNotices=o,o.notice_holder=n(e.getElementById("wpcode-notice-area")),o.document=n(e)},add_notice(e,t="updated"){const n=o.get_notice(e,t);o.notice_holder.append(n),o.document.trigger("wp-updates-notice-added"),n.find("button").focus()},get_notice(e,t){const o=n("<div />"),i=n("<p />");return i.html(e),o.addClass("fade notice is-dismissible"),o.addClass(t),o.append(i),o},show_pro_notice(e,i,a,r){const s=a.startsWith("wpcode-"),c=WPCodeSVG.WPCodeIcon("lock",22,28,"0 0 22 28","iconId");n.confirm({title:c+e,content:i,boxWidth:"560px",theme:"modern upsell-box",onOpenBefore(){this.$btnc.after('<div class="wpcode_check"></div>'),o.l10n.purchased&&this.$btnc.after('<div class="wpcode-already-purchased"><a href=" '+o.l10n.upgrade_link+' ">'+o.l10n.purchased+"</a></div>"),o.l10n.wpcode_lite_users&&(this.$btnc.after('<div class="wpcode-discount-note"><strong> '+o.l10n.bonus+":</strong> "+o.l10n.wpcode_lite_users+"<span> "+o.l10n.price+" </span> "+o.l10n.regular_price+" </div>"),this.$body.find(".jconfirm-content").addClass("wpcode-lite-upgrade"))},buttons:{confirm:{text:r||o.l10n.upgrade_button,btnClass:"wpcode-btn-orange",action:function(){s?s&&WPCodeAddons&&n.confirm({title:o.l10n.please_wait,content:function(){this.showLoading(),WPCodeAddons.install_addon(n("<button></button>").data("addon",a))}}):t.open(a,"_blank","noopener noreferrer")}}},closeIcon:!0,backgroundDismiss:!0,useBootstrap:!1})}};return o}(document,window,jQuery)).init()},327:()=>{(window.WPCodeHeader||function(e,t,n){const o={init(){o.should_init()&&n(e).ready(function(){o.init_sticky_header()})},should_init:()=>n("#wpcode_snippet_code").length>0||n("#ihaf_insert_header").length>0,init_sticky_header(){const e=n(".wpcode-header-bottom"),o=e.height(),i=e.offset().top,a=e.parent();n(t).scroll(function(){const r=n(t).scrollTop();i<r+32?(e.addClass("wpcode-sticky"),a.css("paddingBottom",o+"px")):(e.removeClass("wpcode-sticky"),a.css("paddingBottom",0))})}};return o}(document,window,jQuery)).init()},332:()=>{(window.WPCodeAdminNotifications||function(e,t,n){const o={init(){o.should_init()&&(o.find_elements(),o.init_open(),o.init_close(),o.init_dismiss(),o.init_view_switch(),o.update_count(o.active_count))},should_init:()=>(o.$drawer=n("#wpcode-notifications-drawer"),o.$drawer.length>0),find_elements(){o.$open_button=n("#wpcode-notifications-button"),o.$count=o.$drawer.find("#wpcode-notifications-count"),o.$dismissed_count=o.$drawer.find("#wpcode-notifications-dismissed-count"),o.active_count=o.$open_button.data("count")?o.$open_button.data("count"):0,o.dismissed_count=o.$open_button.data("dismissed"),o.$body=n("body"),o.$dismissed_button=n("#wpcode-notifications-show-dismissed"),o.$active_button=n("#wpcode-notifications-show-active"),o.$active_list=n(".wpcode-notifications-list .wpcode-notifications-active"),o.$dismissed_list=n(".wpcode-notifications-list .wpcode-notifications-dismissed"),o.$dismiss_all=n("#wpcode-dismiss-all")},update_count(e){o.$open_button.data("count",e).attr("data-count",e),0===e&&o.$open_button.removeAttr("data-count"),o.$count.text(e),o.dismissed_count+=Math.abs(e-o.active_count),o.active_count=e,o.$dismissed_count.text(o.dismissed_count),0===o.active_count&&o.$dismiss_all.hide()},init_open(){o.$open_button.on("click",function(e){e.preventDefault(),o.$body.addClass("wpcode-notifications-open")})},init_close(){o.$body.on("click",".wpcode-notifications-close, .wpcode-notifications-overlay",function(e){e.preventDefault(),o.$body.removeClass("wpcode-notifications-open")})},init_dismiss(){o.$drawer.on("click",".wpcode-notification-dismiss",function(e){e.preventDefault();const t=n(this).data("id");if(o.dismiss_notification(t),"all"===t)return o.move_to_dismissed(o.$active_list.find("li")),void o.update_count(0);o.move_to_dismissed(n(this).closest("li")),o.update_count(o.active_count-1)})},move_to_dismissed(e){e.slideUp(function(){n(this).prependTo(o.$dismissed_list).show()})},dismiss_notification:e=>n.post(ajaxurl,{action:"wpcode_notification_dismiss",nonce:wpcode.nonce,id:e}),init_view_switch(){o.$dismissed_button.on("click",function(e){e.preventDefault(),o.$drawer.addClass("show-dismissed")}),o.$active_button.on("click",function(e){e.preventDefault(),o.$drawer.removeClass("show-dismissed")})}};return o}(document,window,jQuery)).init()},350:(e,t,n)=>{var o,i,a;i=[n(428)],void 0===(a="function"==typeof(o=function(e){var t=window;e.fn.confirm=function(n,o){return void 0===n&&(n={}),"string"==typeof n&&(n={content:n,title:o||!1}),e(this).each(function(){var o=e(this);o.attr("jc-attached")?console.warn("jConfirm has already been attached to this element ",o[0]):(o.on("click",function(i){i.preventDefault();var a=e.extend({},n);if(o.attr("data-title")&&(a.title=o.attr("data-title")),o.attr("data-content")&&(a.content=o.attr("data-content")),void 0===a.buttons&&(a.buttons={}),a.$target=o,o.attr("href")&&0===Object.keys(a.buttons).length){var r=e.extend(!0,{},t.jconfirm.pluginDefaults.defaultButtons,(t.jconfirm.defaults||{}).defaultButtons||{}),s=Object.keys(r)[0];a.buttons=r,a.buttons[s].action=function(){location.href=o.attr("href")}}a.closeIcon=!1,e.confirm(a)}),o.attr("jc-attached",!0))}),e(this)},e.confirm=function(n,o){void 0===n&&(n={}),"string"==typeof n&&(n={content:n,title:o||!1});var i=!(!1===n.buttons);if("object"!=typeof n.buttons&&(n.buttons={}),0===Object.keys(n.buttons).length&&i){var a=e.extend(!0,{},t.jconfirm.pluginDefaults.defaultButtons,(t.jconfirm.defaults||{}).defaultButtons||{});n.buttons=a}return t.jconfirm(n)},e.alert=function(n,o){void 0===n&&(n={}),"string"==typeof n&&(n={content:n,title:o||!1});var i=!(!1===n.buttons);if("object"!=typeof n.buttons&&(n.buttons={}),0===Object.keys(n.buttons).length&&i){var a=e.extend(!0,{},t.jconfirm.pluginDefaults.defaultButtons,(t.jconfirm.defaults||{}).defaultButtons||{}),r=Object.keys(a)[0];n.buttons[r]=a[r]}return t.jconfirm(n)},e.dialog=function(e,n){return void 0===e&&(e={}),"string"==typeof e&&(e={content:e,title:n||!1,closeIcon:function(){}}),e.buttons={},void 0===e.closeIcon&&(e.closeIcon=function(){}),e.confirmKeys=[13],t.jconfirm(e)},t.jconfirm=function(n){void 0===n&&(n={});var o=e.extend(!0,{},t.jconfirm.pluginDefaults);t.jconfirm.defaults&&(o=e.extend(!0,o,t.jconfirm.defaults)),o=e.extend(!0,{},o,n);var i=new t.Jconfirm(o);return t.jconfirm.instances.push(i),i},t.Jconfirm=function(t){e.extend(this,t),this._init()},t.Jconfirm.prototype={_init:function(){var n=this;t.jconfirm.instances.length||(t.jconfirm.lastFocused=e("body").find(":focus")),this._id=Math.round(99999*Math.random()),this.contentParsed=e(document.createElement("div")),this.lazyOpen||setTimeout(function(){n.open()},0)},_buildHTML:function(){var t=this;this._parseAnimation(this.animation,"o"),this._parseAnimation(this.closeAnimation,"c"),this._parseBgDismissAnimation(this.backgroundDismissAnimation),this._parseColumnClass(this.columnClass),this._parseTheme(this.theme),this._parseType(this.type);var n=e(this.template);n.find(".jconfirm-box").addClass(this.animationParsed).addClass(this.backgroundDismissAnimationParsed).addClass(this.typeParsed),this.typeAnimated&&n.find(".jconfirm-box").addClass("jconfirm-type-animated"),this.useBootstrap?(n.find(".jc-bs3-row").addClass(this.bootstrapClasses.row),n.find(".jc-bs3-row").addClass("justify-content-md-center justify-content-sm-center justify-content-xs-center justify-content-lg-center"),n.find(".jconfirm-box-container").addClass(this.columnClassParsed),this.containerFluid?n.find(".jc-bs3-container").addClass(this.bootstrapClasses.containerFluid):n.find(".jc-bs3-container").addClass(this.bootstrapClasses.container)):n.find(".jconfirm-box").css("width",this.boxWidth),this.titleClass&&n.find(".jconfirm-title-c").addClass(this.titleClass),n.addClass(this.themeParsed);var o="jconfirm-box"+this._id;n.find(".jconfirm-box").attr("aria-labelledby",o).attr("tabindex",-1),n.find(".jconfirm-content").attr("id",o),null!==this.bgOpacity&&n.find(".jconfirm-bg").css("opacity",this.bgOpacity),this.rtl&&n.addClass("jconfirm-rtl"),this.$el=n.appendTo(this.container),this.$jconfirmBoxContainer=this.$el.find(".jconfirm-box-container"),this.$jconfirmBox=this.$body=this.$el.find(".jconfirm-box"),this.$jconfirmBg=this.$el.find(".jconfirm-bg"),this.$title=this.$el.find(".jconfirm-title"),this.$titleContainer=this.$el.find(".jconfirm-title-c"),this.$content=this.$el.find("div.jconfirm-content"),this.$contentPane=this.$el.find(".jconfirm-content-pane"),this.$icon=this.$el.find(".jconfirm-icon-c"),this.$closeIcon=this.$el.find(".jconfirm-closeIcon"),this.$holder=this.$el.find(".jconfirm-holder"),this.$btnc=this.$el.find(".jconfirm-buttons"),this.$scrollPane=this.$el.find(".jconfirm-scrollpane"),t.setStartingPoint(),this._contentReady=e.Deferred(),this._modalReady=e.Deferred(),this.$holder.css({"padding-top":this.offsetTop,"padding-bottom":this.offsetBottom}),this.setTitle(),this.setIcon(),this._setButtons(),this._parseContent(),this.initDraggable(),this.isAjax&&this.showLoading(!1),e.when(this._contentReady,this._modalReady).then(function(){t.isAjaxLoading?setTimeout(function(){t.isAjaxLoading=!1,t.setContent(),t.setTitle(),t.setIcon(),setTimeout(function(){t.hideLoading(!1),t._updateContentMaxHeight()},100),"function"==typeof t.onContentReady&&t.onContentReady()},50):(t._updateContentMaxHeight(),t.setTitle(),t.setIcon(),"function"==typeof t.onContentReady&&t.onContentReady()),t.autoClose&&t._startCountDown()}).then(function(){t._watchContent()}),"none"===this.animation&&(this.animationSpeed=1,this.animationBounce=1),this.$body.css(this._getCSS(this.animationSpeed,this.animationBounce)),this.$contentPane.css(this._getCSS(this.animationSpeed,1)),this.$jconfirmBg.css(this._getCSS(this.animationSpeed,1)),this.$jconfirmBoxContainer.css(this._getCSS(this.animationSpeed,1))},_typePrefix:"jconfirm-type-",typeParsed:"",_parseType:function(e){this.typeParsed=this._typePrefix+e},setType:function(e){var t=this.typeParsed;this._parseType(e),this.$jconfirmBox.removeClass(t).addClass(this.typeParsed)},themeParsed:"",_themePrefix:"jconfirm-",setTheme:function(e){var t=this.theme;this.theme=e||this.theme,this._parseTheme(this.theme),t&&this.$el.removeClass(t),this.$el.addClass(this.themeParsed),this.theme=e},_parseTheme:function(t){var n=this;t=t.split(","),e.each(t,function(o,i){-1===i.indexOf(n._themePrefix)&&(t[o]=n._themePrefix+e.trim(i))}),this.themeParsed=t.join(" ").toLowerCase()},backgroundDismissAnimationParsed:"",_bgDismissPrefix:"jconfirm-hilight-",_parseBgDismissAnimation:function(t){var n=t.split(","),o=this;e.each(n,function(t,i){-1===i.indexOf(o._bgDismissPrefix)&&(n[t]=o._bgDismissPrefix+e.trim(i))}),this.backgroundDismissAnimationParsed=n.join(" ").toLowerCase()},animationParsed:"",closeAnimationParsed:"",_animationPrefix:"jconfirm-animation-",setAnimation:function(e){this.animation=e||this.animation,this._parseAnimation(this.animation,"o")},_parseAnimation:function(t,n){n=n||"o";var o=t.split(","),i=this;e.each(o,function(t,n){-1===n.indexOf(i._animationPrefix)&&(o[t]=i._animationPrefix+e.trim(n))});var a=o.join(" ").toLowerCase();return"o"===n?this.animationParsed=a:this.closeAnimationParsed=a,a},setCloseAnimation:function(e){this.closeAnimation=e||this.closeAnimation,this._parseAnimation(this.closeAnimation,"c")},setAnimationSpeed:function(e){this.animationSpeed=e||this.animationSpeed},columnClassParsed:"",setColumnClass:function(e){this.useBootstrap?(this.columnClass=e||this.columnClass,this._parseColumnClass(this.columnClass),this.$jconfirmBoxContainer.addClass(this.columnClassParsed)):console.warn("cannot set columnClass, useBootstrap is set to false")},_updateContentMaxHeight:function(){var t=e(window).height()-(this.$jconfirmBox.outerHeight()-this.$contentPane.outerHeight())-(this.offsetTop+this.offsetBottom);this.$contentPane.css({"max-height":t+"px"})},setBoxWidth:function(e){this.useBootstrap?console.warn("cannot set boxWidth, useBootstrap is set to true"):(this.boxWidth=e,this.$jconfirmBox.css("width",e))},_parseColumnClass:function(e){var t;switch(e=e.toLowerCase()){case"xl":case"xlarge":t="col-md-12";break;case"l":case"large":t="col-md-8 col-md-offset-2";break;case"m":case"medium":t="col-md-6 col-md-offset-3";break;case"s":case"small":t="col-md-4 col-md-offset-4";break;case"xs":case"xsmall":t="col-md-2 col-md-offset-5";break;default:t=e}this.columnClassParsed=t},initDraggable:function(){var t=this,n=this.$titleContainer;this.resetDrag(),this.draggable&&(n.on("mousedown",function(e){n.addClass("jconfirm-hand"),t.mouseX=e.clientX,t.mouseY=e.clientY,t.isDrag=!0}),e(window).on("mousemove."+this._id,function(e){t.isDrag&&(t.movingX=e.clientX-t.mouseX+t.initialX,t.movingY=e.clientY-t.mouseY+t.initialY,t.setDrag())}),e(window).on("mouseup."+this._id,function(){n.removeClass("jconfirm-hand"),t.isDrag&&(t.isDrag=!1,t.initialX=t.movingX,t.initialY=t.movingY)}))},resetDrag:function(){this.isDrag=!1,this.initialX=0,this.initialY=0,this.movingX=0,this.movingY=0,this.mouseX=0,this.mouseY=0,this.$jconfirmBoxContainer.css("transform","translate(0px, 0px)")},setDrag:function(){if(this.draggable){this.alignMiddle=!1;var t=this.$jconfirmBox.outerWidth(),n=this.$jconfirmBox.outerHeight(),o=e(window).width(),i=e(window).height(),a=this;if(a.movingX%1==0||a.movingY%1==0){if(a.dragWindowBorder){var r=o/2-t/2,s=i/2-n/2;s-=a.dragWindowGap,(r-=a.dragWindowGap)+a.movingX<0?a.movingX=-r:r-a.movingX<0&&(a.movingX=r),s+a.movingY<0?a.movingY=-s:s-a.movingY<0&&(a.movingY=s)}a.$jconfirmBoxContainer.css("transform","translate("+a.movingX+"px, "+a.movingY+"px)")}}},_scrollTop:function(){if("undefined"!=typeof pageYOffset)return pageYOffset;var e=document.body,t=document.documentElement;return(t=t.clientHeight?t:e).scrollTop},_watchContent:function(){var t=this;this._timer&&clearInterval(this._timer);var n=0;this._timer=setInterval(function(){if(t.smoothContent){var o=t.$content.outerHeight()||0;o!==n&&(n=o);var i=e(window).height();t.offsetTop+t.offsetBottom+t.$jconfirmBox.height()-t.$contentPane.height()+t.$content.height()<i?t.$contentPane.addClass("no-scroll"):t.$contentPane.removeClass("no-scroll")}},this.watchInterval)},_overflowClass:"jconfirm-overflow",_hilightAnimating:!1,highlight:function(){this.hiLightModal()},hiLightModal:function(){var e=this;if(!this._hilightAnimating){e.$body.addClass("hilight");var t=parseFloat(e.$body.css("animation-duration"))||2;this._hilightAnimating=!0,setTimeout(function(){e._hilightAnimating=!1,e.$body.removeClass("hilight")},1e3*t)}},_bindEvents:function(){var t=this;this.boxClicked=!1,this.$scrollPane.click(function(e){if(!t.boxClicked){var n,o=!1,i=!1;if("string"==typeof(n="function"==typeof t.backgroundDismiss?t.backgroundDismiss():t.backgroundDismiss)&&void 0!==t.buttons[n]?(o=n,i=!1):i=void 0===n||1==!!n,o){var a=t.buttons[o].action.apply(t);i=void 0===a||!!a}i?t.close():t.hiLightModal()}t.boxClicked=!1}),this.$jconfirmBox.click(function(e){t.boxClicked=!0});var n=!1;e(window).on("jcKeyDown."+t._id,function(e){n||(n=!0)}),e(window).on("keyup."+t._id,function(e){n&&(t.reactOnKey(e),n=!1)}),e(window).on("resize."+this._id,function(){t._updateContentMaxHeight(),setTimeout(function(){t.resetDrag()},100)})},_cubic_bezier:"0.36, 0.55, 0.19",_getCSS:function(e,t){return{"-webkit-transition-duration":e/1e3+"s","transition-duration":e/1e3+"s","-webkit-transition-timing-function":"cubic-bezier("+this._cubic_bezier+", "+t+")","transition-timing-function":"cubic-bezier("+this._cubic_bezier+", "+t+")"}},_setButtons:function(){var t=this,n=0;if("object"!=typeof this.buttons&&(this.buttons={}),e.each(this.buttons,function(o,i){n+=1,"function"==typeof i&&(t.buttons[o]=i={action:i}),t.buttons[o].text=i.text||o,t.buttons[o].btnClass=i.btnClass||"btn-default",t.buttons[o].action=i.action||function(){},t.buttons[o].keys=i.keys||[],t.buttons[o].isHidden=i.isHidden||!1,t.buttons[o].isDisabled=i.isDisabled||!1,e.each(t.buttons[o].keys,function(e,n){t.buttons[o].keys[e]=n.toLowerCase()});var a=e('<button type="button" class="btn"></button>').html(t.buttons[o].text).addClass(t.buttons[o].btnClass).prop("disabled",t.buttons[o].isDisabled).css("display",t.buttons[o].isHidden?"none":"").click(function(e){e.preventDefault();var n=t.buttons[o].action.apply(t,[t.buttons[o]]);t.onAction.apply(t,[o,t.buttons[o]]),t._stopCountDown(),(void 0===n||n)&&t.close()});t.buttons[o].el=a,t.buttons[o].setText=function(e){a.html(e)},t.buttons[o].addClass=function(e){a.addClass(e)},t.buttons[o].removeClass=function(e){a.removeClass(e)},t.buttons[o].disable=function(){t.buttons[o].isDisabled=!0,a.prop("disabled",!0)},t.buttons[o].enable=function(){t.buttons[o].isDisabled=!1,a.prop("disabled",!1)},t.buttons[o].show=function(){t.buttons[o].isHidden=!1,a.css("display","")},t.buttons[o].hide=function(){t.buttons[o].isHidden=!0,a.css("display","none")},t["$_"+o]=t["$$"+o]=a,t.$btnc.append(a)}),0===n&&this.$btnc.hide(),null===this.closeIcon&&0===n&&(this.closeIcon=!0),this.closeIcon){if(this.closeIconClass){var o='<i class="'+this.closeIconClass+'"></i>';this.$closeIcon.html(o)}this.$closeIcon.click(function(e){e.preventDefault();var n,o=!1,i=!1;if("string"==typeof(n="function"==typeof t.closeIcon?t.closeIcon():t.closeIcon)&&void 0!==t.buttons[n]?(o=n,i=!1):i=void 0===n||1==!!n,o){var a=t.buttons[o].action.apply(t);i=void 0===a||!!a}i&&t.close()}),this.$closeIcon.show()}else this.$closeIcon.hide()},setTitle:function(e,t){if(t=t||!1,void 0!==e)if("string"==typeof e)this.title=e;else if("function"==typeof e){"function"==typeof e.promise&&console.error("Promise was returned from title function, this is not supported.");var n=e();this.title="string"==typeof n&&n}else this.title=!1;this.isAjaxLoading&&!t||(this.$title.html(this.title||""),this.updateTitleContainer())},setIcon:function(e,t){if(t=t||!1,void 0!==e)if("string"==typeof e)this.icon=e;else if("function"==typeof e){var n=e();this.icon="string"==typeof n&&n}else this.icon=!1;this.isAjaxLoading&&!t||(this.$icon.html(this.icon?'<i class="'+this.icon+'"></i>':""),this.updateTitleContainer())},updateTitleContainer:function(){this.title||this.icon?this.$titleContainer.show():this.$titleContainer.hide()},setContentPrepend:function(e,t){e&&this.contentParsed.prepend(e)},setContentAppend:function(e){e&&this.contentParsed.append(e)},setContent:function(e,t){t=!!t;var n=this;e&&this.contentParsed.html("").append(e),this.isAjaxLoading&&!t||(this.$content.html(""),this.$content.append(this.contentParsed),setTimeout(function(){n.$body.find("input[autofocus]:visible:first").focus()},100))},loadingSpinner:!1,showLoading:function(e){this.loadingSpinner=!0,this.$jconfirmBox.addClass("loading"),e&&this.$btnc.find("button").prop("disabled",!0)},hideLoading:function(e){this.loadingSpinner=!1,this.$jconfirmBox.removeClass("loading"),e&&this.$btnc.find("button").prop("disabled",!1)},ajaxResponse:!1,contentParsed:"",isAjax:!1,isAjaxLoading:!1,_parseContent:function(){var t=this,n="&nbsp;";if("function"==typeof this.content){var o=this.content.apply(this);"string"==typeof o?this.content=o:"object"==typeof o&&"function"==typeof o.always?(this.isAjax=!0,this.isAjaxLoading=!0,o.always(function(e,n,o){t.ajaxResponse={data:e,status:n,xhr:o},t._contentReady.resolve(e,n,o),"function"==typeof t.contentLoaded&&t.contentLoaded(e,n,o)}),this.content=n):this.content=n}if("string"==typeof this.content&&"url:"===this.content.substr(0,4).toLowerCase()){this.isAjax=!0,this.isAjaxLoading=!0;var i=this.content.substring(4,this.content.length);e.get(i).done(function(e){t.contentParsed.html(e)}).always(function(e,n,o){t.ajaxResponse={data:e,status:n,xhr:o},t._contentReady.resolve(e,n,o),"function"==typeof t.contentLoaded&&t.contentLoaded(e,n,o)})}this.content||(this.content=n),this.isAjax||(this.contentParsed.html(this.content),this.setContent(),t._contentReady.resolve())},_stopCountDown:function(){clearInterval(this.autoCloseInterval),this.$cd&&this.$cd.remove()},_startCountDown:function(){var t=this,n=this.autoClose.split("|");if(2!==n.length)return console.error("Invalid option for autoClose. example 'close|10000'"),!1;var o=n[0],i=parseInt(n[1]);if(void 0===this.buttons[o])return console.error("Invalid button key '"+o+"' for autoClose"),!1;var a=Math.ceil(i/1e3);this.$cd=e('<span class="countdown"> ('+a+")</span>").appendTo(this["$_"+o]),this.autoCloseInterval=setInterval(function(){t.$cd.html(" ("+(a-=1)+") "),a<=0&&(t["$$"+o].trigger("click"),t._stopCountDown())},1e3)},_getKey:function(e){switch(e){case 192:return"tilde";case 13:return"enter";case 16:return"shift";case 9:return"tab";case 20:return"capslock";case 17:return"ctrl";case 91:return"win";case 18:return"alt";case 27:return"esc";case 32:return"space"}var t=String.fromCharCode(e);return!!/^[A-z0-9]+$/.test(t)&&t.toLowerCase()},reactOnKey:function(t){var n=this,o=e(".jconfirm");if(o.eq(o.length-1)[0]!==this.$el[0])return!1;var i=t.which;if(this.$content.find(":input").is(":focus")&&/13|32/.test(i))return!1;var a,r=this._getKey(i);"esc"===r&&this.escapeKey&&(!0===this.escapeKey?this.$scrollPane.trigger("click"):"string"!=typeof this.escapeKey&&"function"!=typeof this.escapeKey||(a="function"==typeof this.escapeKey?this.escapeKey():this.escapeKey)&&(void 0===this.buttons[a]?console.warn("Invalid escapeKey, no buttons found with key "+a):this["$_"+a].trigger("click"))),e.each(this.buttons,function(e,t){-1!==t.keys.indexOf(r)&&n["$_"+e].trigger("click")})},setDialogCenter:function(){console.info("setDialogCenter is deprecated, dialogs are centered with CSS3 tables")},_unwatchContent:function(){clearInterval(this._timer)},close:function(n){var o=this;return"function"==typeof this.onClose&&this.onClose(n),this._unwatchContent(),e(window).unbind("resize."+this._id),e(window).unbind("keyup."+this._id),e(window).unbind("jcKeyDown."+this._id),this.draggable&&(e(window).unbind("mousemove."+this._id),e(window).unbind("mouseup."+this._id),this.$titleContainer.unbind("mousedown")),o.$el.removeClass(o.loadedClass),e("body").removeClass("jconfirm-no-scroll-"+o._id),o.$jconfirmBoxContainer.removeClass("jconfirm-no-transition"),setTimeout(function(){o.$body.addClass(o.closeAnimationParsed),o.$jconfirmBg.addClass("jconfirm-bg-h");var n="none"===o.closeAnimation?1:o.animationSpeed;setTimeout(function(){o.$el.remove(),t.jconfirm.instances;for(var n=t.jconfirm.instances.length-1;n>=0;n--)t.jconfirm.instances[n]._id===o._id&&t.jconfirm.instances.splice(n,1);if(!t.jconfirm.instances.length&&o.scrollToPreviousElement&&t.jconfirm.lastFocused&&t.jconfirm.lastFocused.length&&e.contains(document,t.jconfirm.lastFocused[0])){var i=t.jconfirm.lastFocused;if(o.scrollToPreviousElementAnimate){var a=e(window).scrollTop(),r=t.jconfirm.lastFocused.offset().top,s=e(window).height();if(r>a&&r<a+s)i.focus();else{var c=r-Math.round(s/3);e("html, body").animate({scrollTop:c},o.animationSpeed,"swing",function(){i.focus()})}}else i.focus();t.jconfirm.lastFocused=!1}"function"==typeof o.onDestroy&&o.onDestroy()},.4*n)},50),!0},open:function(){return!this.isOpen()&&(this._buildHTML(),this._bindEvents(),this._open(),!0)},setStartingPoint:function(){var n=!1;if(!0!==this.animateFromElement&&this.animateFromElement)n=this.animateFromElement,t.jconfirm.lastClicked=!1;else{if(!t.jconfirm.lastClicked||!0!==this.animateFromElement)return!1;n=t.jconfirm.lastClicked,t.jconfirm.lastClicked=!1}if(!n)return!1;var o=n.offset(),i=n.outerHeight()/2,a=n.outerWidth()/2;i-=this.$jconfirmBox.outerHeight()/2,a-=this.$jconfirmBox.outerWidth()/2;var r=o.top+i;r-=this._scrollTop();var s=o.left+a,c=e(window).height()/2,l=e(window).width()/2;if(r-=c-this.$jconfirmBox.outerHeight()/2,s-=l-this.$jconfirmBox.outerWidth()/2,Math.abs(r)>c||Math.abs(s)>l)return!1;this.$jconfirmBoxContainer.css("transform","translate("+s+"px, "+r+"px)")},_open:function(){var e=this;"function"==typeof e.onOpenBefore&&e.onOpenBefore(),this.$body.removeClass(this.animationParsed),this.$jconfirmBg.removeClass("jconfirm-bg-h"),this.$body.focus(),e.$jconfirmBoxContainer.css("transform","translate(0px, 0px)"),setTimeout(function(){e.$body.css(e._getCSS(e.animationSpeed,1)),e.$body.css({"transition-property":e.$body.css("transition-property")+", margin"}),e.$jconfirmBoxContainer.addClass("jconfirm-no-transition"),e._modalReady.resolve(),"function"==typeof e.onOpen&&e.onOpen(),e.$el.addClass(e.loadedClass)},this.animationSpeed)},loadedClass:"jconfirm-open",isClosed:function(){return!this.$el||0===this.$el.parent().length},isOpen:function(){return!this.isClosed()},toggle:function(){this.isOpen()?this.close():this.open()}},t.jconfirm.instances=[],t.jconfirm.lastFocused=!1,t.jconfirm.pluginDefaults={template:'<div class="jconfirm"><div class="jconfirm-bg jconfirm-bg-h"></div><div class="jconfirm-scrollpane"><div class="jconfirm-row"><div class="jconfirm-cell"><div class="jconfirm-holder"><div class="jc-bs3-container"><div class="jc-bs3-row"><div class="jconfirm-box-container jconfirm-animated"><div class="jconfirm-box" role="dialog" aria-labelledby="labelled" tabindex="-1"><div class="jconfirm-closeIcon">&times;</div><div class="jconfirm-title-c"><span class="jconfirm-icon-c"></span><span class="jconfirm-title"></span></div><div class="jconfirm-content-pane"><div class="jconfirm-content"></div></div><div class="jconfirm-buttons"></div><div class="jconfirm-clear"></div></div></div></div></div></div></div></div></div></div>',title:"Hello",titleClass:"",type:"default",typeAnimated:!0,draggable:!0,dragWindowGap:15,dragWindowBorder:!0,animateFromElement:!0,alignMiddle:!0,smoothContent:!0,content:"Are you sure to continue?",buttons:{},defaultButtons:{ok:{action:function(){}},close:{action:function(){}}},contentLoaded:function(){},icon:"",lazyOpen:!1,bgOpacity:null,theme:"light",animation:"scale",closeAnimation:"scale",animationSpeed:400,animationBounce:1,escapeKey:!0,rtl:!1,container:"body",containerFluid:!1,backgroundDismiss:!1,backgroundDismissAnimation:"shake",autoClose:!1,closeIcon:null,closeIconClass:!1,watchInterval:100,columnClass:"col-md-4 col-md-offset-4 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",boxWidth:"50%",scrollToPreviousElement:!0,scrollToPreviousElementAnimate:!0,useBootstrap:!0,offsetTop:40,offsetBottom:40,bootstrapClasses:{container:"container",containerFluid:"container-fluid",row:"row"},onContentReady:function(){},onOpenBefore:function(){},onOpen:function(){},onClose:function(){},onDestroy:function(){},onAction:function(){}};var n=!1;e(window).on("keydown",function(t){if(!n){var o=!1;e(t.target).closest(".jconfirm-box").length&&(o=!0),o&&e(window).trigger("jcKeyDown"),n=!0}}),e(window).on("keyup",function(){n=!1}),t.jconfirm.lastClicked=!1,e(document).on("mousedown","button, a, [jc-source]",function(){t.jconfirm.lastClicked=e(this)})})?o.apply(t,i):o)||(e.exports=a)},405:()=>{(window.WPCodeHelp||function(e,t,n){const o={init:function(){o.should_init()&&(o.find_elements(),o.init_show(),o.init_close_button(),o.init_search(),o.init_accordion())},should_init:()=>(o.$overlay=n("#wpcode-docs-overlay"),o.$overlay.length>0),find_elements(){o.$close_button=n("#wpcode-help-close"),o.$search=n("#wpcode-help-search"),o.$no_result=n("#wpcode-help-no-result"),o.$search_results=n("#wpcode-help-result ul"),o.$categories=n("#wpcode-help-categories")},init_close_button(){o.$close_button.on("click",function(e){e.preventDefault(),o.$overlay.fadeOut(200)})},init_show(){n(e).on("click",".wpcode-show-help",function(e){e.preventDefault(),o.$overlay.fadeIn(200)})},init_accordion(){o.$categories.on("click",".wpcode-help-category header",function(){const e=n(this).closest(".wpcode-help-category");o.toggle_category(e)}),o.$categories.on("click",".viewall",function(e){e.preventDefault(),n(this).closest(".wpcode-help-docs").find("div").slideDown(),n(this).hide()})},toggle_category(e){e.toggleClass("open"),e.find(".wpcode-help-docs").slideToggle()},init_search(){o.$search.on("keyup","input",o.input_search),o.$search.on("click","#wpcode-help-search-clear",o.clear_search)},input_search(){o.$search_results.html("");const e=n(this).val().toLowerCase(),t=n("#wpcode-help-categories .wpcode-help-docs li").filter(function(){return n(this).text().toLowerCase().indexOf(""+e)>-1});e.length>2&&t.clone().appendTo(o.$search_results),0===t.length?o.$no_result.show():o.$no_result.hide(),o.$search.toggleClass("wpcode-search-empty",!e)},clear_search(){o.$search.find("input").val("").trigger("keyup")}};return o}(document,window,jQuery)).init()},428:e=>{"use strict";e.exports=window.jQuery},429:()=>{(window.WPCodeAdminGenerator||function(e,t,n){const o={doing_ajax_call:!1,ajax_snippet_update:!1,editor_id:"wpcode_generator_code_preview",init:function(){o.should_init()&&(o.find_elements(),o.init_generator_form(),o.init_code_editor(),o.init_tabs(),o.init_use_snippet(),o.init_copy_editor(),o.init_repeater(),o.do_spacer(),n(e).ready(function(){o.init_autocomplete()}))},should_init:()=>(o.generator_form=n("#wpcode_generator_form"),o.generator_form.length>0),find_elements(){o.tabs_buttons=n(".wpcode-items-tabs"),o.tabs_content=n(".wpcode-items-list .wpcode-form-tab"),o.use_snippet=n("#wpcode-generator-use-snippet"),o.update_button=n("#wpcode-generator-update-code"),o.repeater_row=n("#wpcode-generator-repeater-row").html()},init_generator_form(){o.generator_form.on("submit",function(e){e.preventDefault(),o.update_snippet()}),o.generator_form.on("change","input, select",function(){o.update_snippet()}),o.update_snippet()},update_snippet(){o.doing_ajax_call||(o.ajax_snippet_update&&o.ajax_snippet_update.abort(),o.show_button_spinner(o.update_button),o.ajax_snippet_update=n.post(ajaxurl,n(o.generator_form).serialize()).done(function(e){o.ajax_snippet_update=!1,WPCodeAdminCodeEditor.set_value(o.editor_id,e),o.hide_button_spinner(o.update_button)}))},init_tabs(){o.tabs_buttons.on("click","button",function(e){e.preventDefault(),o.switch_active_tab(n(this))})},switch_active_tab(e){o.tabs_buttons.find("button").removeClass("wpcode-active"),e.addClass("wpcode-active");const t=e.data("category");o.tabs_content.hide(),o.tabs_content.filter(function(){return n(this).data("tab")===t}).show(),o.do_spacer(),WPCodeAdminCodeEditor.refresh(o.editor_id)},init_use_snippet(){o.use_snippet.on("click",function(e){if(e.preventDefault(),o.doing_ajax_call)return;o.doing_ajax_call=!0;const i=o.generator_form.serializeArray(),a=n(this);n.each(i,function(e,t){"action"===t.name&&(i[e].value="wpcode_save_generated_snippet")}),o.show_button_spinner(a),n.post(ajaxurl,n.param(i)).done(function(e){o.doing_ajax_call=!1,o.hide_button_spinner(a),e.success&&e.data.url&&(t.location=e.data.url)})})},show_button_spinner(e){t.WPCodeSpinner.show_button_spinner(e)},hide_button_spinner(e){t.WPCodeSpinner.hide_button_spinner(e)},init_copy_editor:function(){n(".wpcode-copy-target").on("click",function(e){e.preventDefault();const t=n(this),i=WPCodeAdminCodeEditor.get_value(o.editor_id);i&&(navigator.clipboard.writeText(i),t.addClass("wpcode-show-success-icon"),setTimeout(function(){t.removeClass("wpcode-show-success-icon")},500))})},init_repeater(){o.row_id=0,o.tabs_content.on("click",".wpcode-repeater-button",function(){const e=n(this).data("target"),t=n(n('.wpcode-generator-column > [data-repeater="'+e+'"]').get().reverse());let i,a,r=0;o.row_id++,t.each(function(){const e=n(this).closest(".wpcode-generator-column");e.is(i)||(r++,i=e,a=n(o.repeater_row),r>1?a.find("button").remove():a.find("button").data("target",o.row_id),a.attr("data-id",o.row_id),e.append(a)),n(this).clone().attr("data-repeater",null).prependTo(a).find("input").val(null)});let s=0,c=n('.wpcode-repeater-group[data-id="'+o.row_id+'"]');c.each(function(){const e=n(this).height();e>s&&(s=e)}),c.height(s),c.first().find("input").first().focus()}),o.tabs_content.on("click",".wpcode-remove-row",function(){const e=n(this).data("target");n('.wpcode-repeater-group[data-id="'+e+'"]').remove()})},do_spacer(){n(".wpcode-generator-field-spacer").each(function(){const e=n(this).closest(".wpcode-generator-column"),t=n(this).closest(".wpcode-generator-column").outerHeight();let o=0;e.siblings(".wpcode-generator-column").each(function(){const e=n(this).height();e>o&&(o=e)}),o>t&&n(this).height(o-t+3)})},init_autocomplete(){n(".wpcode-generator-field-autocomplete").each(function(){const e=n(this).find('input[type="text"]'),t=n(this).find(".wpcode-field-autocomplete").text();e.autocomplete({source:JSON.parse(t)})})},init_code_editor(){const e=n(".wpcode-generator-code");if(0===e.length)return;const t=wp.codeEditor.initialize(e);o.CodeMirror=t.codemirror,o.CodeMirror.setOption("readOnly",!1),o.CodeMirror.on("change",function(e){clearTimeout(o.editor_change_handler),o.editor_change_handler=setTimeout(function(){jQuery(e.getTextArea()).val(e.getValue()).change(),o.update_snippet()},300)})}};return o}(document,window,jQuery)).init()},474:()=>{(window.WPCodeSVG||function(e,t){const n={l18n:wpcode,init(){t.WPCodeSVG=n},sanitizeSvg(e,t){const n=(new DOMParser).parseFromString(e,"image/svg+xml");return n.querySelector("parsererror")?(console.error("Error parsing SVG:",n.querySelector("parsererror").textContent),""):(n.querySelectorAll("*").forEach(e=>{const n=e.tagName.toLowerCase();if(t[n])for(let o of[...e.attributes])t[n][o.name.toLowerCase()]||e.removeAttribute(o.name);else e.parentNode.removeChild(e)}),"svg"===n.documentElement.nodeName?n.documentElement.outerHTML:"")},WPCodeIcon(e,t=20,o=20,i="",a="",r=""){const s=n.wpcodeIcons();if(!s.hasOwnProperty(e))return"";i||(i=`0 0 ${t} ${o}`);const c=`<svg class = "wpcode-icon wpcode-icon-${e} ${r}" width = "${t}" height = "${o}" viewBox = "${i}" fill = "none" xmlns = "http://www.w3.org/2000/svg" id = "${a}" > ${s[e]} </svg> `;return this.sanitizeSvg(c,{svg:{class:!0,"aria-hidden":!0,"aria-labelledby":!0,role:!0,xmlns:!0,width:!0,height:!0,viewbox:!0,id:!0},g:{fill:!0,"clip-path":!0},title:{title:!0},path:{d:!0,fill:!0,"fill-rule":!0,"clip-rule":!0,"data-name":!0},circle:{cx:!0,cy:!0,r:!0,stroke:!0,"stroke-width":!0,fill:!0},rect:{x:!0,y:!0,width:!0,height:!0,fill:!0},polyline:{points:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-miterlimit":!0},clipPath:{id:!0},defs:{}})},wpcodeIcons:()=>({logo:'<path fill-rule="evenodd" clip-rule="evenodd" d="M57.5706 64H6.56732C2.89985 64 0 61.1064 0 57.4468V6.55319C0 2.89362 2.89985 0 6.56732 0H57.5706C61.2381 0 64.1379 2.89362 64.1379 6.55319V57.4468C64.1379 61.1064 61.2381 64 57.5706 64ZM15.863 52.0855C15.5219 52.0855 15.0954 52.0004 14.7543 51.9153C13.2191 51.3196 12.4515 49.6175 13.0485 48.0855L26.439 13.7877C27.036 12.2558 28.7418 11.4898 30.277 12.0855C31.8122 12.6813 32.5798 14.3834 31.9828 15.9153L18.6776 50.2132C18.2512 51.4047 17.0571 52.0855 15.863 52.0855ZM35.0534 47.7445C35.6504 48.3403 36.418 48.5956 37.1856 48.5956C37.9532 48.5956 38.7208 48.3403 39.3179 47.7445L49.8085 37.3616C51.6849 35.4892 51.6849 32.3403 49.8085 30.468L39.3179 19.9999C38.2091 18.8084 36.3327 18.8084 35.1386 19.9999C33.9446 21.1063 33.9446 22.9786 35.1386 24.1701L44.7764 33.8722L35.0534 43.5743C33.8593 44.6807 33.8593 46.5531 35.0534 47.7445Z" fill="white"/>',checkmark:'<circle class="path circle" fill="none" stroke="#73AF55" stroke-width="6" stroke-miterlimit="10" cx="65.1" cy="65.1" r="62.1"/><polyline class="path check" fill="none" stroke="#73AF55" stroke-width="6" stroke-linecap="round" stroke-miterlimit="10" points="100.2,40.2 51.5,88.8 29.8,67.5 "/>',lock:'<path d="M19 9.33333H17.6666V6.66667C17.6666 2.98667 14.68 0 11 0C7.31998 0 4.33331 2.98667 4.33331 6.66667V9.33333H2.99998C1.53331 9.33333 0.333313 10.5333 0.333313 12V25.3333C0.333313 26.8 1.53331 28 2.99998 28H19C20.4666 28 21.6666 26.8 21.6666 25.3333V12C21.6666 10.5333 20.4666 9.33333 19 9.33333ZM6.99998 6.66667C6.99998 4.45333 8.78665 2.66667 11 2.66667C13.2133 2.66667 15 4.45333 15 6.66667V9.33333H6.99998V6.66667ZM19 25.3333H2.99998V12H19V25.3333ZM11 21.3333C12.4666 21.3333 13.6666 20.1333 13.6666 18.6667C13.6666 17.2 12.4666 16 11 16C9.53331 16 8.33331 17.2 8.33331 18.6667C8.33331 20.1333 9.53331 21.3333 11 21.3333Z" fill="#8A8A8A"/>'})};return n}(document,window,jQuery)).init()},481:()=>{(window.WPCodeSchemaGenerator||function(e,t,n){const o={l18n:t.wpcode,init(){o.bindEvents(),console.log("Schema Generator with Smart Tags initialized!")},bindEvents(){n(e).on("click",".wpcode-media-library-button",o.openMediaLibrary),n(e).on("focus",".wpcode-input-text, .wpcode-input-textarea",function(){o.currentInputField=this}),n(e).on("click",".wpcode-insert-smart-tag",function(e){const t=n(this).closest(".wpcode-smart-tags").find(".wpcode-smart-tags-dropdown"),o=t.data("upgrade-title");if(o)return e.preventDefault(),e.stopImmediatePropagation(),WPCodeAdminNotices.show_pro_notice(o,t.data("upgrade-text"),t.data("upgrade-link"),t.data("upgrade-button")),!1})},openMediaLibrary(e){e.preventDefault();const t=n(this),i=n("#"+t.data("target")),a=wp.media({title:o.l18n.select_image||"Select Image",button:{text:o.l18n.use_image||"Use Image"},multiple:!1});a.on("select",function(){const e=a.state().get("selection").first().toJSON();i.val(e.url).trigger("change")}),a.open()},insertAtCursor(e,t){if(e.selectionStart||0===e.selectionStart){const n=e.selectionStart,o=e.selectionEnd;e.value=e.value.substring(0,n)+t+e.value.substring(o,e.value.length),e.selectionStart=n+t.length,e.selectionEnd=n+t.length}else e.value+=t;n(e).trigger("change"),e.focus()}};return o}(document,window,jQuery)).init()},512:()=>{(window.WPCodeAdminWelcome||function(e,t,n){const o={init:function(){o.add_listener()},add_listener(){n("#wpbody-content").on("click",".wpcode-scroll-to",function(e){e.preventDefault();const t=n(this).attr("href"),o=n(t);n("html, body").animate({scrollTop:o.offset().top},700)})}};return o}(document,window,jQuery)).init()},596:function(e,t,n){var o,i;!function(a){o=[n(428)],i=function(e){return function(e,t){"use strict";var n=e.document,o=t(e),i=t.Deferred,a=t("html"),r=[],s="aria-hidden",c="lity-"+s,l='a[href],area[href],input:not([disabled]),select:not([disabled]),textarea:not([disabled]),button:not([disabled]),iframe,object,embed,[contenteditable],[tabindex]:not([tabindex^="-"])',d={esc:!0,handler:null,handlers:{image:C,inline:function(e,n){var o,i,a;try{o=t(e)}catch(e){return!1}return!!o.length&&(i=t('<i style="display:none !important"></i>'),a=o.hasClass("lity-hide"),n.element().one("lity:remove",function(){i.before(o).remove(),a&&!o.closest(".lity-content").length&&o.addClass("lity-hide")}),o.removeClass("lity-hide").after(i))},youtube:function(e){var n=p.exec(e);return!!n&&x(b(e,y("https://www.youtube"+(n[2]||"")+".com/embed/"+n[4],t.extend({autoplay:1},w(n[5]||"")))))},vimeo:function(e){var n=h.exec(e);return!!n&&x(b(e,y("https://player.vimeo.com/video/"+n[3],t.extend({autoplay:1},w(n[4]||"")))))},googlemaps:function(e){var t=f.exec(e);return!!t&&x(b(e,y("https://www.google."+t[3]+"/maps?"+t[6],{output:t[6].indexOf("layer=c")>0?"svembed":"embed"})))},facebookvideo:function(e){var n=m.exec(e);return!!n&&(0!==e.indexOf("http")&&(e="https:"+e),x(b(e,y("https://www.facebook.com/plugins/video.php?href="+e,t.extend({autoplay:1},w(n[4]||""))))))},iframe:x},template:'<div class="lity" role="dialog" aria-label="Dialog Window (Press escape to close)" tabindex="-1"><div class="lity-wrap" data-lity-close role="document"><div class="lity-loader" aria-hidden="true">Loading...</div><div class="lity-container"><div class="lity-content"></div><button class="lity-close" type="button" aria-label="Close (Press escape to close)" data-lity-close>&times;</button></div></div></div>'},u=/(^data:image\/)|(\.(png|jpe?g|gif|svg|webp|bmp|ico|tiff?)(\?\S*)?$)/i,p=/(youtube(-nocookie)?\.com|youtu\.be)\/(watch\?v=|v\/|u\/|embed\/?)?([\w-]{11})(.*)?/i,h=/(vimeo(pro)?.com)\/(?:[^\d]+)?(\d+)\??(.*)?$/,f=/((maps|www)\.)?google\.([^\/\?]+)\/?((maps\/?)?\?)(.*)/i,m=/(facebook\.com)\/([a-z0-9_-]*)\/videos\/([0-9]*)(.*)?$/i,g=function(){var e=n.createElement("div"),t={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};for(var o in t)if(void 0!==e.style[o])return t[o];return!1}();function _(e){var t=i();return g&&e.length?(e.one(g,t.resolve),setTimeout(t.resolve,500)):t.resolve(),t.promise()}function v(e,n,o){if(1===arguments.length)return t.extend({},e);if("string"==typeof n){if(void 0===o)return void 0===e[n]?null:e[n];e[n]=o}else t.extend(e,n);return this}function w(e){for(var t,n=decodeURI(e.split("#")[0]).split("&"),o={},i=0,a=n.length;i<a;i++)n[i]&&(o[(t=n[i].split("="))[0]]=t[1]);return o}function y(e,n){return e+(e.indexOf("?")>-1?"&":"?")+t.param(n)}function b(e,t){var n=e.indexOf("#");return-1===n?t:(n>0&&(e=e.substr(n)),t+e)}function C(e,n){var o=n.opener()&&n.opener().data("lity-desc")||"Image with no description",a=t('<img src="'+e+'" alt="'+o+'"/>'),r=i(),s=function(){var e;r.reject((e="Failed loading image",t('<span class="lity-error"></span>').append(e)))};return a.on("load",function(){if(0===this.naturalWidth)return s();r.resolve(a)}).on("error",s),r.promise()}function x(e){return'<div class="lity-iframe-container"><iframe frameborder="0" allowfullscreen allow="autoplay; fullscreen" src="'+e+'"/></div>'}function D(){return n.documentElement.clientHeight?n.documentElement.clientHeight:Math.round(o.height())}function k(e){var t=A();t&&(27===e.keyCode&&t.options("esc")&&t.close(),9===e.keyCode&&function(e,t){var o=t.element().find(l),i=o.index(n.activeElement);e.shiftKey&&i<=0?(o.get(o.length-1).focus(),e.preventDefault()):e.shiftKey||i!==o.length-1||(o.get(0).focus(),e.preventDefault())}(e,t))}function $(){t.each(r,function(e,t){t.resize()})}function A(){return 0===r.length?null:r[0]}function j(e,l,u,p){var h,f,m,g,w=this,y=!1,b=!1;l=t.extend({},d,l),f=t(l.template),w.element=function(){return f},w.opener=function(){return u},w.options=t.proxy(v,w,l),w.handlers=t.proxy(v,w,l.handlers),w.resize=function(){y&&!b&&m.css("max-height",D()+"px").trigger("lity:resize",[w])},w.close=function(){if(y&&!b){b=!0,(l=w).element().attr(s,"true"),1===r.length&&(a.removeClass("lity-active"),o.off({resize:$,keydown:k})),((r=t.grep(r,function(e){return l!==e})).length?r[0].element():t(".lity-hidden")).removeClass("lity-hidden").each(function(){var e=t(this),n=e.data(c);n?e.attr(s,n):e.removeAttr(s),e.removeData(c)});var e=i();if(p&&(n.activeElement===f[0]||t.contains(f[0],n.activeElement)))try{p.focus()}catch(e){}return m.trigger("lity:close",[w]),f.removeClass("lity-opened").addClass("lity-closed"),_(m.add(f)).always(function(){m.trigger("lity:remove",[w]),f.remove(),f=void 0,e.resolve()}),e.promise()}var l},h=function(e,n,o,i){var a,r="inline",s=t.extend({},o);return i&&s[i]?(a=s[i](e,n),r=i):(t.each(["inline","iframe"],function(e,t){delete s[t],s[t]=o[t]}),t.each(s,function(t,o){return!o||!(!o.test||o.test(e,n))||(!1!==(a=o(e,n))?(r=t,!1):void 0)})),{handler:r,content:a||""}}(e,w,l.handlers,l.handler),f.attr(s,"false").addClass("lity-loading lity-opened lity-"+h.handler).appendTo("body").focus().on("click","[data-lity-close]",function(e){t(e.target).is("[data-lity-close]")&&w.close()}).trigger("lity:open",[w]),g=w,1===r.unshift(g)&&(a.addClass("lity-active"),o.on({resize:$,keydown:k})),t("body > *").not(g.element()).addClass("lity-hidden").each(function(){var e=t(this);void 0===e.data(c)&&e.data(c,e.attr(s)||null)}).attr(s,"true"),t.when(h.content).always(function(e){m=t(e).css("max-height",D()+"px"),f.find(".lity-loader").each(function(){var e=t(this);_(e).always(function(){e.remove()})}),f.removeClass("lity-loading").find(".lity-content").empty().append(m),y=!0,m.trigger("lity:ready",[w])})}function M(e,o,i){e.preventDefault?(e.preventDefault(),e=(i=t(this)).data("lity-target")||i.attr("href")||i.attr("src")):i=t(i);var a=new j(e,t.extend({},i.data("lity-options")||i.data("lity"),o),i,n.activeElement);if(!e.preventDefault)return a}return C.test=function(e){return u.test(e)},M.version="2.4.1",M.options=t.proxy(v,M,d),M.handlers=t.proxy(v,M,d.handlers),M.current=A,t(n).on("click.lity","[data-lity]",M),M}(a,e)}.apply(t,o),void 0===i||(e.exports=i)}("undefined"!=typeof window?window:this)},609:()=>{(window.WPCodeSpinner||function(e,t,n){const o={init(){t.WPCodeSpinner=o,o.spinner=n("#wpcode-admin-spinner")},show_button_spinner(e,t="right"){e.prop("disabled",!0);const i=e.offset(),a=n("#adminmenuwrap").width(),r=n("#wpadminbar").height();let s={};o.spinner.show(),s="right"===t?{left:i.left-a+e.outerWidth(),top:i.top-r+e.outerHeight()/2-o.spinner.height()/2}:{left:i.left-a-o.spinner.outerWidth()-20,top:i.top-r+e.outerHeight()/2-o.spinner.height()/2},o.spinner.css(s)},hide_button_spinner(e){e.prop("disabled",!1),o.spinner.hide()}};return o}(document,window,jQuery)).init()},615:()=>{(window.WPCodeInputs||function(e,t,n){const o={init(){n(o.ready)},ready(){o.initFileUploads(),o.initCheckboxMultiselectColumns()},initFileUploads(){n(".wpcode-file-upload").each(function(){const e=n(this).find("input[type=file]"),t=n(this).find("label"),o=t.html();e.on("change",function(e){let n="";this.files&&this.files.length>1?n=(this.getAttribute("data-multiple-caption")||"").replace("{count}",this.files.length):e.target.value&&(n=e.target.value.split("\\").pop()),n?t.find(".wpcode-file-field").html(n):t.html(o)}),e.on("focus",function(){e.addClass("has-focus")}).on("blur",function(){e.removeClass("has-focus")})})},initCheckboxMultiselectColumns(){n(e).on("change",".wpcode-checkbox-multiselect-columns input",function(){var e=n(this),t=e.parent(),o=e.closest(".wpcode-checkbox-multiselect-columns"),i=t.text(),a="check-item-"+e.val(),r=o.find("#"+a);e.prop("checked")?(e.parent().addClass("checked"),r.length||o.find(".second-column ul").append('<li id="'+a+'">'+i+"</li>")):(e.parent().removeClass("checked"),o.find("#"+a).remove())}),n(e).on("click",".wpcode-checkbox-multiselect-columns .all",function(e){e.preventDefault(),n(this).closest(".wpcode-checkbox-multiselect-columns").find("input[type=checkbox]").prop("checked",!0).trigger("change")})}};return o}(document,window,jQuery)).init()},632:(e,t,n)=>{var o,i,a;i=[n(428)],void 0===(a="function"==typeof(o=function(e){var t=function(){if(e&&e.fn&&e.fn.select2&&e.fn.select2.amd)var t=e.fn.select2.amd;var n,o,i;return t&&t.requirejs||(t?o=t:t={},function(e){var t,a,r,s,c={},l={},d={},u={},p=Object.prototype.hasOwnProperty,h=[].slice,f=/\.js$/;function m(e,t){return p.call(e,t)}function g(e,t){var n,o,i,a,r,s,c,l,u,p,h,m=t&&t.split("/"),g=d.map,_=g&&g["*"]||{};if(e){for(r=(e=e.split("/")).length-1,d.nodeIdCompat&&f.test(e[r])&&(e[r]=e[r].replace(f,"")),"."===e[0].charAt(0)&&m&&(e=m.slice(0,m.length-1).concat(e)),u=0;u<e.length;u++)if("."===(h=e[u]))e.splice(u,1),u-=1;else if(".."===h){if(0===u||1===u&&".."===e[2]||".."===e[u-1])continue;u>0&&(e.splice(u-1,2),u-=2)}e=e.join("/")}if((m||_)&&g){for(u=(n=e.split("/")).length;u>0;u-=1){if(o=n.slice(0,u).join("/"),m)for(p=m.length;p>0;p-=1)if((i=g[m.slice(0,p).join("/")])&&(i=i[o])){a=i,s=u;break}if(a)break;!c&&_&&_[o]&&(c=_[o],l=u)}!a&&c&&(a=c,s=l),a&&(n.splice(0,s,a),e=n.join("/"))}return e}function _(t,n){return function(){var o=h.call(arguments,0);return"string"!=typeof o[0]&&1===o.length&&o.push(null),a.apply(e,o.concat([t,n]))}}function v(e){return function(t){c[e]=t}}function w(n){if(m(l,n)){var o=l[n];delete l[n],u[n]=!0,t.apply(e,o)}if(!m(c,n)&&!m(u,n))throw new Error("No "+n);return c[n]}function y(e){var t,n=e?e.indexOf("!"):-1;return n>-1&&(t=e.substring(0,n),e=e.substring(n+1,e.length)),[t,e]}function b(e){return e?y(e):[]}function C(e){return function(){return d&&d.config&&d.config[e]||{}}}r=function(e,t){var n,o,i=y(e),a=i[0],r=t[1];return e=i[1],a&&(n=w(a=g(a,r))),a?e=n&&n.normalize?n.normalize(e,(o=r,function(e){return g(e,o)})):g(e,r):(a=(i=y(e=g(e,r)))[0],e=i[1],a&&(n=w(a))),{f:a?a+"!"+e:e,n:e,pr:a,p:n}},s={require:function(e){return _(e)},exports:function(e){var t=c[e];return void 0!==t?t:c[e]={}},module:function(e){return{id:e,uri:"",exports:c[e],config:C(e)}}},t=function(t,n,o,i){var a,d,p,h,f,g,y,C=[],x=typeof o;if(g=b(i=i||t),"undefined"===x||"function"===x){for(n=!n.length&&o.length?["require","exports","module"]:n,f=0;f<n.length;f+=1)if("require"===(d=(h=r(n[f],g)).f))C[f]=s.require(t);else if("exports"===d)C[f]=s.exports(t),y=!0;else if("module"===d)a=C[f]=s.module(t);else if(m(c,d)||m(l,d)||m(u,d))C[f]=w(d);else{if(!h.p)throw new Error(t+" missing "+d);h.p.load(h.n,_(i,!0),v(d),{}),C[f]=c[d]}p=o?o.apply(c[t],C):void 0,t&&(a&&a.exports!==e&&a.exports!==c[t]?c[t]=a.exports:p===e&&y||(c[t]=p))}else t&&(c[t]=o)},n=o=a=function(n,o,i,c,l){if("string"==typeof n)return s[n]?s[n](o):w(r(n,b(o)).f);if(!n.splice){if((d=n).deps&&a(d.deps,d.callback),!o)return;o.splice?(n=o,o=i,i=null):n=e}return o=o||function(){},"function"==typeof i&&(i=c,c=l),c?t(e,n,o,i):setTimeout(function(){t(e,n,o,i)},4),a},a.config=function(e){return a(e)},n._defined=c,(i=function(e,t,n){if("string"!=typeof e)throw new Error("See almond README: incorrect module build, no module name");t.splice||(n=t,t=[]),m(c,e)||m(l,e)||(l[e]=[e,t,n])}).amd={jQuery:!0}}(),t.requirejs=n,t.require=o,t.define=i),t.define("almond",function(){}),t.define("jquery",[],function(){var t=e||$;return null==t&&console&&console.error&&console.error("Select2: An instance of jQuery or a jQuery-compatible library was not found. Make sure that you are including jQuery before Select2 on your web page."),t}),t.define("select2/utils",["jquery"],function(e){var t={};function n(e){var t=e.prototype,n=[];for(var o in t)"function"==typeof t[o]&&"constructor"!==o&&n.push(o);return n}t.Extend=function(e,t){var n={}.hasOwnProperty;function o(){this.constructor=e}for(var i in t)n.call(t,i)&&(e[i]=t[i]);return o.prototype=t.prototype,e.prototype=new o,e.__super__=t.prototype,e},t.Decorate=function(e,t){var o=n(t),i=n(e);function a(){var n=Array.prototype.unshift,o=t.prototype.constructor.length,i=e.prototype.constructor;o>0&&(n.call(arguments,e.prototype.constructor),i=t.prototype.constructor),i.apply(this,arguments)}t.displayName=e.displayName,a.prototype=new function(){this.constructor=a};for(var r=0;r<i.length;r++){var s=i[r];a.prototype[s]=e.prototype[s]}for(var c=function(e){var n=function(){};e in a.prototype&&(n=a.prototype[e]);var o=t.prototype[e];return function(){return Array.prototype.unshift.call(arguments,n),o.apply(this,arguments)}},l=0;l<o.length;l++){var d=o[l];a.prototype[d]=c(d)}return a};var o=function(){this.listeners={}};return o.prototype.on=function(e,t){this.listeners=this.listeners||{},e in this.listeners?this.listeners[e].push(t):this.listeners[e]=[t]},o.prototype.trigger=function(e){var t=Array.prototype.slice,n=t.call(arguments,1);this.listeners=this.listeners||{},null==n&&(n=[]),0===n.length&&n.push({}),n[0]._type=e,e in this.listeners&&this.invoke(this.listeners[e],t.call(arguments,1)),"*"in this.listeners&&this.invoke(this.listeners["*"],arguments)},o.prototype.invoke=function(e,t){for(var n=0,o=e.length;n<o;n++)e[n].apply(this,t)},t.Observable=o,t.generateChars=function(e){for(var t="",n=0;n<e;n++)t+=Math.floor(36*Math.random()).toString(36);return t},t.bind=function(e,t){return function(){e.apply(t,arguments)}},t._convertData=function(e){for(var t in e){var n=t.split("-"),o=e;if(1!==n.length){for(var i=0;i<n.length;i++){var a=n[i];(a=a.substring(0,1).toLowerCase()+a.substring(1))in o||(o[a]={}),i==n.length-1&&(o[a]=e[t]),o=o[a]}delete e[t]}}return e},t.hasScroll=function(t,n){var o=e(n),i=n.style.overflowX,a=n.style.overflowY;return(i!==a||"hidden"!==a&&"visible"!==a)&&("scroll"===i||"scroll"===a||o.innerHeight()<n.scrollHeight||o.innerWidth()<n.scrollWidth)},t.escapeMarkup=function(e){var t={"\\":"&#92;","&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#47;"};return"string"!=typeof e?e:String(e).replace(/[&<>"'\/\\]/g,function(e){return t[e]})},t.appendMany=function(t,n){if("1.7"===e.fn.jquery.substr(0,3)){var o=e();e.map(n,function(e){o=o.add(e)}),n=o}t.append(n)},t.isTouchscreen=function(){return void 0===t._isTouchscreenCache&&(t._isTouchscreenCache="ontouchstart"in document.documentElement),t._isTouchscreenCache},t}),t.define("select2/results",["jquery","./utils"],function(e,t){function n(e,t,o){this.$element=e,this.data=o,this.options=t,n.__super__.constructor.call(this)}return t.Extend(n,t.Observable),n.prototype.render=function(){var t=e('<ul class="select2-results__options" role="listbox" tabindex="-1"></ul>');return this.options.get("multiple")&&t.attr("aria-multiselectable","true"),this.$results=t,t},n.prototype.clear=function(){this.$results.empty()},n.prototype.displayMessage=function(t){var n=this.options.get("escapeMarkup");this.clear(),this.hideLoading();var o=e('<li role="alert" aria-live="assertive" class="select2-results__option"></li>'),i=this.options.get("translations").get(t.message);o.append(n(i(t.args))),o[0].className+=" select2-results__message",this.$results.append(o)},n.prototype.hideMessages=function(){this.$results.find(".select2-results__message").remove()},n.prototype.append=function(e){this.hideLoading();var t=[];if(null!=e.results&&0!==e.results.length){e.results=this.sort(e.results);for(var n=0;n<e.results.length;n++){var o=e.results[n],i=this.option(o);t.push(i)}this.$results.append(t)}else 0===this.$results.children().length&&this.trigger("results:message",{message:"noResults"})},n.prototype.position=function(e,t){t.find(".select2-results").append(e)},n.prototype.sort=function(e){return this.options.get("sorter")(e)},n.prototype.highlightFirstItem=function(){var e=this.$results.find(".select2-results__option[data-selected]"),t=e.filter("[data-selected=true]");t.length>0?t.first().trigger("mouseenter"):e.first().trigger("mouseenter"),this.ensureHighlightVisible()},n.prototype.setClasses=function(){var t=this;this.data.current(function(n){var o=e.map(n,function(e){return e.id.toString()});t.$results.find(".select2-results__option[data-selected]").each(function(){var t=e(this),n=e.data(this,"data"),i=""+n.id;null!=n.element&&n.element.selected||null==n.element&&e.inArray(i,o)>-1?t.attr("data-selected","true"):t.attr("data-selected","false")})})},n.prototype.showLoading=function(e){this.hideLoading();var t={disabled:!0,loading:!0,text:this.options.get("translations").get("searching")(e)},n=this.option(t);n.className+=" loading-results",this.$results.prepend(n)},n.prototype.hideLoading=function(){this.$results.find(".loading-results").remove()},n.prototype.option=function(t){var n=document.createElement("li");n.className="select2-results__option";var o={role:"option","data-selected":"false",tabindex:-1};for(var i in t.disabled&&(delete o["data-selected"],o["aria-disabled"]="true"),null==t.id&&delete o["data-selected"],null!=t._resultId&&(n.id=t._resultId),t.title&&(n.title=t.title),t.children&&(o["aria-label"]=t.text,delete o["data-selected"]),o){var a=o[i];n.setAttribute(i,a)}if(t.children){var r=e(n),s=document.createElement("strong");s.className="select2-results__group";var c=e(s);this.template(t,s),c.attr("role","presentation");for(var l=[],d=0;d<t.children.length;d++){var u=t.children[d],p=this.option(u);l.push(p)}var h=e("<ul></ul>",{class:"select2-results__options select2-results__options--nested",role:"listbox"});h.append(l),r.attr("role","list"),r.append(s),r.append(h)}else this.template(t,n);return e.data(n,"data",t),n},n.prototype.bind=function(t,n){var o=this,i=t.id+"-results";this.$results.attr("id",i),t.on("results:all",function(e){o.clear(),o.append(e.data),t.isOpen()&&(o.setClasses(),o.highlightFirstItem())}),t.on("results:append",function(e){o.append(e.data),t.isOpen()&&o.setClasses()}),t.on("query",function(e){o.hideMessages(),o.showLoading(e)}),t.on("select",function(){t.isOpen()&&(o.setClasses(),o.highlightFirstItem())}),t.on("unselect",function(){t.isOpen()&&(o.setClasses(),o.highlightFirstItem())}),t.on("open",function(){o.$results.attr("aria-expanded","true"),o.$results.attr("aria-hidden","false"),o.setClasses(),o.ensureHighlightVisible()}),t.on("close",function(){o.$results.attr("aria-expanded","false"),o.$results.attr("aria-hidden","true"),o.$results.removeAttr("aria-activedescendant")}),t.on("results:toggle",function(){var e=o.getHighlightedResults();0!==e.length&&e.trigger("mouseup")}),t.on("results:select",function(){var e=o.getHighlightedResults();if(0!==e.length){var t=e.data("data");"true"==e.attr("data-selected")?o.trigger("close",{}):o.trigger("select",{data:t})}}),t.on("results:previous",function(){var e=o.getHighlightedResults(),t=o.$results.find("[data-selected]"),n=t.index(e);if(0!==n){var i=n-1;0===e.length&&(i=0);var a=t.eq(i);a.trigger("mouseenter");var r=o.$results.offset().top,s=a.offset().top,c=o.$results.scrollTop()+(s-r);0===i?o.$results.scrollTop(0):s-r<0&&o.$results.scrollTop(c)}}),t.on("results:next",function(){var e=o.getHighlightedResults(),t=o.$results.find("[data-selected]"),n=t.index(e)+1;if(!(n>=t.length)){var i=t.eq(n);i.trigger("mouseenter");var a=o.$results.offset().top+o.$results.outerHeight(!1),r=i.offset().top+i.outerHeight(!1),s=o.$results.scrollTop()+r-a;0===n?o.$results.scrollTop(0):r>a&&o.$results.scrollTop(s)}}),t.on("results:focus",function(e){e.element.addClass("select2-results__option--highlighted").attr("aria-selected","true"),o.$results.attr("aria-activedescendant",e.element.attr("id"))}),t.on("results:message",function(e){o.displayMessage(e)}),e.fn.mousewheel&&this.$results.on("mousewheel",function(e){var t=o.$results.scrollTop(),n=o.$results.get(0).scrollHeight-t+e.deltaY,i=e.deltaY>0&&t-e.deltaY<=0,a=e.deltaY<0&&n<=o.$results.height();i?(o.$results.scrollTop(0),e.preventDefault(),e.stopPropagation()):a&&(o.$results.scrollTop(o.$results.get(0).scrollHeight-o.$results.height()),e.preventDefault(),e.stopPropagation())}),this.$results.on("mouseup",".select2-results__option[data-selected]",function(t){var n=e(this),i=n.data("data");"true"!==n.attr("data-selected")?o.trigger("select",{originalEvent:t,data:i}):o.options.get("multiple")?o.trigger("unselect",{originalEvent:t,data:i}):o.trigger("close",{})}),this.$results.on("mouseenter",".select2-results__option[data-selected]",function(t){var n=e(this).data("data");o.getHighlightedResults().removeClass("select2-results__option--highlighted").attr("aria-selected","false"),o.trigger("results:focus",{data:n,element:e(this)})})},n.prototype.getHighlightedResults=function(){return this.$results.find(".select2-results__option--highlighted")},n.prototype.destroy=function(){this.$results.remove()},n.prototype.ensureHighlightVisible=function(){var e=this.getHighlightedResults();if(0!==e.length){var t=this.$results.find("[data-selected]").index(e),n=this.$results.offset().top,o=e.offset().top,i=this.$results.scrollTop()+(o-n),a=o-n;i-=2*e.outerHeight(!1),t<=2?this.$results.scrollTop(0):(a>this.$results.outerHeight()||a<0)&&this.$results.scrollTop(i)}},n.prototype.template=function(t,n){var o=this.options.get("templateResult"),i=this.options.get("escapeMarkup"),a=o(t,n);null==a?n.style.display="none":"string"==typeof a?n.innerHTML=i(a):e(n).append(a)},n}),t.define("select2/keys",[],function(){return{BACKSPACE:8,TAB:9,ENTER:13,SHIFT:16,CTRL:17,ALT:18,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46}}),t.define("select2/selection/base",["jquery","../utils","../keys"],function(e,t,n){function o(e,t){this.$element=e,this.options=t,o.__super__.constructor.call(this)}return t.Extend(o,t.Observable),o.prototype.render=function(){var t=e('<span class="select2-selection"  aria-haspopup="true" aria-expanded="false"></span>');return this._tabindex=0,null!=this.$element.data("old-tabindex")?this._tabindex=this.$element.data("old-tabindex"):null!=this.$element.attr("tabindex")&&(this._tabindex=this.$element.attr("tabindex")),t.attr("title",this.$element.attr("title")),t.attr("tabindex",this._tabindex),this.$selection=t,t},o.prototype.bind=function(e,t){var o=this,i=(e.id,e.id+"-results");this.options.get("minimumResultsForSearch"),this.container=e,this.$selection.on("focus",function(e){o.trigger("focus",e)}),this.$selection.on("blur",function(e){o._handleBlur(e)}),this.$selection.on("keydown",function(e){o.trigger("keypress",e),e.which===n.SPACE&&e.preventDefault()}),e.on("results:focus",function(e){o.$selection.attr("aria-activedescendant",e.data._resultId)}),e.on("selection:update",function(e){o.update(e.data)}),e.on("open",function(){o.$selection.attr("aria-expanded","true"),o.$selection.attr("aria-owns",i),o._attachCloseHandler(e)}),e.on("close",function(){o.$selection.attr("aria-expanded","false"),o.$selection.removeAttr("aria-activedescendant"),o.$selection.removeAttr("aria-owns"),window.setTimeout(function(){o.$selection.focus()},1),o._detachCloseHandler(e)}),e.on("enable",function(){o.$selection.attr("tabindex",o._tabindex)}),e.on("disable",function(){o.$selection.attr("tabindex","-1")})},o.prototype._handleBlur=function(t){var n=this;window.setTimeout(function(){document.activeElement==n.$selection[0]||e.contains(n.$selection[0],document.activeElement)||n.trigger("blur",t)},1)},o.prototype._attachCloseHandler=function(t){e(document.body).on("mousedown.select2."+t.id,function(t){var n=e(t.target),o=n.closest(".select2");e(".select2.select2-container--open").each(function(){var t=e(this);this!=o[0]&&(t.data("element").select2("close"),setTimeout(function(){t.find("*:focus").blur(),n.focus()},1))})})},o.prototype._detachCloseHandler=function(t){e(document.body).off("mousedown.select2."+t.id)},o.prototype.position=function(e,t){t.find(".selection").append(e)},o.prototype.destroy=function(){this._detachCloseHandler(this.container)},o.prototype.update=function(e){throw new Error("The `update` method must be defined in child classes.")},o}),t.define("select2/selection/single",["jquery","./base","../utils","../keys"],function(e,t,n,o){function i(){i.__super__.constructor.apply(this,arguments)}return n.Extend(i,t),i.prototype.render=function(){var e=i.__super__.render.call(this);return e.addClass("select2-selection--single"),e.html('<span class="select2-selection__rendered"></span><span class="select2-selection__arrow" role="presentation"><b role="presentation"></b></span>'),e},i.prototype.bind=function(e,t){var n=this;i.__super__.bind.apply(this,arguments);var o=e.id+"-container";this.$selection.find(".select2-selection__rendered").attr("id",o).attr("role","textbox").attr("aria-readonly","true"),this.$selection.attr("aria-labelledby",o),this.$selection.attr("role","combobox"),this.$selection.on("mousedown",function(e){1===e.which&&n.trigger("toggle",{originalEvent:e})}),this.$selection.on("focus",function(e){}),this.$selection.on("keydown",function(t){!e.isOpen()&&t.which>=48&&t.which<=90&&e.open()}),this.$selection.on("blur",function(e){}),e.on("focus",function(t){e.isOpen()||n.$selection.focus()}),e.on("selection:update",function(e){n.update(e.data)})},i.prototype.clear=function(){this.$selection.find(".select2-selection__rendered").empty()},i.prototype.display=function(e,t){var n=this.options.get("templateSelection");return this.options.get("escapeMarkup")(n(e,t))},i.prototype.selectionContainer=function(){return e("<span></span>")},i.prototype.update=function(e){if(0!==e.length){var t=e[0],n=this.$selection.find(".select2-selection__rendered"),o=this.display(t,n);n.empty().append(o),n.prop("title",t.title||t.text)}else this.clear()},i}),t.define("select2/selection/multiple",["jquery","./base","../utils"],function(e,t,n){function o(e,t){o.__super__.constructor.apply(this,arguments)}return n.Extend(o,t),o.prototype.render=function(){var e=o.__super__.render.call(this);return e.addClass("select2-selection--multiple"),e.html('<ul class="select2-selection__rendered" aria-live="polite" aria-relevant="additions removals" aria-atomic="true"></ul>'),e},o.prototype.bind=function(t,n){var i=this;o.__super__.bind.apply(this,arguments),this.$selection.on("click",function(e){i.trigger("toggle",{originalEvent:e})}),this.$selection.on("click",".select2-selection__choice__remove",function(t){if(!i.options.get("disabled")){var n=e(this).parent().data("data");i.trigger("unselect",{originalEvent:t,data:n})}}),this.$selection.on("keydown",function(e){!t.isOpen()&&e.which>=48&&e.which<=90&&t.open()}),t.on("focus",function(){i.focusOnSearch()})},o.prototype.clear=function(){this.$selection.find(".select2-selection__rendered").empty()},o.prototype.display=function(e,t){var n=this.options.get("templateSelection");return this.options.get("escapeMarkup")(n(e,t))},o.prototype.selectionContainer=function(){return e('<li class="select2-selection__choice"><span class="select2-selection__choice__remove" role="presentation" aria-hidden="true">&times;</span></li>')},o.prototype.focusOnSearch=function(){var e=this;void 0!==e.$search&&setTimeout(function(){e._keyUpPrevented=!0,e.$search.focus()},1)},o.prototype.update=function(e){if(this.clear(),0!==e.length){for(var t=[],o=0;o<e.length;o++){var i=e[o],a=this.selectionContainer(),r=this.display(i,a);"string"==typeof r&&(r=r.trim()),a.append(r),a.prop("title",i.title||i.text),a.data("data",i),t.push(a)}var s=this.$selection.find(".select2-selection__rendered");n.appendMany(s,t)}},o}),t.define("select2/selection/placeholder",["../utils"],function(e){function t(e,t,n){this.placeholder=this.normalizePlaceholder(n.get("placeholder")),e.call(this,t,n)}return t.prototype.normalizePlaceholder=function(e,t){return"string"==typeof t&&(t={id:"",text:t}),t},t.prototype.createPlaceholder=function(e,t){var n=this.selectionContainer();return n.html(this.display(t)),n.addClass("select2-selection__placeholder").removeClass("select2-selection__choice"),n},t.prototype.update=function(e,t){var n=1==t.length&&t[0].id!=this.placeholder.id;if(t.length>1||n)return e.call(this,t);this.clear();var o=this.createPlaceholder(this.placeholder);this.$selection.find(".select2-selection__rendered").append(o)},t}),t.define("select2/selection/allowClear",["jquery","../keys"],function(e,t){function n(){}return n.prototype.bind=function(e,t,n){var o=this;e.call(this,t,n),null==this.placeholder&&this.options.get("debug")&&window.console&&console.error&&console.error("Select2: The `allowClear` option should be used in combination with the `placeholder` option."),this.$selection.on("mousedown",".select2-selection__clear",function(e){o._handleClear(e)}),t.on("keypress",function(e){o._handleKeyboardClear(e,t)})},n.prototype._handleClear=function(e,t){if(!this.options.get("disabled")){var n=this.$selection.find(".select2-selection__clear");if(0!==n.length){t.stopPropagation();for(var o=n.data("data"),i=0;i<o.length;i++){var a={data:o[i]};if(this.trigger("unselect",a),a.prevented)return}this.$element.val(this.placeholder.id).trigger("change"),this.trigger("toggle",{})}}},n.prototype._handleKeyboardClear=function(e,n,o){o.isOpen()||n.which!=t.DELETE&&n.which!=t.BACKSPACE||this._handleClear(n)},n.prototype.update=function(t,n){if(t.call(this,n),!(this.$selection.find(".select2-selection__placeholder").length>0||0===n.length)){var o=e('<span class="select2-selection__clear">&times;</span>');o.data("data",n),this.$selection.find(".select2-selection__rendered").prepend(o)}},n}),t.define("select2/selection/search",["jquery","../utils","../keys"],function(e,t,n){function o(e,t,n){e.call(this,t,n)}return o.prototype.render=function(t){var n=e('<li class="select2-search select2-search--inline"><input class="select2-search__field" type="text" tabindex="-1" autocomplete="off" autocorrect="off" autocapitalize="none" spellcheck="false" role="textbox" aria-autocomplete="list" /></li>');this.$searchContainer=n,this.$search=n.find("input");var o=t.call(this);return this._transferTabIndex(),o},o.prototype.bind=function(e,t,o){var i=this,a=t.id+"-results";e.call(this,t,o),t.on("open",function(){i.$search.attr("aria-owns",a),i.$search.trigger("focus")}),t.on("close",function(){i.$search.val(""),i.$search.removeAttr("aria-activedescendant"),i.$search.removeAttr("aria-owns"),i.$search.trigger("focus")}),t.on("enable",function(){i.$search.prop("disabled",!1),i._transferTabIndex()}),t.on("disable",function(){i.$search.prop("disabled",!0)}),t.on("focus",function(e){i.$search.trigger("focus")}),t.on("results:focus",function(e){i.$search.attr("aria-activedescendant",e.data._resultId)}),this.$selection.on("focusin",".select2-search--inline",function(e){i.trigger("focus",e)}),this.$selection.on("focusout",".select2-search--inline",function(e){i._handleBlur(e)}),this.$selection.on("keydown",".select2-search--inline",function(e){if(e.stopPropagation(),i.trigger("keypress",e),i._keyUpPrevented=e.isDefaultPrevented(),e.which===n.BACKSPACE&&""===i.$search.val()){var o=i.$searchContainer.prev(".select2-selection__choice");if(o.length>0){var a=o.data("data");i.searchRemoveChoice(a),e.preventDefault()}}else e.which===n.ENTER&&(t.open(),e.preventDefault())});var r=document.documentMode,s=r&&r<=11;this.$selection.on("input.searchcheck",".select2-search--inline",function(e){s?i.$selection.off("input.search input.searchcheck"):i.$selection.off("keyup.search")}),this.$selection.on("keyup.search input.search",".select2-search--inline",function(e){if(s&&"input"===e.type)i.$selection.off("input.search input.searchcheck");else{var t=e.which;t!=n.SHIFT&&t!=n.CTRL&&t!=n.ALT&&t!=n.TAB&&i.handleSearch(e)}})},o.prototype._transferTabIndex=function(e){this.$search.attr("tabindex",this.$selection.attr("tabindex")),this.$selection.attr("tabindex","-1")},o.prototype.createPlaceholder=function(e,t){this.$search.attr("placeholder",t.text)},o.prototype.update=function(e,t){var n=this.$search[0]==document.activeElement;this.$search.attr("placeholder",""),e.call(this,t),this.$selection.find(".select2-selection__rendered").append(this.$searchContainer),this.resizeSearch(),n&&this.$search.focus()},o.prototype.handleSearch=function(){if(this.resizeSearch(),!this._keyUpPrevented){var e=this.$search.val();this.trigger("query",{term:e})}this._keyUpPrevented=!1},o.prototype.searchRemoveChoice=function(e,t){this.trigger("unselect",{data:t}),this.$search.val(t.text),this.handleSearch()},o.prototype.resizeSearch=function(){this.$search.css("width","25px");var e;e=""!==this.$search.attr("placeholder")?this.$selection.find(".select2-selection__rendered").innerWidth():.75*(this.$search.val().length+1)+"em",this.$search.css("width",e)},o}),t.define("select2/selection/eventRelay",["jquery"],function(e){function t(){}return t.prototype.bind=function(t,n,o){var i=this,a=["open","opening","close","closing","select","selecting","unselect","unselecting"],r=["opening","closing","selecting","unselecting"];t.call(this,n,o),n.on("*",function(t,n){if(-1!==e.inArray(t,a)){n=n||{};var o=e.Event("select2:"+t,{params:n});i.$element.trigger(o),-1!==e.inArray(t,r)&&(n.prevented=o.isDefaultPrevented())}})},t}),t.define("select2/translation",["jquery","require"],function(e,t){function n(e){this.dict=e||{}}return n.prototype.all=function(){return this.dict},n.prototype.get=function(e){return this.dict[e]},n.prototype.extend=function(t){this.dict=e.extend({},t.all(),this.dict)},n._cache={},n.loadPath=function(e){if(!(e in n._cache)){var o=t(e);n._cache[e]=o}return new n(n._cache[e])},n}),t.define("select2/diacritics",[],function(){return{"Ⓐ":"A",Ａ:"A",À:"A",Á:"A",Â:"A",Ầ:"A",Ấ:"A",Ẫ:"A",Ẩ:"A",Ã:"A",Ā:"A",Ă:"A",Ằ:"A",Ắ:"A",Ẵ:"A",Ẳ:"A",Ȧ:"A",Ǡ:"A",Ä:"A",Ǟ:"A",Ả:"A",Å:"A",Ǻ:"A",Ǎ:"A",Ȁ:"A",Ȃ:"A",Ạ:"A",Ậ:"A",Ặ:"A",Ḁ:"A",Ą:"A",Ⱥ:"A",Ɐ:"A",Ꜳ:"AA",Æ:"AE",Ǽ:"AE",Ǣ:"AE",Ꜵ:"AO",Ꜷ:"AU",Ꜹ:"AV",Ꜻ:"AV",Ꜽ:"AY","Ⓑ":"B",Ｂ:"B",Ḃ:"B",Ḅ:"B",Ḇ:"B",Ƀ:"B",Ƃ:"B",Ɓ:"B","Ⓒ":"C",Ｃ:"C",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",Ç:"C",Ḉ:"C",Ƈ:"C",Ȼ:"C",Ꜿ:"C","Ⓓ":"D",Ｄ:"D",Ḋ:"D",Ď:"D",Ḍ:"D",Ḑ:"D",Ḓ:"D",Ḏ:"D",Đ:"D",Ƌ:"D",Ɗ:"D",Ɖ:"D",Ꝺ:"D",Ǳ:"DZ",Ǆ:"DZ",ǲ:"Dz",ǅ:"Dz","Ⓔ":"E",Ｅ:"E",È:"E",É:"E",Ê:"E",Ề:"E",Ế:"E",Ễ:"E",Ể:"E",Ẽ:"E",Ē:"E",Ḕ:"E",Ḗ:"E",Ĕ:"E",Ė:"E",Ë:"E",Ẻ:"E",Ě:"E",Ȅ:"E",Ȇ:"E",Ẹ:"E",Ệ:"E",Ȩ:"E",Ḝ:"E",Ę:"E",Ḙ:"E",Ḛ:"E",Ɛ:"E",Ǝ:"E","Ⓕ":"F",Ｆ:"F",Ḟ:"F",Ƒ:"F",Ꝼ:"F","Ⓖ":"G",Ｇ:"G",Ǵ:"G",Ĝ:"G",Ḡ:"G",Ğ:"G",Ġ:"G",Ǧ:"G",Ģ:"G",Ǥ:"G",Ɠ:"G",Ꞡ:"G",Ᵹ:"G",Ꝿ:"G","Ⓗ":"H",Ｈ:"H",Ĥ:"H",Ḣ:"H",Ḧ:"H",Ȟ:"H",Ḥ:"H",Ḩ:"H",Ḫ:"H",Ħ:"H",Ⱨ:"H",Ⱶ:"H",Ɥ:"H","Ⓘ":"I",Ｉ:"I",Ì:"I",Í:"I",Î:"I",Ĩ:"I",Ī:"I",Ĭ:"I",İ:"I",Ï:"I",Ḯ:"I",Ỉ:"I",Ǐ:"I",Ȉ:"I",Ȋ:"I",Ị:"I",Į:"I",Ḭ:"I",Ɨ:"I","Ⓙ":"J",Ｊ:"J",Ĵ:"J",Ɉ:"J","Ⓚ":"K",Ｋ:"K",Ḱ:"K",Ǩ:"K",Ḳ:"K",Ķ:"K",Ḵ:"K",Ƙ:"K",Ⱪ:"K",Ꝁ:"K",Ꝃ:"K",Ꝅ:"K",Ꞣ:"K","Ⓛ":"L",Ｌ:"L",Ŀ:"L",Ĺ:"L",Ľ:"L",Ḷ:"L",Ḹ:"L",Ļ:"L",Ḽ:"L",Ḻ:"L",Ł:"L",Ƚ:"L",Ɫ:"L",Ⱡ:"L",Ꝉ:"L",Ꝇ:"L",Ꞁ:"L",Ǉ:"LJ",ǈ:"Lj","Ⓜ":"M",Ｍ:"M",Ḿ:"M",Ṁ:"M",Ṃ:"M",Ɱ:"M",Ɯ:"M","Ⓝ":"N",Ｎ:"N",Ǹ:"N",Ń:"N",Ñ:"N",Ṅ:"N",Ň:"N",Ṇ:"N",Ņ:"N",Ṋ:"N",Ṉ:"N",Ƞ:"N",Ɲ:"N",Ꞑ:"N",Ꞥ:"N",Ǌ:"NJ",ǋ:"Nj","Ⓞ":"O",Ｏ:"O",Ò:"O",Ó:"O",Ô:"O",Ồ:"O",Ố:"O",Ỗ:"O",Ổ:"O",Õ:"O",Ṍ:"O",Ȭ:"O",Ṏ:"O",Ō:"O",Ṑ:"O",Ṓ:"O",Ŏ:"O",Ȯ:"O",Ȱ:"O",Ö:"O",Ȫ:"O",Ỏ:"O",Ő:"O",Ǒ:"O",Ȍ:"O",Ȏ:"O",Ơ:"O",Ờ:"O",Ớ:"O",Ỡ:"O",Ở:"O",Ợ:"O",Ọ:"O",Ộ:"O",Ǫ:"O",Ǭ:"O",Ø:"O",Ǿ:"O",Ɔ:"O",Ɵ:"O",Ꝋ:"O",Ꝍ:"O",Ƣ:"OI",Ꝏ:"OO",Ȣ:"OU","Ⓟ":"P",Ｐ:"P",Ṕ:"P",Ṗ:"P",Ƥ:"P",Ᵽ:"P",Ꝑ:"P",Ꝓ:"P",Ꝕ:"P","Ⓠ":"Q",Ｑ:"Q",Ꝗ:"Q",Ꝙ:"Q",Ɋ:"Q","Ⓡ":"R",Ｒ:"R",Ŕ:"R",Ṙ:"R",Ř:"R",Ȑ:"R",Ȓ:"R",Ṛ:"R",Ṝ:"R",Ŗ:"R",Ṟ:"R",Ɍ:"R",Ɽ:"R",Ꝛ:"R",Ꞧ:"R",Ꞃ:"R","Ⓢ":"S",Ｓ:"S",ẞ:"S",Ś:"S",Ṥ:"S",Ŝ:"S",Ṡ:"S",Š:"S",Ṧ:"S",Ṣ:"S",Ṩ:"S",Ș:"S",Ş:"S",Ȿ:"S",Ꞩ:"S",Ꞅ:"S","Ⓣ":"T",Ｔ:"T",Ṫ:"T",Ť:"T",Ṭ:"T",Ț:"T",Ţ:"T",Ṱ:"T",Ṯ:"T",Ŧ:"T",Ƭ:"T",Ʈ:"T",Ⱦ:"T",Ꞇ:"T",Ꜩ:"TZ","Ⓤ":"U",Ｕ:"U",Ù:"U",Ú:"U",Û:"U",Ũ:"U",Ṹ:"U",Ū:"U",Ṻ:"U",Ŭ:"U",Ü:"U",Ǜ:"U",Ǘ:"U",Ǖ:"U",Ǚ:"U",Ủ:"U",Ů:"U",Ű:"U",Ǔ:"U",Ȕ:"U",Ȗ:"U",Ư:"U",Ừ:"U",Ứ:"U",Ữ:"U",Ử:"U",Ự:"U",Ụ:"U",Ṳ:"U",Ų:"U",Ṷ:"U",Ṵ:"U",Ʉ:"U","Ⓥ":"V",Ｖ:"V",Ṽ:"V",Ṿ:"V",Ʋ:"V",Ꝟ:"V",Ʌ:"V",Ꝡ:"VY","Ⓦ":"W",Ｗ:"W",Ẁ:"W",Ẃ:"W",Ŵ:"W",Ẇ:"W",Ẅ:"W",Ẉ:"W",Ⱳ:"W","Ⓧ":"X",Ｘ:"X",Ẋ:"X",Ẍ:"X","Ⓨ":"Y",Ｙ:"Y",Ỳ:"Y",Ý:"Y",Ŷ:"Y",Ỹ:"Y",Ȳ:"Y",Ẏ:"Y",Ÿ:"Y",Ỷ:"Y",Ỵ:"Y",Ƴ:"Y",Ɏ:"Y",Ỿ:"Y","Ⓩ":"Z",Ｚ:"Z",Ź:"Z",Ẑ:"Z",Ż:"Z",Ž:"Z",Ẓ:"Z",Ẕ:"Z",Ƶ:"Z",Ȥ:"Z",Ɀ:"Z",Ⱬ:"Z",Ꝣ:"Z","ⓐ":"a",ａ:"a",ẚ:"a",à:"a",á:"a",â:"a",ầ:"a",ấ:"a",ẫ:"a",ẩ:"a",ã:"a",ā:"a",ă:"a",ằ:"a",ắ:"a",ẵ:"a",ẳ:"a",ȧ:"a",ǡ:"a",ä:"a",ǟ:"a",ả:"a",å:"a",ǻ:"a",ǎ:"a",ȁ:"a",ȃ:"a",ạ:"a",ậ:"a",ặ:"a",ḁ:"a",ą:"a",ⱥ:"a",ɐ:"a",ꜳ:"aa",æ:"ae",ǽ:"ae",ǣ:"ae",ꜵ:"ao",ꜷ:"au",ꜹ:"av",ꜻ:"av",ꜽ:"ay","ⓑ":"b",ｂ:"b",ḃ:"b",ḅ:"b",ḇ:"b",ƀ:"b",ƃ:"b",ɓ:"b","ⓒ":"c",ｃ:"c",ć:"c",ĉ:"c",ċ:"c",č:"c",ç:"c",ḉ:"c",ƈ:"c",ȼ:"c",ꜿ:"c",ↄ:"c","ⓓ":"d",ｄ:"d",ḋ:"d",ď:"d",ḍ:"d",ḑ:"d",ḓ:"d",ḏ:"d",đ:"d",ƌ:"d",ɖ:"d",ɗ:"d",ꝺ:"d",ǳ:"dz",ǆ:"dz","ⓔ":"e",ｅ:"e",è:"e",é:"e",ê:"e",ề:"e",ế:"e",ễ:"e",ể:"e",ẽ:"e",ē:"e",ḕ:"e",ḗ:"e",ĕ:"e",ė:"e",ë:"e",ẻ:"e",ě:"e",ȅ:"e",ȇ:"e",ẹ:"e",ệ:"e",ȩ:"e",ḝ:"e",ę:"e",ḙ:"e",ḛ:"e",ɇ:"e",ɛ:"e",ǝ:"e","ⓕ":"f",ｆ:"f",ḟ:"f",ƒ:"f",ꝼ:"f","ⓖ":"g",ｇ:"g",ǵ:"g",ĝ:"g",ḡ:"g",ğ:"g",ġ:"g",ǧ:"g",ģ:"g",ǥ:"g",ɠ:"g",ꞡ:"g",ᵹ:"g",ꝿ:"g","ⓗ":"h",ｈ:"h",ĥ:"h",ḣ:"h",ḧ:"h",ȟ:"h",ḥ:"h",ḩ:"h",ḫ:"h",ẖ:"h",ħ:"h",ⱨ:"h",ⱶ:"h",ɥ:"h",ƕ:"hv","ⓘ":"i",ｉ:"i",ì:"i",í:"i",î:"i",ĩ:"i",ī:"i",ĭ:"i",ï:"i",ḯ:"i",ỉ:"i",ǐ:"i",ȉ:"i",ȋ:"i",ị:"i",į:"i",ḭ:"i",ɨ:"i",ı:"i","ⓙ":"j",ｊ:"j",ĵ:"j",ǰ:"j",ɉ:"j","ⓚ":"k",ｋ:"k",ḱ:"k",ǩ:"k",ḳ:"k",ķ:"k",ḵ:"k",ƙ:"k",ⱪ:"k",ꝁ:"k",ꝃ:"k",ꝅ:"k",ꞣ:"k","ⓛ":"l",ｌ:"l",ŀ:"l",ĺ:"l",ľ:"l",ḷ:"l",ḹ:"l",ļ:"l",ḽ:"l",ḻ:"l",ſ:"l",ł:"l",ƚ:"l",ɫ:"l",ⱡ:"l",ꝉ:"l",ꞁ:"l",ꝇ:"l",ǉ:"lj","ⓜ":"m",ｍ:"m",ḿ:"m",ṁ:"m",ṃ:"m",ɱ:"m",ɯ:"m","ⓝ":"n",ｎ:"n",ǹ:"n",ń:"n",ñ:"n",ṅ:"n",ň:"n",ṇ:"n",ņ:"n",ṋ:"n",ṉ:"n",ƞ:"n",ɲ:"n",ŉ:"n",ꞑ:"n",ꞥ:"n",ǌ:"nj","ⓞ":"o",ｏ:"o",ò:"o",ó:"o",ô:"o",ồ:"o",ố:"o",ỗ:"o",ổ:"o",õ:"o",ṍ:"o",ȭ:"o",ṏ:"o",ō:"o",ṑ:"o",ṓ:"o",ŏ:"o",ȯ:"o",ȱ:"o",ö:"o",ȫ:"o",ỏ:"o",ő:"o",ǒ:"o",ȍ:"o",ȏ:"o",ơ:"o",ờ:"o",ớ:"o",ỡ:"o",ở:"o",ợ:"o",ọ:"o",ộ:"o",ǫ:"o",ǭ:"o",ø:"o",ǿ:"o",ɔ:"o",ꝋ:"o",ꝍ:"o",ɵ:"o",ƣ:"oi",ȣ:"ou",ꝏ:"oo","ⓟ":"p",ｐ:"p",ṕ:"p",ṗ:"p",ƥ:"p",ᵽ:"p",ꝑ:"p",ꝓ:"p",ꝕ:"p","ⓠ":"q",ｑ:"q",ɋ:"q",ꝗ:"q",ꝙ:"q","ⓡ":"r",ｒ:"r",ŕ:"r",ṙ:"r",ř:"r",ȑ:"r",ȓ:"r",ṛ:"r",ṝ:"r",ŗ:"r",ṟ:"r",ɍ:"r",ɽ:"r",ꝛ:"r",ꞧ:"r",ꞃ:"r","ⓢ":"s",ｓ:"s",ß:"s",ś:"s",ṥ:"s",ŝ:"s",ṡ:"s",š:"s",ṧ:"s",ṣ:"s",ṩ:"s",ș:"s",ş:"s",ȿ:"s",ꞩ:"s",ꞅ:"s",ẛ:"s","ⓣ":"t",ｔ:"t",ṫ:"t",ẗ:"t",ť:"t",ṭ:"t",ț:"t",ţ:"t",ṱ:"t",ṯ:"t",ŧ:"t",ƭ:"t",ʈ:"t",ⱦ:"t",ꞇ:"t",ꜩ:"tz","ⓤ":"u",ｕ:"u",ù:"u",ú:"u",û:"u",ũ:"u",ṹ:"u",ū:"u",ṻ:"u",ŭ:"u",ü:"u",ǜ:"u",ǘ:"u",ǖ:"u",ǚ:"u",ủ:"u",ů:"u",ű:"u",ǔ:"u",ȕ:"u",ȗ:"u",ư:"u",ừ:"u",ứ:"u",ữ:"u",ử:"u",ự:"u",ụ:"u",ṳ:"u",ų:"u",ṷ:"u",ṵ:"u",ʉ:"u","ⓥ":"v",ｖ:"v",ṽ:"v",ṿ:"v",ʋ:"v",ꝟ:"v",ʌ:"v",ꝡ:"vy","ⓦ":"w",ｗ:"w",ẁ:"w",ẃ:"w",ŵ:"w",ẇ:"w",ẅ:"w",ẘ:"w",ẉ:"w",ⱳ:"w","ⓧ":"x",ｘ:"x",ẋ:"x",ẍ:"x","ⓨ":"y",ｙ:"y",ỳ:"y",ý:"y",ŷ:"y",ỹ:"y",ȳ:"y",ẏ:"y",ÿ:"y",ỷ:"y",ẙ:"y",ỵ:"y",ƴ:"y",ɏ:"y",ỿ:"y","ⓩ":"z",ｚ:"z",ź:"z",ẑ:"z",ż:"z",ž:"z",ẓ:"z",ẕ:"z",ƶ:"z",ȥ:"z",ɀ:"z",ⱬ:"z",ꝣ:"z",Ά:"Α",Έ:"Ε",Ή:"Η",Ί:"Ι",Ϊ:"Ι",Ό:"Ο",Ύ:"Υ",Ϋ:"Υ",Ώ:"Ω",ά:"α",έ:"ε",ή:"η",ί:"ι",ϊ:"ι",ΐ:"ι",ό:"ο",ύ:"υ",ϋ:"υ",ΰ:"υ",ω:"ω",ς:"σ"}}),t.define("select2/data/base",["../utils"],function(e){function t(e,n){t.__super__.constructor.call(this)}return e.Extend(t,e.Observable),t.prototype.current=function(e){throw new Error("The `current` method must be defined in child classes.")},t.prototype.query=function(e,t){throw new Error("The `query` method must be defined in child classes.")},t.prototype.bind=function(e,t){},t.prototype.destroy=function(){},t.prototype.generateResultId=function(t,n){var o="";return o+=null!=t?t.id:e.generateChars(4),o+="-result-",o+=e.generateChars(4),null!=n.id?o+="-"+n.id.toString():o+="-"+e.generateChars(4),o},t}),t.define("select2/data/select",["./base","../utils","jquery"],function(e,t,n){function o(e,t){this.$element=e,this.options=t,o.__super__.constructor.call(this)}return t.Extend(o,e),o.prototype.current=function(e){var t=[],o=this;this.$element.find(":selected").each(function(){var e=n(this),i=o.item(e);t.push(i)}),e(t)},o.prototype.select=function(e){var t=this;if(e.selected=!0,n(e.element).is("option"))return e.element.selected=!0,void this.$element.trigger("change");if(this.$element.prop("multiple"))this.current(function(o){var i=[];(e=[e]).push.apply(e,o);for(var a=0;a<e.length;a++){var r=e[a].id;-1===n.inArray(r,i)&&i.push(r)}t.$element.val(i),t.$element.trigger("change")});else{var o=e.id;this.$element.val(o),this.$element.trigger("change")}},o.prototype.unselect=function(e){var t=this;if(this.$element.prop("multiple")){if(e.selected=!1,n(e.element).is("option"))return e.element.selected=!1,void this.$element.trigger("change");this.current(function(o){for(var i=[],a=0;a<o.length;a++){var r=o[a].id;r!==e.id&&-1===n.inArray(r,i)&&i.push(r)}t.$element.val(i),t.$element.trigger("change")})}},o.prototype.bind=function(e,t){var n=this;this.container=e,e.on("select",function(e){n.select(e.data)}),e.on("unselect",function(e){n.unselect(e.data)})},o.prototype.destroy=function(){this.$element.find("*").each(function(){n.removeData(this,"data")})},o.prototype.query=function(e,t){var o=[],i=this;this.$element.children().each(function(){var t=n(this);if(t.is("option")||t.is("optgroup")){var a=i.item(t),r=i.matches(e,a);null!==r&&o.push(r)}}),t({results:o})},o.prototype.addOptions=function(e){t.appendMany(this.$element,e)},o.prototype.option=function(e){var t;e.children?(t=document.createElement("optgroup")).label=e.text:void 0!==(t=document.createElement("option")).textContent?t.textContent=e.text:t.innerText=e.text,void 0!==e.id&&(t.value=e.id),e.disabled&&(t.disabled=!0),e.selected&&(t.selected=!0),e.title&&(t.title=e.title);var o=n(t),i=this._normalizeItem(e);return i.element=t,n.data(t,"data",i),o},o.prototype.item=function(e){var t={};if(null!=(t=n.data(e[0],"data")))return t;if(e.is("option"))t={id:e.val(),text:e.text(),disabled:e.prop("disabled"),selected:e.prop("selected"),title:e.prop("title")};else if(e.is("optgroup")){t={text:e.prop("label"),children:[],title:e.prop("title")};for(var o=e.children("option"),i=[],a=0;a<o.length;a++){var r=n(o[a]),s=this.item(r);i.push(s)}t.children=i}return(t=this._normalizeItem(t)).element=e[0],n.data(e[0],"data",t),t},o.prototype._normalizeItem=function(e){n.isPlainObject(e)||(e={id:e,text:e});return null!=(e=n.extend({},{text:""},e)).id&&(e.id=e.id.toString()),null!=e.text&&(e.text=e.text.toString()),null==e._resultId&&e.id&&(e._resultId=this.generateResultId(this.container,e)),n.extend({},{selected:!1,disabled:!1},e)},o.prototype.matches=function(e,t){return this.options.get("matcher")(e,t)},o}),t.define("select2/data/array",["./select","../utils","jquery"],function(e,t,n){function o(e,t){var n=t.get("data")||[];o.__super__.constructor.call(this,e,t),this.addOptions(this.convertToOptions(n))}return t.Extend(o,e),o.prototype.select=function(e){var t=this.$element.find("option").filter(function(t,n){return n.value==e.id.toString()});0===t.length&&(t=this.option(e),this.addOptions(t)),o.__super__.select.call(this,e)},o.prototype.convertToOptions=function(e){var o=this,i=this.$element.find("option"),a=i.map(function(){return o.item(n(this)).id}).get(),r=[];function s(e){return function(){return n(this).val()==e.id}}for(var c=0;c<e.length;c++){var l=this._normalizeItem(e[c]);if(n.inArray(l.id,a)>=0){var d=i.filter(s(l)),u=this.item(d),p=n.extend(!0,{},l,u),h=this.option(p);d.replaceWith(h)}else{var f=this.option(l);if(l.children){var m=this.convertToOptions(l.children);t.appendMany(f,m)}r.push(f)}}return r},o}),t.define("select2/data/ajax",["./array","../utils","jquery"],function(e,t,n){function o(e,t){this.ajaxOptions=this._applyDefaults(t.get("ajax")),null!=this.ajaxOptions.processResults&&(this.processResults=this.ajaxOptions.processResults),o.__super__.constructor.call(this,e,t)}return t.Extend(o,e),o.prototype._applyDefaults=function(e){var t={data:function(e){return n.extend({},e,{q:e.term})},transport:function(e,t,o){var i=n.ajax(e);return i.then(t),i.fail(o),i}};return n.extend({},t,e,!0)},o.prototype.processResults=function(e){return e},o.prototype.query=function(e,t){var o=this;null!=this._request&&(n.isFunction(this._request.abort)&&this._request.abort(),this._request=null);var i=n.extend({type:"GET"},this.ajaxOptions);function a(){var a=i.transport(i,function(i){var a=o.processResults(i,e);o.options.get("debug")&&window.console&&console.error&&(a&&a.results&&n.isArray(a.results)||console.error("Select2: The AJAX results did not return an array in the `results` key of the response.")),t(a),o.container.focusOnActiveElement()},function(){a.status&&"0"===a.status||o.trigger("results:message",{message:"errorLoading"})});o._request=a}"function"==typeof i.url&&(i.url=i.url.call(this.$element,e)),"function"==typeof i.data&&(i.data=i.data.call(this.$element,e)),this.ajaxOptions.delay&&null!=e.term?(this._queryTimeout&&window.clearTimeout(this._queryTimeout),this._queryTimeout=window.setTimeout(a,this.ajaxOptions.delay)):a()},o}),t.define("select2/data/tags",["jquery"],function(e){function t(t,n,o){var i=o.get("tags"),a=o.get("createTag");void 0!==a&&(this.createTag=a);var r=o.get("insertTag");if(void 0!==r&&(this.insertTag=r),t.call(this,n,o),e.isArray(i))for(var s=0;s<i.length;s++){var c=i[s],l=this._normalizeItem(c),d=this.option(l);this.$element.append(d)}}return t.prototype.query=function(e,t,n){var o=this;this._removeOldTags(),null!=t.term&&null==t.page?e.call(this,t,function e(i,a){for(var r=i.results,s=0;s<r.length;s++){var c=r[s],l=null!=c.children&&!e({results:c.children},!0);if((c.text||"").toUpperCase()===(t.term||"").toUpperCase()||l)return!a&&(i.data=r,void n(i))}if(a)return!0;var d=o.createTag(t);if(null!=d){var u=o.option(d);u.attr("data-select2-tag",!0),o.addOptions([u]),o.insertTag(r,d)}i.results=r,n(i)}):e.call(this,t,n)},t.prototype.createTag=function(t,n){var o=e.trim(n.term);return""===o?null:{id:o,text:o}},t.prototype.insertTag=function(e,t,n){t.unshift(n)},t.prototype._removeOldTags=function(t){this._lastTag,this.$element.find("option[data-select2-tag]").each(function(){this.selected||e(this).remove()})},t}),t.define("select2/data/tokenizer",["jquery"],function(e){function t(e,t,n){var o=n.get("tokenizer");void 0!==o&&(this.tokenizer=o),e.call(this,t,n)}return t.prototype.bind=function(e,t,n){e.call(this,t,n),this.$search=t.dropdown.$search||t.selection.$search||n.find(".select2-search__field")},t.prototype.query=function(t,n,o){var i=this;n.term=n.term||"";var a=this.tokenizer(n,this.options,function(t){var n=i._normalizeItem(t);if(!i.$element.find("option").filter(function(){return e(this).val()===n.id}).length){var o=i.option(n);o.attr("data-select2-tag",!0),i._removeOldTags(),i.addOptions([o])}!function(e){i.trigger("select",{data:e})}(n)});a.term!==n.term&&(this.$search.length&&(this.$search.val(a.term),this.$search.focus()),n.term=a.term),t.call(this,n,o)},t.prototype.tokenizer=function(t,n,o,i){for(var a=o.get("tokenSeparators")||[],r=n.term,s=0,c=this.createTag||function(e){return{id:e.term,text:e.term}};s<r.length;){var l=r[s];if(-1!==e.inArray(l,a)){var d=r.substr(0,s),u=c(e.extend({},n,{term:d}));null!=u?(i(u),r=r.substr(s+1)||"",s=0):s++}else s++}return{term:r}},t}),t.define("select2/data/minimumInputLength",[],function(){function e(e,t,n){this.minimumInputLength=n.get("minimumInputLength"),e.call(this,t,n)}return e.prototype.query=function(e,t,n){t.term=t.term||"",t.term.length<this.minimumInputLength?this.trigger("results:message",{message:"inputTooShort",args:{minimum:this.minimumInputLength,input:t.term,params:t}}):e.call(this,t,n)},e}),t.define("select2/data/maximumInputLength",[],function(){function e(e,t,n){this.maximumInputLength=n.get("maximumInputLength"),e.call(this,t,n)}return e.prototype.query=function(e,t,n){t.term=t.term||"",this.maximumInputLength>0&&t.term.length>this.maximumInputLength?this.trigger("results:message",{message:"inputTooLong",args:{maximum:this.maximumInputLength,input:t.term,params:t}}):e.call(this,t,n)},e}),t.define("select2/data/maximumSelectionLength",[],function(){function e(e,t,n){this.maximumSelectionLength=n.get("maximumSelectionLength"),e.call(this,t,n)}return e.prototype.query=function(e,t,n){var o=this;this.current(function(i){var a=null!=i?i.length:0;o.maximumSelectionLength>0&&a>=o.maximumSelectionLength?o.trigger("results:message",{message:"maximumSelected",args:{maximum:o.maximumSelectionLength}}):e.call(o,t,n)})},e}),t.define("select2/dropdown",["jquery","./utils"],function(e,t){function n(e,t){this.$element=e,this.options=t,n.__super__.constructor.call(this)}return t.Extend(n,t.Observable),n.prototype.render=function(){var t=e('<span class="select2-dropdown"><span class="select2-results"></span></span>');return t.attr("dir",this.options.get("dir")),this.$dropdown=t,t},n.prototype.bind=function(){},n.prototype.position=function(e,t){},n.prototype.destroy=function(){this.$dropdown.remove()},n}),t.define("select2/dropdown/search",["jquery","../utils"],function(e,t){function n(){}return n.prototype.render=function(t){var n=t.call(this),o=e('<span class="select2-search select2-search--dropdown"><input class="select2-search__field" type="text" tabindex="-1" autocomplete="off" autocorrect="off" autocapitalize="none" spellcheck="false" role="combobox" aria-autocomplete="list" aria-expanded="true" /></span>');return this.$searchContainer=o,this.$search=o.find("input"),n.prepend(o),n},n.prototype.bind=function(t,n,o){var i=this,a=n.id+"-results";t.call(this,n,o),this.$search.on("keydown",function(e){i.trigger("keypress",e),i._keyUpPrevented=e.isDefaultPrevented()}),this.$search.on("input",function(t){e(this).off("keyup")}),this.$search.on("keyup input",function(e){i.handleSearch(e)}),n.on("open",function(){i.$search.attr("tabindex",0),i.$search.attr("aria-owns",a),i.$search.focus(),window.setTimeout(function(){i.$search.focus()},0)}),n.on("close",function(){i.$search.attr("tabindex",-1),i.$search.removeAttr("aria-activedescendant"),i.$search.removeAttr("aria-owns"),i.$search.val("")}),n.on("focus",function(){n.isOpen()||i.$search.focus()}),n.on("results:all",function(e){null!=e.query.term&&""!==e.query.term||(i.showSearch(e)?i.$searchContainer.removeClass("select2-search--hide"):i.$searchContainer.addClass("select2-search--hide"))}),n.on("results:focus",function(e){i.$search.attr("aria-activedescendant",e.data._resultId)})},n.prototype.handleSearch=function(e){if(!this._keyUpPrevented){var t=this.$search.val();this.trigger("query",{term:t})}this._keyUpPrevented=!1},n.prototype.showSearch=function(e,t){return!0},n}),t.define("select2/dropdown/hidePlaceholder",[],function(){function e(e,t,n,o){this.placeholder=this.normalizePlaceholder(n.get("placeholder")),e.call(this,t,n,o)}return e.prototype.append=function(e,t){t.results=this.removePlaceholder(t.results),e.call(this,t)},e.prototype.normalizePlaceholder=function(e,t){return"string"==typeof t&&(t={id:"",text:t}),t},e.prototype.removePlaceholder=function(e,t){for(var n=t.slice(0),o=t.length-1;o>=0;o--){var i=t[o];this.placeholder.id===i.id&&n.splice(o,1)}return n},e}),t.define("select2/dropdown/infiniteScroll",["jquery"],function(e){function t(e,t,n,o){this.lastParams={},e.call(this,t,n,o),this.$loadingMore=this.createLoadingMore(),this.loading=!1}return t.prototype.append=function(e,t){this.$loadingMore.remove(),this.loading=!1,e.call(this,t),this.showLoadingMore(t)&&this.$results.append(this.$loadingMore)},t.prototype.bind=function(t,n,o){var i=this;t.call(this,n,o),n.on("query",function(e){i.lastParams=e,i.loading=!0}),n.on("query:append",function(e){i.lastParams=e,i.loading=!0}),this.$results.on("scroll",function(){var t=e.contains(document.documentElement,i.$loadingMore[0]);!i.loading&&t&&i.$results.offset().top+i.$results.outerHeight(!1)+50>=i.$loadingMore.offset().top+i.$loadingMore.outerHeight(!1)&&i.loadMore()})},t.prototype.loadMore=function(){this.loading=!0;var t=e.extend({},{page:1},this.lastParams);t.page++,this.trigger("query:append",t)},t.prototype.showLoadingMore=function(e,t){return t.pagination&&t.pagination.more},t.prototype.createLoadingMore=function(){var t=e('<li class="select2-results__option select2-results__option--load-more"role="option" aria-disabled="true"></li>'),n=this.options.get("translations").get("loadingMore");return t.html(n(this.lastParams)),t},t}),t.define("select2/dropdown/attachBody",["jquery","../utils"],function(e,t){function n(t,n,o){this.$dropdownParent=o.get("dropdownParent")||e(document.body),t.call(this,n,o)}return n.prototype.bind=function(e,t,n){var o=this,i=!1;e.call(this,t,n),t.on("open",function(){o._showDropdown(),o._attachPositioningHandler(t),i||(i=!0,t.on("results:all",function(){o._positionDropdown(),o._resizeDropdown()}),t.on("results:append",function(){o._positionDropdown(),o._resizeDropdown()}))}),t.on("close",function(){o._hideDropdown(),o._detachPositioningHandler(t)}),this.$dropdownContainer.on("mousedown",function(e){e.stopPropagation()})},n.prototype.destroy=function(e){e.call(this),this.$dropdownContainer.remove()},n.prototype.position=function(e,t,n){t.attr("class",n.attr("class")),t.removeClass("select2"),t.addClass("select2-container--open"),t.css({position:"absolute",top:-999999}),this.$container=n},n.prototype.render=function(t){var n=e("<span></span>"),o=t.call(this);return n.append(o),this.$dropdownContainer=n,n},n.prototype._hideDropdown=function(e){this.$dropdownContainer.detach()},n.prototype._attachPositioningHandler=function(n,o){var i=this,a="scroll.select2."+o.id,r="resize.select2."+o.id,s="orientationchange.select2."+o.id,c=this.$container.parents().filter(t.hasScroll);c.each(function(){e(this).data("select2-scroll-position",{x:e(this).scrollLeft(),y:e(this).scrollTop()})}),c.on(a,function(t){var n=e(this).data("select2-scroll-position");e(this).scrollTop(n.y)}),e(window).on(a+" "+r+" "+s,function(e){i._positionDropdown(),i._resizeDropdown()})},n.prototype._detachPositioningHandler=function(n,o){var i="scroll.select2."+o.id,a="resize.select2."+o.id,r="orientationchange.select2."+o.id;this.$container.parents().filter(t.hasScroll).off(i),e(window).off(i+" "+a+" "+r)},n.prototype._positionDropdown=function(){var t=e(window),n=this.$dropdown.hasClass("select2-dropdown--above"),o=this.$dropdown.hasClass("select2-dropdown--below"),i=null,a=this.$container.offset();a.bottom=a.top+this.$container.outerHeight(!1);var r={height:this.$container.outerHeight(!1)};r.top=a.top,r.bottom=a.top+r.height;var s=this.$dropdown.outerHeight(!1),c=t.scrollTop(),l=t.scrollTop()+t.height(),d=c<a.top-s,u=l>a.bottom+s,p={left:a.left,top:r.bottom},h=this.$dropdownParent;"static"===h.css("position")&&(h=h.offsetParent());var f=h.offset();p.top-=f.top,p.left-=f.left,n||o||(i="below"),u||!d||n?!d&&u&&n&&(i="below"):i="above",("above"==i||n&&"below"!==i)&&(p.top=r.top-f.top-s),null!=i&&(this.$dropdown.removeClass("select2-dropdown--below select2-dropdown--above").addClass("select2-dropdown--"+i),this.$container.removeClass("select2-container--below select2-container--above").addClass("select2-container--"+i)),this.$dropdownContainer.css(p)},n.prototype._resizeDropdown=function(){var e={width:this.$container.outerWidth(!1)+"px"};this.options.get("dropdownAutoWidth")&&(e.minWidth=e.width,e.position="relative",e.width="auto"),this.$dropdown.css(e)},n.prototype._showDropdown=function(e){this.$dropdownContainer.appendTo(this.$dropdownParent),this._positionDropdown(),this._resizeDropdown()},n}),t.define("select2/dropdown/minimumResultsForSearch",[],function(){function e(t){for(var n=0,o=0;o<t.length;o++){var i=t[o];i.children?n+=e(i.children):n++}return n}function t(e,t,n,o){this.minimumResultsForSearch=n.get("minimumResultsForSearch"),this.minimumResultsForSearch<0&&(this.minimumResultsForSearch=1/0),e.call(this,t,n,o)}return t.prototype.showSearch=function(t,n){return!(e(n.data.results)<this.minimumResultsForSearch)&&t.call(this,n)},t}),t.define("select2/dropdown/selectOnClose",[],function(){function e(){}return e.prototype.bind=function(e,t,n){var o=this;e.call(this,t,n),t.on("close",function(e){o._handleSelectOnClose(e)})},e.prototype._handleSelectOnClose=function(e,t){if(t&&null!=t.originalSelect2Event){var n=t.originalSelect2Event;if("select"===n._type||"unselect"===n._type)return}var o=this.getHighlightedResults();if(!(o.length<1)){var i=o.data("data");null!=i.element&&i.element.selected||null==i.element&&i.selected||this.trigger("select",{data:i})}},e}),t.define("select2/dropdown/closeOnSelect",[],function(){function e(){}return e.prototype.bind=function(e,t,n){var o=this;e.call(this,t,n),t.on("select",function(e){o._selectTriggered(e)}),t.on("unselect",function(e){o._selectTriggered(e)})},e.prototype._selectTriggered=function(e,t){var n=t.originalEvent;n&&n.ctrlKey||this.trigger("close",{originalEvent:n,originalSelect2Event:t})},e}),t.define("select2/i18n/en",[],function(){return{errorLoading:function(){return"The results could not be loaded."},inputTooLong:function(e){var t=e.input.length-e.maximum,n="Please delete "+t+" character";return 1!=t&&(n+="s"),n},inputTooShort:function(e){return"Please enter "+(e.minimum-e.input.length)+" or more characters"},loadingMore:function(){return"Loading more results…"},maximumSelected:function(e){var t="You can only select "+e.maximum+" item";return 1!=e.maximum&&(t+="s"),t},noResults:function(){return"No results found"},searching:function(){return"Searching…"}}}),t.define("select2/defaults",["jquery","require","./results","./selection/single","./selection/multiple","./selection/placeholder","./selection/allowClear","./selection/search","./selection/eventRelay","./utils","./translation","./diacritics","./data/select","./data/array","./data/ajax","./data/tags","./data/tokenizer","./data/minimumInputLength","./data/maximumInputLength","./data/maximumSelectionLength","./dropdown","./dropdown/search","./dropdown/hidePlaceholder","./dropdown/infiniteScroll","./dropdown/attachBody","./dropdown/minimumResultsForSearch","./dropdown/selectOnClose","./dropdown/closeOnSelect","./i18n/en"],function(e,t,n,o,i,a,r,s,c,l,d,u,p,h,f,m,g,_,v,w,y,b,C,x,D,k,$,A,j){function M(){this.reset()}return M.prototype.apply=function(u){if(null==(u=e.extend(!0,{},this.defaults,u)).dataAdapter){if(null!=u.ajax?u.dataAdapter=f:null!=u.data?u.dataAdapter=h:u.dataAdapter=p,u.minimumInputLength>0&&(u.dataAdapter=l.Decorate(u.dataAdapter,_)),u.maximumInputLength>0&&(u.dataAdapter=l.Decorate(u.dataAdapter,v)),u.maximumSelectionLength>0&&(u.dataAdapter=l.Decorate(u.dataAdapter,w)),u.tags&&(u.dataAdapter=l.Decorate(u.dataAdapter,m)),null==u.tokenSeparators&&null==u.tokenizer||(u.dataAdapter=l.Decorate(u.dataAdapter,g)),null!=u.query){var j=t(u.amdBase+"compat/query");u.dataAdapter=l.Decorate(u.dataAdapter,j)}if(null!=u.initSelection){var M=t(u.amdBase+"compat/initSelection");u.dataAdapter=l.Decorate(u.dataAdapter,M)}}if(null==u.resultsAdapter&&(u.resultsAdapter=n,null!=u.ajax&&(u.resultsAdapter=l.Decorate(u.resultsAdapter,x)),null!=u.placeholder&&(u.resultsAdapter=l.Decorate(u.resultsAdapter,C)),u.selectOnClose&&(u.resultsAdapter=l.Decorate(u.resultsAdapter,$))),null==u.dropdownAdapter){if(u.multiple)u.dropdownAdapter=y;else{var E=l.Decorate(y,b);u.dropdownAdapter=E}if(0!==u.minimumResultsForSearch&&(u.dropdownAdapter=l.Decorate(u.dropdownAdapter,k)),u.closeOnSelect&&(u.dropdownAdapter=l.Decorate(u.dropdownAdapter,A)),null!=u.dropdownCssClass||null!=u.dropdownCss||null!=u.adaptDropdownCssClass){var P=t(u.amdBase+"compat/dropdownCss");u.dropdownAdapter=l.Decorate(u.dropdownAdapter,P)}u.dropdownAdapter=l.Decorate(u.dropdownAdapter,D)}if(null==u.selectionAdapter){if(u.multiple?u.selectionAdapter=i:u.selectionAdapter=o,null!=u.placeholder&&(u.selectionAdapter=l.Decorate(u.selectionAdapter,a)),u.allowClear&&(u.selectionAdapter=l.Decorate(u.selectionAdapter,r)),u.multiple&&(u.selectionAdapter=l.Decorate(u.selectionAdapter,s)),null!=u.containerCssClass||null!=u.containerCss||null!=u.adaptContainerCssClass){var T=t(u.amdBase+"compat/containerCss");u.selectionAdapter=l.Decorate(u.selectionAdapter,T)}u.selectionAdapter=l.Decorate(u.selectionAdapter,c)}if("string"==typeof u.language)if(u.language.indexOf("-")>0){var S=u.language.split("-")[0];u.language=[u.language,S]}else u.language=[u.language];if(e.isArray(u.language)){var O=new d;u.language.push("en");for(var I=u.language,L=0;L<I.length;L++){var H=I[L],W={};try{W=d.loadPath(H)}catch(e){try{H=this.defaults.amdLanguageBase+H,W=d.loadPath(H)}catch(e){u.debug&&window.console&&console.warn&&console.warn('Select2: The language file for "'+H+'" could not be automatically loaded. A fallback will be used instead.');continue}}O.extend(W)}u.translations=O}else{var N=d.loadPath(this.defaults.amdLanguageBase+"en"),F=new d(u.language);F.extend(N),u.translations=F}return u},M.prototype.reset=function(){function t(e){return e.replace(/[^\u0000-\u007E]/g,function(e){return u[e]||e})}this.defaults={amdBase:"./",amdLanguageBase:"./i18n/",closeOnSelect:!0,debug:!1,dropdownAutoWidth:!1,escapeMarkup:l.escapeMarkup,language:j,matcher:function n(o,i){if(""===e.trim(o.term))return i;if(i.children&&i.children.length>0){for(var a=e.extend(!0,{},i),r=i.children.length-1;r>=0;r--)null==n(o,i.children[r])&&a.children.splice(r,1);return a.children.length>0?a:n(o,a)}var s=t(i.text).toUpperCase(),c=t(o.term).toUpperCase();return s.indexOf(c)>-1?i:null},minimumInputLength:0,maximumInputLength:0,maximumSelectionLength:0,minimumResultsForSearch:0,selectOnClose:!1,sorter:function(e){return e},templateResult:function(e){return e.text},templateSelection:function(e){return e.text},theme:"default",width:"resolve"}},M.prototype.set=function(t,n){var o={};o[e.camelCase(t)]=n;var i=l._convertData(o);e.extend(this.defaults,i)},new M}),t.define("select2/options",["require","jquery","./defaults","./utils"],function(e,t,n,o){function i(t,i){if(this.options=t,null!=i&&this.fromElement(i),this.options=n.apply(this.options),i&&i.is("input")){var a=e(this.get("amdBase")+"compat/inputData");this.options.dataAdapter=o.Decorate(this.options.dataAdapter,a)}}return i.prototype.fromElement=function(e){var n=["select2"];null==this.options.multiple&&(this.options.multiple=e.prop("multiple")),null==this.options.disabled&&(this.options.disabled=e.prop("disabled")),null==this.options.language&&(e.prop("lang")?this.options.language=e.prop("lang").toLowerCase():e.closest("[lang]").prop("lang")&&(this.options.language=e.closest("[lang]").prop("lang"))),null==this.options.dir&&(e.prop("dir")?this.options.dir=e.prop("dir"):e.closest("[dir]").prop("dir")?this.options.dir=e.closest("[dir]").prop("dir"):this.options.dir="ltr"),e.prop("disabled",this.options.disabled),e.prop("multiple",this.options.multiple),e.data("select2Tags")&&(this.options.debug&&window.console&&console.warn&&console.warn('Select2: The `data-select2-tags` attribute has been changed to use the `data-data` and `data-tags="true"` attributes and will be removed in future versions of Select2.'),e.data("data",e.data("select2Tags")),e.data("tags",!0)),e.data("ajaxUrl")&&(this.options.debug&&window.console&&console.warn&&console.warn("Select2: The `data-ajax-url` attribute has been changed to `data-ajax--url` and support for the old attribute will be removed in future versions of Select2."),e.attr("ajax--url",e.data("ajaxUrl")),e.data("ajax--url",e.data("ajaxUrl")));var i;i=t.fn.jquery&&"1."==t.fn.jquery.substr(0,2)&&e[0].dataset?t.extend(!0,{},e[0].dataset,e.data()):e.data();var a=t.extend(!0,{},i);for(var r in a=o._convertData(a))t.inArray(r,n)>-1||(t.isPlainObject(this.options[r])?t.extend(this.options[r],a[r]):this.options[r]=a[r]);return this},i.prototype.get=function(e){return this.options[e]},i.prototype.set=function(e,t){this.options[e]=t},i}),t.define("select2/core",["jquery","./options","./utils","./keys"],function(e,t,n,o){var i=function(e,n){null!=e.data("select2")&&e.data("select2").destroy(),this.$element=e,this.id=this._generateId(e),n=n||{},this.options=new t(n,e),i.__super__.constructor.call(this);var o=e.attr("tabindex")||0;e.data("old-tabindex",o),e.attr("tabindex","-1");var a=this.options.get("dataAdapter");this.dataAdapter=new a(e,this.options);var r=this.render();this._placeContainer(r);var s=this.options.get("selectionAdapter");this.selection=new s(e,this.options),this.$selection=this.selection.render(),this.selection.position(this.$selection,r);var c=this.options.get("dropdownAdapter");this.dropdown=new c(e,this.options),this.$dropdown=this.dropdown.render(),this.dropdown.position(this.$dropdown,r);var l=this.options.get("resultsAdapter");this.results=new l(e,this.options,this.dataAdapter),this.$results=this.results.render(),this.results.position(this.$results,this.$dropdown);var d=this;this._bindAdapters(),this._registerDomEvents(),this._registerDataEvents(),this._registerSelectionEvents(),this._registerDropdownEvents(),this._registerResultsEvents(),this._registerEvents(),this.dataAdapter.current(function(e){d.trigger("selection:update",{data:e})}),e.addClass("select2-hidden-accessible"),e.attr("aria-hidden","true"),this._syncAttributes(),e.data("select2",this)};return n.Extend(i,n.Observable),i.prototype._generateId=function(e){return"select2-"+(null!=e.attr("id")?e.attr("id"):null!=e.attr("name")?e.attr("name")+"-"+n.generateChars(2):n.generateChars(4)).replace(/(:|\.|\[|\]|,)/g,"")},i.prototype._placeContainer=function(e){e.insertAfter(this.$element);var t=this._resolveWidth(this.$element,this.options.get("width"));null!=t&&e.css("width",t)},i.prototype._resolveWidth=function(e,t){var n=/^width:(([-+]?([0-9]*\.)?[0-9]+)(px|em|ex|%|in|cm|mm|pt|pc))/i;if("resolve"==t){var o=this._resolveWidth(e,"style");return null!=o?o:this._resolveWidth(e,"element")}if("element"==t){var i=e.outerWidth(!1);return i<=0?"auto":i+"px"}if("style"==t){var a=e.attr("style");if("string"!=typeof a)return null;for(var r=a.split(";"),s=0,c=r.length;s<c;s+=1){var l=r[s].replace(/\s/g,"").match(n);if(null!==l&&l.length>=1)return l[1]}return null}return t},i.prototype._bindAdapters=function(){this.dataAdapter.bind(this,this.$container),this.selection.bind(this,this.$container),this.dropdown.bind(this,this.$container),this.results.bind(this,this.$container)},i.prototype._registerDomEvents=function(){var t=this;this.$element.on("change.select2",function(){t.dataAdapter.current(function(e){t.trigger("selection:update",{data:e})})}),this.$element.on("focus.select2",function(e){t.trigger("focus",e)}),this._syncA=n.bind(this._syncAttributes,this),this._syncS=n.bind(this._syncSubtree,this),this.$element[0].attachEvent&&this.$element[0].attachEvent("onpropertychange",this._syncA);var o=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver;null!=o?(this._observer=new o(function(n){e.each(n,t._syncA),e.each(n,t._syncS)}),this._observer.observe(this.$element[0],{attributes:!0,childList:!0,subtree:!1})):this.$element[0].addEventListener&&(this.$element[0].addEventListener("DOMAttrModified",t._syncA,!1),this.$element[0].addEventListener("DOMNodeInserted",t._syncS,!1),this.$element[0].addEventListener("DOMNodeRemoved",t._syncS,!1))},i.prototype._registerDataEvents=function(){var e=this;this.dataAdapter.on("*",function(t,n){e.trigger(t,n)})},i.prototype._registerSelectionEvents=function(){var t=this,n=["toggle","focus"];this.selection.on("toggle",function(){t.toggleDropdown()}),this.selection.on("focus",function(e){t.focus(e)}),this.selection.on("*",function(o,i){-1===e.inArray(o,n)&&t.trigger(o,i)})},i.prototype._registerDropdownEvents=function(){var e=this;this.dropdown.on("*",function(t,n){e.trigger(t,n)})},i.prototype._registerResultsEvents=function(){var e=this;this.results.on("*",function(t,n){e.trigger(t,n)})},i.prototype._registerEvents=function(){var t=this;this.on("open",function(){t.$container.addClass("select2-container--open")}),this.on("close",function(){t.$container.removeClass("select2-container--open")}),this.on("enable",function(){t.$container.removeClass("select2-container--disabled")}),this.on("disable",function(){t.$container.addClass("select2-container--disabled")}),this.on("blur",function(){t.$container.removeClass("select2-container--focus")}),this.on("query",function(e){t.isOpen()||t.trigger("open",{}),this.dataAdapter.query(e,function(n){t.trigger("results:all",{data:n,query:e})})}),this.on("query:append",function(e){this.dataAdapter.query(e,function(n){t.trigger("results:append",{data:n,query:e})})}),this.on("open",function(){setTimeout(function(){t.focusOnActiveElement()},1)}),e(document).on("keydown",function(e){var n=e.which;if(t.isOpen()){n===o.ESC||n===o.TAB||n===o.UP&&e.altKey?(t.close(),e.preventDefault()):n===o.ENTER?(t.trigger("results:select",{}),e.preventDefault()):n===o.SPACE&&e.ctrlKey?(t.trigger("results:toggle",{}),e.preventDefault()):n===o.UP?(t.trigger("results:previous",{}),e.preventDefault()):n===o.DOWN&&(t.trigger("results:next",{}),e.preventDefault());var i=t.$dropdown.find(".select2-search__field");i.length||(i=t.$container.find(".select2-search__field")),n===o.DOWN||n===o.UP?t.focusOnActiveElement():(i.focus(),setTimeout(function(){t.focusOnActiveElement()},1e3))}else t.hasFocus()&&(n!==o.ENTER&&n!==o.SPACE&&n!==o.DOWN||(t.open(),e.preventDefault()))})},i.prototype.focusOnActiveElement=function(){this.isOpen()&&!n.isTouchscreen()&&this.$results.find("li.select2-results__option--highlighted").focus()},i.prototype._syncAttributes=function(){this.options.set("disabled",this.$element.prop("disabled")),this.options.get("disabled")?(this.isOpen()&&this.close(),this.trigger("disable",{})):this.trigger("enable",{})},i.prototype._syncSubtree=function(e,t){var n=!1,o=this;if(!e||!e.target||"OPTION"===e.target.nodeName||"OPTGROUP"===e.target.nodeName){if(t)if(t.addedNodes&&t.addedNodes.length>0)for(var i=0;i<t.addedNodes.length;i++)t.addedNodes[i].selected&&(n=!0);else t.removedNodes&&t.removedNodes.length>0&&(n=!0);else n=!0;n&&this.dataAdapter.current(function(e){o.trigger("selection:update",{data:e})})}},i.prototype.trigger=function(e,t){var n=i.__super__.trigger,o={open:"opening",close:"closing",select:"selecting",unselect:"unselecting"};if(void 0===t&&(t={}),e in o){var a=o[e],r={prevented:!1,name:e,args:t};if(n.call(this,a,r),r.prevented)return void(t.prevented=!0)}n.call(this,e,t)},i.prototype.toggleDropdown=function(){this.options.get("disabled")||(this.isOpen()?this.close():this.open())},i.prototype.open=function(){this.isOpen()||this.trigger("query",{})},i.prototype.close=function(){this.isOpen()&&this.trigger("close",{})},i.prototype.isOpen=function(){return this.$container.hasClass("select2-container--open")},i.prototype.hasFocus=function(){return this.$container.hasClass("select2-container--focus")},i.prototype.focus=function(e){this.hasFocus()||(this.$container.addClass("select2-container--focus"),this.trigger("focus",{}))},i.prototype.enable=function(e){this.options.get("debug")&&window.console&&console.warn&&console.warn('Select2: The `select2("enable")` method has been deprecated and will be removed in later Select2 versions. Use $element.prop("disabled") instead.'),null!=e&&0!==e.length||(e=[!0]);var t=!e[0];this.$element.prop("disabled",t)},i.prototype.data=function(){this.options.get("debug")&&arguments.length>0&&window.console&&console.warn&&console.warn('Select2: Data can no longer be set using `select2("data")`. You should consider setting the value instead using `$element.val()`.');var e=[];return this.dataAdapter.current(function(t){e=t}),e},i.prototype.val=function(t){if(this.options.get("debug")&&window.console&&console.warn&&console.warn('Select2: The `select2("val")` method has been deprecated and will be removed in later Select2 versions. Use $element.val() instead.'),null==t||0===t.length)return this.$element.val();var n=t[0];e.isArray(n)&&(n=e.map(n,function(e){return e.toString()})),this.$element.val(n).trigger("change")},i.prototype.destroy=function(){this.$container.remove(),this.$element[0].detachEvent&&this.$element[0].detachEvent("onpropertychange",this._syncA),null!=this._observer?(this._observer.disconnect(),this._observer=null):this.$element[0].removeEventListener&&(this.$element[0].removeEventListener("DOMAttrModified",this._syncA,!1),this.$element[0].removeEventListener("DOMNodeInserted",this._syncS,!1),this.$element[0].removeEventListener("DOMNodeRemoved",this._syncS,!1)),this._syncA=null,this._syncS=null,this.$element.off(".select2"),this.$element.attr("tabindex",this.$element.data("old-tabindex")),this.$element.removeClass("select2-hidden-accessible"),this.$element.attr("aria-hidden","false"),this.$element.removeData("select2"),this.dataAdapter.destroy(),this.selection.destroy(),this.dropdown.destroy(),this.results.destroy(),this.dataAdapter=null,this.selection=null,this.dropdown=null,this.results=null},i.prototype.render=function(){var t=e('<span class="select2 select2-container"><span class="selection"></span><span class="dropdown-wrapper" aria-hidden="true"></span></span>');return t.attr("dir",this.options.get("dir")),this.$container=t,this.$container.addClass("select2-container--"+this.options.get("theme")),t.data("element",this.$element),t},i}),t.define("jquery-mousewheel",["jquery"],function(e){return e}),t.define("jquery.select2",["jquery","jquery-mousewheel","./select2/core","./select2/defaults"],function(e,t,n,o){if(null==e.fn.selectWoo){var i=["open","close","destroy"];e.fn.selectWoo=function(t){if("object"==typeof(t=t||{}))return this.each(function(){var o=e.extend(!0,{},t);new n(e(this),o)}),this;if("string"==typeof t){var o,a=Array.prototype.slice.call(arguments,1);return this.each(function(){var n=e(this).data("select2");null==n&&window.console&&console.error&&console.error("The select2('"+t+"') method was called on an element that is not using Select2."),o=n[t].apply(n,a)}),e.inArray(t,i)>-1?this:o}throw new Error("Invalid arguments for Select2: "+t)}}return null!=e.fn.select2&&null!=e.fn.select2.defaults&&(e.fn.selectWoo.defaults=e.fn.select2.defaults),null==e.fn.selectWoo.defaults&&(e.fn.selectWoo.defaults=o),e.fn.select2=e.fn.select2||e.fn.selectWoo,n}),{define:t.define,require:t.require}}(),n=t.require("jquery.select2");return e.fn.select2.amd=t,e.fn.selectWoo.amd=t,n})?o.apply(t,i):o)||(e.exports=a)},634:()=>{(window.WPCodeTestingMode||function(e,t,n){const o={l18n:t.wpcode,init:function(){o.should_init()&&o.init_events()},should_init:()=>(o.$toggle=n(".wpcode-lite-version #wpcode-toggle-testing-mode"),o.$toggle.length>0),init_events(){o.$toggle.on("change",o.toggle)},toggle(){const e=WPCodeSVG.WPCodeIcon("lock",22,28,"0 0 22 28","iconId");n.confirm({title:e+o.l18n.testing_mode.title,content:o.l18n.testing_mode.text,closeIcon:!0,boxWidth:"560px",backgroundDismiss:!0,theme:"modern upsell-box-alert width560px",buttons:{confirm:{text:o.l18n.testing_mode.button_text,btnClass:"wpcode-btn-orange wpcode-button-large",action:function(){o.$toggle.prop("checked",!1),t.open(o.l18n.testing_mode.link,"_blank","noopener noreferrer")}}},onContentReady:function(){this.$content.parent().parent().append('<div class="footer-link"><a href="'+o.l18n.testing_mode.learn_more_link+'" target="_blank" rel="noopener noreferrer">'+o.l18n.testing_mode.learn_more_text+"</a></div>")},onClose:function(){o.$toggle.prop("checked",!1)}})}};return o}(document,window,jQuery)).init()},695:()=>{jQuery(function(e){WPCodeCodeTypePicker.init()}),window.WPCodeCodeTypePicker=function(e,t,n){const o={hidePicker:function(){o.body.removeClass("wpcode-code-type-picker-visible")},findElements:function(){o.snippet_type_dropdown=n("#wpcode_snippet_type"),o.snippet_type_holder=n("#wpcode_snippet_type-holder"),o.backdrop=n("#wpcode-code-type-picker-backdrop"),o.close=n("#wpcode-close-code-type-picker"),o.body=n("body"),o.code_type_list=n("#wpcode-code-type-list")},init:function(){o.findElements();const e=new URLSearchParams(t.location.search);o.body.hasClass("wpcode-new-snippet")&&!e.has("ai_generate")&&o.showPicker(),o.snippet_type_holder.click(function(e){e.preventDefault(),e.stopPropagation(),o.showPicker()}),o.backdrop.click(function(){o.hidePicker()}),o.close.click(function(){o.hidePicker()}),o.code_type_list.on("click",".wpcode-code-type",function(e){e.preventDefault();const t=n(this).data("code-type");o.snippet_type_dropdown.val(t).trigger("change"),n(".wpcode-code-type").removeClass("wpcode-code-type-selected"),n(this).addClass("wpcode-code-type-selected"),o.hidePicker()})},showPicker:function(){o.code_type_list.find(".wpcode-code-type").each(function(){n(this).data("code-type")===o.snippet_type_dropdown.val()?n(this).addClass("wpcode-code-type-selected"):n(this).removeClass("wpcode-code-type-selected")}),o.body.addClass("wpcode-code-type-picker-visible")}};return o}(document,window,jQuery)},719:()=>{(window.WPCodeAdminLibrary||function(e,t,n){const o={l10n:wpcode,init:function(){o.should_init()&&(o.find_elements(),o.init_preview(),o.init_ai_button())},should_init:()=>n(".wpcode-library-preview-button").length>0,find_elements(){o.library_list=n(".wpcode-items-list"),o.code_preview_use=n("#wpcode-preview-use-code"),o.code_preview_edit=n("#wpcode-preview-edit-snippet"),o.code_preview_updated=n("#wpcode-preview-updated"),o.code_preview_title=n("#wpcode-preview-title")},init_preview(){o.library_list.on("click",".wpcode-library-preview-button",function(e){e.preventDefault();const t=n(this).parent().find(".wpcode-item-use-button"),i=n(this).closest(".wpcode-list-item").data("id"),a=n(this).closest(".wpcode-items-list").data("type");o.show_code_preview(i,t.attr("href"),a),o.code_preview_use.text(t.text())}),n(".wpcode-close-modal, .wpcode-modal-overlay").on("click",function(){n("body").removeClass("wpcode-show-modal")})},show_code_preview(e,t,i="library"){const a=("library"===i?o.l10n.library.snippets:o.l10n.library.mysnippets).find(t=>t.library_id===e);WPCodeAdminCodeEditor.switch_code_mode("wpcode-code-preview",a.code_type),WPCodeAdminCodeEditor.set_value("wpcode-code-preview",a.code),o.code_preview_use.attr("href",t),o.code_preview_title.text(a.title),"mylibrary"===i?(n(".wpcode-my-library-buttons").show(),o.code_preview_edit.attr("href",o.l10n.cloud_edit_url+e)):n(".wpcode-my-library-buttons").hide(),o.code_preview_updated&&o.code_preview_updated.text(a.updated_text),n("body").addClass("wpcode-show-modal"),WPCodeAdminCodeEditor.refresh("wpcode-code-preview")},init_ai_button(){n(".wpcode-library-item-ai-not-available").on("click",function(e){e.preventDefault(),e.stopPropagation(),WPCodeAdminNotices.show_pro_notice(o.l10n.ai_title,o.l10n.ai_text,o.l10n.ai_url,o.l10n.ai_button)})}};return o}(document,window,jQuery)).init()},790:()=>{const e=window.WPCodeShortcodeLocations||function(e,t,n){const o={init:function(){if(!wpcode||!wpcode.nonce)return;const t=e.getElementById("wpcode-find-locations");t&&o.bindEvents(t)},bindEvents(e){e.addEventListener("click",o.handleButtonClick)},handleButtonClick(){const t=e.getElementById("wpcode-locations-list");t.innerHTML="";let o=1,i=!0;const a=()=>{i&&n.ajax({url:ajaxurl,type:"POST",data:{action:"wpcode_get_shortcode_locations",_wpnonce:wpcode.nonce,snippet_id:wpcode.snippet_id,page:o},success:function(e){e.success&&e.data?(t.innerHTML+=e.data.html,i=e.data.has_more,o++,i&&a()):(t.innerHTML='<div class="wpcode-notice wpcode-notice-error">'+(e.data?.message||wpcode.text.error)+"</div>",i=!1)},error:function(){t.innerHTML='<div class="wpcode-notice wpcode-notice-error">'+wpcode.text.error+"</div>",i=!1}})};a()}};return o}(document,window,jQuery);document.addEventListener("DOMContentLoaded",e.init)},799:()=>{jQuery(function(e){const t=e(".wpcode-smart-tags");let n;function o(){n.removeClass("wpcode-smart-tags-open"),e(document).off("click.wpcode")}t.on("click",".wpcode-smart-tags-toggle",function(t){t.preventDefault();const i=e(this).closest(".wpcode-smart-tags");i.toggleClass("wpcode-smart-tags-open"),n=i,e(document).on("click.wpcode",function(t){e(t.target).closest(".wpcode-smart-tags").length||o()}),i.hasClass("wpcode-smart-tags-open")&&i.find(".wpcode-smart-tags-search-input").focus()}),t.on("wpcode_close_smart_tags_dropdown",o),t.on("input",".wpcode-smart-tags-search-input",function(){const t=e(this).val().toLowerCase(),n=e(this).closest(".wpcode-smart-tags-dropdown"),o=n.find(".wpcode-smart-tags-category"),i=n.find(".wpcode-smart-tags-no-results");let a=0;o.each(function(){const n=e(this),o=n.find(".wpcode-smart-tag-category-label"),i=n.find("li:not(.wpcode-smart-tag-category-label)");let r=!1;i.each(function(){const n=e(this);-1!==n.text().toLowerCase().indexOf(t)?(n.show(),r=!0,a++):n.hide()}),r?(n.show(),o.show()):n.hide()}),0===a&&""!==t?i.show():i.hide()}),t.on("wpcode_close_smart_tags_dropdown",function(){const e=n.find(".wpcode-smart-tags-search-input");e.length&&(e.val(""),e.trigger("input"))}),"undefined"!=typeof WPCodeSnippetManager&&t.on("click",".wpcode-insert-smart-tag",function(t){const n=e(this).closest(".wpcode-smart-tags-dropdown"),o=n.data("upgrade-title");if(o)return t.preventDefault(),t.stopImmediatePropagation(),WPCodeSnippetManager.show_pro_notice(o,n.data("upgrade-text"),n.data("upgrade-link"),n.data("upgrade-button")),!1})})},866:()=>{(window.WPCodeLibraryAuth||function(e,t,n){const o={i18n:t.wpcode,init:function(){o.load_elements(),o.add_event_listeners(),t.WPCodeLibraryAuth=o},load_elements(){o.auth_delete_button=n(".wpcode-delete-auth")},add_event_listeners(){n(e).on("click",".wpcode-start-auth",function(e){e.preventDefault(),o.start_auth(n(this))}),o.auth_delete_button.on("click",function(e){e.preventDefault(),o.delete_auth()}),t.addEventListener("message",e=>{e.isTrusted&&o.store_auth(e.data,e.origin)},!1)},start_auth(e){let i=t.open(o.i18n.connect_url,"_blank","location=no,width=500,height=730,scrollbars=0");null===i?n.confirm({title:"Your browser blocked the authorization window from opening. Please check your popup settings.",type:"blue",theme:"modern no-content",buttons:{ok:{text:"Ok",btnClass:"wpcode-btn-confirm",action:function(){}}}}):i.focus()},delete_auth(){const e=o.show_please_wait();n.post(ajaxurl,{action:"wpcode_library_delete_auth",_ajax_nonce:o.i18n.nonce,multisite:o.i18n.multisite},function(o){e.close(),o.success?t.location.reload():n.confirm({title:"Error!",content:"There was an issue processing your request.",buttons:{ok:function(){}}})})},store_auth(e,i){if(void 0===e.key||void 0===e.username)return;const a=void 0!==e.deploy_snippet_id?e.deploy_snippet_id:0,r=o.show_please_wait();n.post(ajaxurl,{action:"wpcode_library_store_auth",key:e.key,username:e.username,webhook_secret:e.webhook_secret,client_id:e.client_id,deploy_snippet_id:a,origin:i,_ajax_nonce:o.i18n.nonce,multisite:o.i18n.multisite},function(e){if(r.close(),e.success){let o='<div class="lds-dual-ring"></div>';n.confirm({title:e.data.title,content:e.data.text+o,closeIcon:!1,theme:"modern loader-spinner-completed",buttons:!1,boxWidth:"600px",onOpen:function(){const e=this;setTimeout(function(){e.close(),t.location.reload()},2e3)}})}})},show_please_wait:()=>n.confirm({title:o.i18n.please_wait,closeIcon:!1,content:'<div class="lds-dual-ring"></div>',boxWidth:"600px",theme:"modern loader-spinner",buttons:{close:{isHidden:!0}},onOpenBefore:function(){this.buttons.close.hide(),this.$content.parent().addClass("jconfirm-loading")},onClose:function(){this.$content.parent().removeClass("jconfirm-loading")}})};return o}(document,window,jQuery)).init()},875:()=>{(window.WPCodeAdminTools||function(e,t,n){const o={i18n:t.wpcode,init:function(){o.should_init()&&(o.find_elements(),o.init_importer(),o.init_ssl_verify(),o.init_confirm_delete_log())},should_init:()=>n("body").hasClass("wpcode-tools"),find_elements(){o.importer_button=n("#wpcode-importer-snippets-submit"),o.$import_progress=n("#wpcode-importer-process"),o.provider=n("#wpcode-importer-provider").val(),o.status_update=n("#wpcode-importer-status-update").html()},init_importer(){o.importer_button.on("click",function(e){e.preventDefault();const t=n("#wpcode-importer-snippets input:checked");if(t.length){const e=[];t.each(function(){e.push(n(this).val())}),o.import_snippets(e)}})},import_snippets(e){o.$import_progress.find(".snippet-total").text(e.length),o.$import_progress.find(".snippet-current").text("1"),n("#wpcode-importer-snippets").hide(),o.$import_progress.show(),o.import_queue=e,o.imported=0,o.import_snippet()},import_snippet(){const e=o.import_queue[0];n.post(ajaxurl,{action:"wpcode_import_snippet_"+o.provider,snippet_id:e,_wpnonce:wpcode.nonce}).done(function(e){if(e.success){o.import_queue.shift(),o.imported++;const t=n(o.status_update);t.find(".name span").text(e.data.name),t.find(".actions a").attr("href",e.data.edit),o.$import_progress.find(".status").prepend(t),o.$import_progress.find(".status").show(),0===o.import_queue.length?(o.$import_progress.find(".process-count").hide(),o.$import_progress.find(".snippets-completed").text(o.imported),o.$import_progress.find(".process-completed").show()):(o.$import_progress.find(".snippet-current").text(o.imported+1),o.import_snippet())}})},init_ssl_verify(){n(e).on("click","#wpcode-ssl-verify",function(e){e.preventDefault(),o.verify_ssl()})},verify_ssl(){const e=n("#wpcode-ssl-verify"),t=e.text(),o=e.outerWidth(),i=e.parent(),a={action:"wpcode_verify_ssl",nonce:wpcode.nonce};e.css("width",o).prop("disabled",!0).text(wpcode.testing),n.post(ajaxurl,a,function(n){console.log(n),i.find(".wpcode-alert, .wpcode-ssl-error").remove(),n.success&&e.before('<div class="wpcode-alert wpcode-alert-success">'+n.data.msg+"</div>"),!n.success&&n.data.msg&&e.before('<div class="wpcode-alert wpcode-alert-danger">'+n.data.msg+"</div>"),!n.success&&n.data.debug&&e.before('<div class="wpcode-ssl-error pre-error">'+n.data.debug+"</div>"),e.css("width",o).prop("disabled",!1).text(t)})},init_confirm_delete_log(){n(".wpcode-delete-log").on("click",function(e){return e.stopPropagation(),t.confirm(o.i18n.confirm_delete_log)})}};return o}(document,window,jQuery)).init()},960:()=>{window.WPCodePlugins=window.WPCodePlugins||function(e,t,n){const o={l18n:t.wpcode,init:function(){o.should_init()&&o.init_install()},should_init:()=>(o.$install_buttons=n(".wpcode-button-install-plugin"),o.$install_buttons.length>0),init_install(){o.$install_buttons.on("click",function(e){e.preventDefault();const t=n(this);o.install_plugin(t)})},install_plugin(e){const i=e.data("slug");i&&(o.show_button_spinner(e),n.post(ajaxurl,{action:"wpcode_install_plugin",slug:i,_wpnonce:wpcode.nonce,multisite:o.l18n.multisite},function(i){if(i.success)t.location.reload();else if(o.hide_button_spinner(e),i.data.message){const e="<div class='excl-mark'>!</div>";n.confirm({title:!1,content:e+i.data.message,type:"blue",buttons:{ok:{text:o.l18n.ok,btnClass:"wpcode-btn-confirm",action:function(){}}}})}}))},show_button_spinner(e){t.WPCodeSpinner.show_button_spinner(e)},hide_button_spinner(e){t.WPCodeSpinner.hide_button_spinner(e)}};return o}(document,window,jQuery),WPCodePlugins.init()},967:()=>{(window.WPCodeAdminSettings||function(e,t,n){const o={init:function(){o.should_init()&&(o.init_auto_height_toggle(),o.init_select2())},should_init:()=>n("body").hasClass("wpcode-settings"),init_auto_height_toggle(){const e=n("#editor_height_auto"),t=n("#wpcode-editor-height");e.on("change",function(){n(this).is(":checked")?t.prop("disabled",!0):t.prop("disabled",!1)})},init_select2(){n(".wpcode-select2").selectWoo()}};return o}(document,window,jQuery)).init()},990:()=>{"use strict";"function"!=typeof Object.assign&&(Object.assign=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];if(!e)throw TypeError("Cannot convert undefined or null to object");for(var o=function(t){t&&Object.keys(t).forEach(function(n){return e[n]=t[n]})},i=0,a=t;i<a.length;i++)o(a[i]);return e})}},t={};function n(o){var i=t[o];if(void 0!==i)return i.exports;var a=t[o]={exports:{}};return e[o].call(a.exports,a,a.exports,n),a.exports}(()=>{"use strict";n(350),n(75),n(474),n(145),n(632);const e=window.WPCodeSnippetManager||function(e,t,n){const o={editor_id:"wpcode_snippet_code",unload_set:!1,icon_lock:'<svg width="22" height="28" viewBox="0 0 22 28" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M19 9.33333H17.6666V6.66667C17.6666 2.98667 14.68 0 11 0C7.31998 0 4.33331 2.98667 4.33331 6.66667V9.33333H2.99998C1.53331 9.33333 0.333313 10.5333 0.333313 12V25.3333C0.333313 26.8 1.53331 28 2.99998 28H19C20.4666 28 21.6666 26.8 21.6666 25.3333V12C21.6666 10.5333 20.4666 9.33333 19 9.33333ZM6.99998 6.66667C6.99998 4.45333 8.78665 2.66667 11 2.66667C13.2133 2.66667 15 4.45333 15 6.66667V9.33333H6.99998V6.66667ZM19 25.3333H2.99998V12H19V25.3333ZM11 21.3333C12.4666 21.3333 13.6666 20.1333 13.6666 18.6667C13.6666 17.2 12.4666 16 11 16C9.53331 16 8.33331 17.2 8.33331 18.6667C8.33331 20.1333 9.53331 21.3333 11 21.3333Z" fill="#8A8A8A"/></svg>',l10n:wpcode,saving_height:!1,init:function(){o.should_init()&&(t.WPCodeSnippetManager=o,o.find_elements(),o.init_location_picker(),o.init_location_click(),o.prevent_multi_button_click(),o.init_snippet_type_switcher(),o.init_auto_insert_toggle(),o.init_dynamic_hide(),o.init_copy_target(),o.init_tags_picker(),o.init_metabox_toggler(),o.init_select2(),o.init_tinymce_listener(),o.unload_change_listener(),o.init_save_to_library(),o.init_custom_shortcode(),o.init_conditional_logic_notice(),o.init_device_type(),o.init_datetime_lite(),o.init_shortcode_attributes(),o.update_smart_tags_attributes(),o.restore_cursor_position(),o.maybe_highlight_error_line(),o.init_load_as_file(),o.init_compress_output(),o.init_edit_lock(),o.editor_resizer(),o.init_ai_button(),o.keyboard_shortcuts(),o.update_available_locations(o.switcher.val(),!1))},should_init:function(){return null!==e.getElementById(o.editor_id)},find_elements(){o.location_dropdown=n("#wpcode_auto_insert_location"),o.switcher=n(e.getElementById("wpcode_snippet_type")),o.$body=n("body"),o.$text_editor="undefined"!=typeof tinymce&&tinymce.get("wpcode_snippet_text"),o.$selected_location_display=n("#wpcode-selected-location-display"),o.code_type=o.switcher.val()},init_snippet_type_switcher:function(){o.switcher.on("change",function(){let e=o.switcher.val();if(n(this).find(":selected").hasClass("wpcode-pro"))return o.switcher.val(o.code_type),void("scss"===e?o.show_pro_notice(o.l10n.scss_title,o.l10n.scss_text,o.l10n.scss_url,o.l10n.scss_button).then(function(){WPCodeCodeTypePicker.showPicker()}):"blocks"===e&&o.show_pro_notice(o.l10n.blocks_title,o.l10n.blocks_text,o.l10n.blocks_url,o.l10n.blocks_button).then(function(){WPCodeCodeTypePicker.showPicker()}));if(o.set_before_unload(),t.WPCodeAdminCodeEditor.switch_code_mode(o.editor_id,n(this).val(),n(this).find(":selected").data("mode"),n(this).find(":selected").data("lint")),o.$body.removeClass("wpcode-code-type-"+o.code_type),o.$body.addClass("wpcode-code-type-"+n(this).val()),"text"===o.switcher.val()){const e=t.WPCodeAdminCodeEditor.get_value(o.editor_id);o.$text_editor?o.$text_editor.setContent(e):n("#wpcode_snippet_text").val(e)}else t.WPCodeAdminCodeEditor.refresh(o.editor_id);const i=o.switcher.val();"php"!==o.code_type&&"php"!==i||o.update_available_locations(i),o.switcher.data("previous-type",o.code_type),o.code_type=i})},update_available_locations(e,t=!0){const i=o.location_dropdown.find(".wpcode-list-item");i.removeClass("wpcode-list-item-disabled"),i.find('input[type="radio"]').prop("disabled",!1);const a=i.filter(function(){const t=n(this).data("code-type");return"all"!==t&&e!==t});if(a.length>0&&(a.addClass("wpcode-list-item-disabled"),a.find('input[type="radio"]').prop("disabled",!0)),o.location_dropdown.find(".wpcode-items-list-category").each(function(){const e=n(this).find(".wpcode-list-item");e.sort(function(e,t){return n(e).data("index")-n(t).data("index")}),n(this).html(e)}),a.detach().appendTo(o.location_dropdown.find(".wpcode-items-list-category")),t){const e=o.location_dropdown.find(".wpcode-list-item:not(.wpcode-list-item-disabled):not(.wpcode-list-item-separator)").first();e.length>0&&e.find('input[type="radio"]').prop("checked",!0).trigger("change")}},init_location_picker:function(){o.location_dropdown.on("keydown",".wpcode-list-item-location",function(e){if("Enter"===e.key||" "===e.key){const e=n(this).find('input[type="radio"]');e.prop("disabled")||(e.prop("checked",!0).trigger("change"),o.$selected_location_display.focus())}}),o.location_dropdown.on("change",'input[type="radio"]',function(e){o.location_dropdown.find(".wpcode-list-item").removeClass("wpcode-list-item-selected");const t=n(this).closest(".wpcode-list-item");t.addClass("wpcode-list-item-selected"),o.$selected_location_display.text(t.find(".wpcode-list-item-title").attr("title")),o.location_dropdown.is(":visible")&&o.close_location_dropdown()})},prevent_multi_button_click:function(){n('#wpcode-snippet-manager-form .wpcode-button[type="submit"]').click(function(e){return!e.detail||1==e.detail})},init_location_click:function(){o.location_dropdown.hide(),n("body").on("click",".wpcode-list-item",function(e){const t=n(this).find("label").first();t.data("upgrade-title")&&o.show_pro_notice(t.data("upgrade-title"),t.data("upgrade-text"),t.data("upgrade-link"),t.data("upgrade-button"))}),o.$selected_location_display.on("click, focus",function(){o.location_dropdown.slideDown(200,function(){o.init_dropdown_close()}),o.scroll_to_location_dropdown()})},init_dropdown_close:function(){n(e).on("click.wpcodelocation",function(e){n(e.target).closest("#wpcode_auto_insert_location").length||n(e.target).closest(".jconfirm-box").length||o.close_location_dropdown()})},close_location_dropdown:function(){o.location_dropdown.hide(),n(e).off("click.wpcodelocation"),o.scroll_to_location_dropdown(300),o.$selected_location_display.blur()},scroll_to_location_dropdown:function(e=100){const t=o.$selected_location_display.offset();t&&n("html, body").animate({scrollTop:t.top-e},200)},init_auto_insert_toggle:function(){const t={toggles:"",init:function(){t.toggles=n(e.querySelectorAll(".wpcode-button-toggle")),t.listen_to_switch()},listen_to_switch:function(){t.toggles.each(function(){const e=n(this).find(".wpcode-button-toggle-input");n(this).on("click",".wpcode-button",function(i){i.preventDefault(),o.set_before_unload(),e.val(n(this).val()).change(),t.make_button_active(n(this))})})},make_button_active:function(e){e.closest(".wpcode-button-toggle").find(".wpcode-button").each(function(){e.is(n(this))?n(this).removeClass("wpcode-button-secondary-inactive"):n(this).addClass("wpcode-button-secondary-inactive")})}};t.init()},init_dynamic_hide:function(){const e={init:function(){e.elements=n("[data-show-if-id]"),e.add_listeners()},add_listeners:function(){e.elements.each(function(){const t=n(this),o=t.data("show-if-id");if(""===o)return;let i=!1,a=String(t.data("show-if-value")).split(",");t.data("hide-if-value")&&(a=String(t.data("hide-if-value")).split(","),i=!0);const r=n(o);n(".wpcode-admin-page #wpbody-content").on("change",o,function(){e.maybe_hide(n(this),t,a,i)}),e.maybe_hide(r,t,a,i)})},maybe_hide:function(e,t,n,i){let a=String(e.val());if("checkbox"===e.attr("type")&&(a=e.prop("checked")?"1":"0"),"radio"===e.attr("type")&&(a=e.closest("form").find('input[name="'+e.attr("name")+'"]:checked').val()),i){if(n.indexOf(a)>=0)return void t.hide();t.show(),t.find(".wpcode-select2").length>0&&o.init_select2()}else n.indexOf(a)<0?t.hide():(t.show(),t.find(".wpcode-select2").length>0&&o.init_select2())}};e.init()},init_copy_target:function(){n(".wpcode-copy-target").on("click",function(e){e.preventDefault();const t=n(this),o=t.data("target"),i=t.data("prefix"),a=t.data("suffix"),r=n(o).val();r&&(navigator.clipboard.writeText(i+r+a),t.addClass("wpcode-show-success-icon"),setTimeout(function(){t.removeClass("wpcode-show-success-icon")},500))})},init_select2:function(){n(".wpcode-select2").selectWoo({templateResult:function(e){const t=n(e.element),o=t.data("label-pill"),i=t.data("upgrade-title"),a=t.data("upgrade-text"),r=t.data("upgrade-link"),s=t.data("upgrade-button"),c=n('<span class="wpcode-pro-pill">'+o+"</span>");return""!==i&&c.attr("data-upgrade-title",i),""!==a&&c.attr("data-upgrade-text",a),""!==r&&c.attr("data-upgrade-link",r),""!==s&&c.attr("data-upgrade-button",s),void 0!==o&&""!==o?((e=n("<span>"+e.text+"</span>")).append(c),e):e.text}}),n("body").on("click",".select2-results__options",function(e){const t=n(this).closest(".select2-results__option").find(".wpcode-pro-pill");t.length>0&&o.show_pro_notice(t.data("upgrade-title"),t.data("upgrade-text"),t.data("upgrade-link"),t.data("upgrade-button"))})},init_tags_picker:function(){const e=n(".wpcode-tags-picker");e.selectWoo({tags:!0,ajax:{url:ajaxurl,data:function(e){return{action:"ajax-tag-search",tax:"wpcode_tags",q:e.term?e.term:""}},processResults:function(e){const t=e.split(","),n=[];return t.forEach(function(e){""!==e&&n.push({id:e,text:e})}),{results:n}}}}),e.on("change",function(){o.set_before_unload();const e=n(this).data("target");n(e).val(n(this).val().join(","))})},init_metabox_toggler:function(){n(".wpcode-metabox-title").on("click",function(){n(this).parent().toggleClass("wpcode-metabox-collapsed")})},init_tinymce_listener(){if(null===o.$text_editor)return o.$text_editor=tinymce.get("wpcode_snippet_text"),void setTimeout(o.init_tinymce_listener,100);!1!==o.$text_editor?o.$text_editor.on("Paste Change input Undo Redo",function(){o.set_before_unload(),clearTimeout(o.editor_change_handler),o.editor_change_handler=setTimeout(function(){t.WPCodeAdminCodeEditor.set_value(o.editor_id,o.$text_editor.getContent())},100)}):n("#wpcode_snippet_text").on("paste change input",function(){o.set_before_unload(),clearTimeout(o.editor_change_handler),o.editor_change_handler=setTimeout(function(){t.WPCodeAdminCodeEditor.set_value(o.editor_id,n("#wpcode_snippet_text").val())},100)})},set_before_unload(){o.unload_set||(o.unload_set=!0,o.catch_unsaved_button=!0,n(t).on("beforeunload",function(){return"Are you sure?"}))},unload_change_listener(){const e=t.WPCodeAdminCodeEditor.get_editor(o.editor_id);e&&e.on("change",function(){o.set_before_unload()});const i=n("#wpcode-snippet-manager-form");i.on("change","input, select",function(){o.set_before_unload()}),i.on("submit",function(){n(t).off("beforeunload"),o.save_cursor_position()})},save_cursor_position(){const e=t.WPCodeAdminCodeEditor.get_editor(o.editor_id);e&&(o.cursor_position=e.getCursor(),localStorage.setItem("wpcode_cursor_position",JSON.stringify(o.cursor_position)),localStorage.setItem("wpcode_scroll_position",JSON.stringify(e.getScrollInfo())))},restore_cursor_position(){const e=localStorage.getItem("wpcode_cursor_position");if(e){const n=JSON.parse(e),i=localStorage.getItem("wpcode_scroll_position");localStorage.removeItem("wpcode_cursor_position");const a=t.WPCodeAdminCodeEditor.get_editor(o.editor_id);if(a&&(a.focus(),a.setCursor(n),i)){const e=JSON.parse(i);a.scrollTo(e.left,e.top)}}},init_save_to_library(){n("#wpcode_save_to_library").click(function(e){e.preventDefault(),o.show_pro_notice(o.l10n.save_to_library_title,o.l10n.save_to_library_text,o.l10n.save_to_library_url)})},init_custom_shortcode(){n("#wpcode-custom-shortcode-lite").click(function(e){e.preventDefault(),o.show_pro_notice(o.l10n.shortcode_title,o.l10n.shortcode_text,o.l10n.shortcode_url)})},init_datetime_lite(){n(".wpcode-input-datetime[readonly]").click(function(e){e.preventDefault(),o.show_pro_notice(o.l10n.datetime_title,o.l10n.datetime_text,o.l10n.datetime_url)})},init_device_type(){n(".wpcode-device-type-picker-lite label").click(function(e){e.preventDefault(),o.show_pro_notice(o.l10n.device_title,o.l10n.device_text,o.l10n.device_url)})},init_load_as_file(){n("#wpcode_snippet_as_file_option #wpcode_snippet_as_file").on("change",function(e){e.preventDefault(),n(this).prop("checked",!1),o.show_pro_notice(o.l10n.laf_title,o.l10n.laf_text,o.l10n.laf_url)})},init_compress_output(){n("#wpcode_compress_output_option #wpcode_compress_output").on("change",function(e){e.preventDefault(),n(this).prop("checked",!1),o.show_pro_notice(o.l10n.co_title,o.l10n.co_text,o.l10n.co_url)})},show_pro_notice:(e,t,n,o)=>WPCodeAdminNotices.show_pro_notice(e,t,n,o),init_conditional_logic_notice(){n("#wpcode-conditions-holder").on("change",".wpcode-cl-rule-type",function(e){const t=n(this).find(":selected");if(t.data("upgrade-title")){e.stopPropagation(),o.show_pro_notice(t.data("upgrade-title"),t.data("upgrade-text"),t.data("upgrade-link"),t.data("upgrade-button"));const i=n(this).find("option").first();n(this).val(i.attr("value")).trigger("change")}})},init_shortcode_attributes(){const e=n("#wpcode-shortcode-attributes-list ul"),t=n("#wpcode-shortcode-attribute-name"),i=n("#wpcode_shortcode_attribute_list_item_template").html();n("#wpcode_add_attribute").on("click",function(a){a.preventDefault();const r=o.sanitize_key(t.val());if(""===r)return;const s=n(i);s.find(".wpcode-shortcode-attribute-name").text(r),s.find(".wpcode-shortcode-attribute-item-input").val(r),e.append(s),t.val(""),o.update_smart_tags_attributes()}),e.on("click",".wpcode-shortcode-attribute-remove",function(e){e.preventDefault(),n(this).closest("li").remove(),o.update_smart_tags_attributes()})},sanitize_key:e=>e.replace(/[^a-z0-9_]/gi,"").toLowerCase(),update_smart_tags_attributes(){const e=n(".wpcode-shortcode-attribute-item-input"),t=n(".wpcode-smart-tags-dropdown"),i=t.find(".wpcode-smart-tags-list");if(t.find(".wpcode-attribute-smart-tag").remove(),e.length>0){let t=i.find(".wpcode-attributes-category");0===t.length&&(i.append('<ul class="wpcode-smart-tags-category wpcode-attributes-category"></ul>'),t=i.find(".wpcode-attributes-category")),t.append('<li class="wpcode-attribute-smart-tag wpcode-smart-tag-category-label">'+o.l10n.shortcode_attributes+"</li>"),e.each(function(){const e=n(this).val();t.append('<li class="wpcode-attribute-smart-tag"><button class="wpcode-insert-smart-tag" data-tag="{attr_'+e+'}" type="button"><code>{attr_'+e+"}</code> - "+e+"</button></li>")})}},maybe_highlight_error_line(){if(o.l10n.error_line<=0)return;const t=wpcode_editor[o.editor_id].codemirror;var n;t.doc.setGutterMarker(o.l10n.error_line-1,"CodeMirror-lint-markers",((n=e.createElement("div")).innerHTML='<div class="wpcode-line-error-icon"></div>',n.setAttribute("title",o.l10n.error_line_message),n)),t.doc.addLineClass(o.l10n.error_line-1,"background","wpcode-line-error-code")},init_edit_lock(){n(e).on("heartbeat-send.refresh-lock",function(e,t){t.wpcode_lock=o.l10n.snippet_id}),o.l10n.is_locked&&o.show_locked_message(o.l10n.locked_by)},show_locked_message(e){const t=WPCodeSVG.WPCodeIcon("lock",22,28,"0 0 22 28","iconId");n.confirm({title:t+o.l10n.edited+e,boxWidth:"560px",theme:"modern no-content",type:"blue",buttons:{ok:{text:o.l10n.ok,btnClass:"wpcode-btn-confirm",action:function(){}}}})},editor_resizer(){let n=e.querySelector(".wpcode-resize-handle"),i=e.querySelector(".wpcode-code-textarea"),a=0;const r=t.WPCodeAdminCodeEditor.get_editor(o.editor_id);var s,c;function l(e){a=Math.max(200,c+e.y-s)+"px",r.setSize(null,a)}function d(n){e.body.removeEventListener("mousemove",l),t.removeEventListener("mouseup",d),i.classList.remove("wpcode-resizing"),o.save_editor_height(a)}n.addEventListener("mousedown",function(n){var o;n.x,s=n.y,o=i,c=parseInt(t.getComputedStyle(o).height.replace(/px$/,"")),i.classList.add("wpcode-resizing"),e.body.addEventListener("mousemove",l),t.addEventListener("mouseup",d)})},save_editor_height(e){o.saving_height&&o.saving_height.abort(),o.saving_height=n.post(ajaxurl,{action:"wpcode_save_editor_height",height:e,_wpnonce:o.l10n.nonce})},init_ai_button(){n(".wpcode-button-ai-not-available").on("click",function(e){e.preventDefault(),e.stopPropagation(),WPCodeAdminNotices.show_pro_notice(o.l10n.ai_improve_title,o.l10n.ai_text,o.l10n.ai_improve_url,o.l10n.ai_button)})},keyboard_shortcuts(){var i={};n(e).on("keydown",function(e){var t=/Mac/.test(navigator.userAgent),n=!t&&e.ctrlKey,o=t&&e.metaKey;if(n||o){var a=String.fromCharCode(e.which).toUpperCase();i.hasOwnProperty(a)&&(e.preventDefault(),i[a](e))}});const a=t.WPCodeAdminCodeEditor.get_editor(o.editor_id);var r;r=function(e){a.hasFocus()&&n("#wpcode-snippet-manager-form").submit()},i["S".toUpperCase()]=r}};return o}(document,window,jQuery);jQuery(function(){e.init()}),n(18);var t=["onChange","onClose","onDayCreate","onDestroy","onKeyDown","onMonthChange","onOpen","onParseConfig","onReady","onValueUpdate","onYearChange","onPreCalendarPosition"],o={_disable:[],allowInput:!1,allowInvalidPreload:!1,altFormat:"F j, Y",altInput:!1,altInputClass:"form-control input",animate:"object"==typeof window&&-1===window.navigator.userAgent.indexOf("MSIE"),ariaDateFormat:"F j, Y",autoFillDefaultTime:!0,clickOpens:!0,closeOnSelect:!0,conjunction:", ",dateFormat:"Y-m-d",defaultHour:12,defaultMinute:0,defaultSeconds:0,disable:[],disableMobile:!1,enableSeconds:!1,enableTime:!1,errorHandler:function(e){return"undefined"!=typeof console&&console.warn(e)},getWeek:function(e){var t=new Date(e.getTime());t.setHours(0,0,0,0),t.setDate(t.getDate()+3-(t.getDay()+6)%7);var n=new Date(t.getFullYear(),0,4);return 1+Math.round(((t.getTime()-n.getTime())/864e5-3+(n.getDay()+6)%7)/7)},hourIncrement:1,ignoredFocusElements:[],inline:!1,locale:"default",minuteIncrement:5,mode:"single",monthSelectorType:"dropdown",nextArrow:"<svg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 17 17'><g></g><path d='M13.207 8.472l-7.854 7.854-0.707-0.707 7.146-7.146-7.146-7.148 0.707-0.707 7.854 7.854z' /></svg>",noCalendar:!1,now:new Date,onChange:[],onClose:[],onDayCreate:[],onDestroy:[],onKeyDown:[],onMonthChange:[],onOpen:[],onParseConfig:[],onReady:[],onValueUpdate:[],onYearChange:[],onPreCalendarPosition:[],plugins:[],position:"auto",positionElement:void 0,prevArrow:"<svg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 17 17'><g></g><path d='M5.207 8.471l7.146 7.147-0.707 0.707-7.853-7.854 7.854-7.853 0.707 0.707-7.147 7.146z' /></svg>",shorthandCurrentMonth:!1,showMonths:1,static:!1,time_24hr:!1,weekNumbers:!1,wrap:!1},i={weekdays:{shorthand:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],longhand:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},months:{shorthand:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],longhand:["January","February","March","April","May","June","July","August","September","October","November","December"]},daysInMonth:[31,28,31,30,31,30,31,31,30,31,30,31],firstDayOfWeek:0,ordinal:function(e){var t=e%100;if(t>3&&t<21)return"th";switch(t%10){case 1:return"st";case 2:return"nd";case 3:return"rd";default:return"th"}},rangeSeparator:" to ",weekAbbreviation:"Wk",scrollTitle:"Scroll to increment",toggleTitle:"Click to toggle",amPM:["AM","PM"],yearAriaLabel:"Year",monthAriaLabel:"Month",hourAriaLabel:"Hour",minuteAriaLabel:"Minute",time_24hr:!1};const a=i;var r=function(e,t){return void 0===t&&(t=2),("000"+e).slice(-1*t)},s=function(e){return!0===e?1:0};function c(e,t){var n;return function(){var o=this,i=arguments;clearTimeout(n),n=setTimeout(function(){return e.apply(o,i)},t)}}var l=function(e){return e instanceof Array?e:[e]};function d(e,t,n){if(!0===n)return e.classList.add(t);e.classList.remove(t)}function u(e,t,n){var o=window.document.createElement(e);return t=t||"",n=n||"",o.className=t,void 0!==n&&(o.textContent=n),o}function p(e){for(;e.firstChild;)e.removeChild(e.firstChild)}function h(e,t){return t(e)?e:e.parentNode?h(e.parentNode,t):void 0}function f(e,t){var n=u("div","numInputWrapper"),o=u("input","numInput "+e),i=u("span","arrowUp"),a=u("span","arrowDown");if(-1===navigator.userAgent.indexOf("MSIE 9.0")?o.type="number":(o.type="text",o.pattern="\\d*"),void 0!==t)for(var r in t)o.setAttribute(r,t[r]);return n.appendChild(o),n.appendChild(i),n.appendChild(a),n}function m(e){try{return"function"==typeof e.composedPath?e.composedPath()[0]:e.target}catch(t){return e.target}}var g=function(){},_=function(e,t,n){return n.months[t?"shorthand":"longhand"][e]},v={D:g,F:function(e,t,n){e.setMonth(n.months.longhand.indexOf(t))},G:function(e,t){e.setHours((e.getHours()>=12?12:0)+parseFloat(t))},H:function(e,t){e.setHours(parseFloat(t))},J:function(e,t){e.setDate(parseFloat(t))},K:function(e,t,n){e.setHours(e.getHours()%12+12*s(new RegExp(n.amPM[1],"i").test(t)))},M:function(e,t,n){e.setMonth(n.months.shorthand.indexOf(t))},S:function(e,t){e.setSeconds(parseFloat(t))},U:function(e,t){return new Date(1e3*parseFloat(t))},W:function(e,t,n){var o=parseInt(t),i=new Date(e.getFullYear(),0,2+7*(o-1),0,0,0,0);return i.setDate(i.getDate()-i.getDay()+n.firstDayOfWeek),i},Y:function(e,t){e.setFullYear(parseFloat(t))},Z:function(e,t){return new Date(t)},d:function(e,t){e.setDate(parseFloat(t))},h:function(e,t){e.setHours((e.getHours()>=12?12:0)+parseFloat(t))},i:function(e,t){e.setMinutes(parseFloat(t))},j:function(e,t){e.setDate(parseFloat(t))},l:g,m:function(e,t){e.setMonth(parseFloat(t)-1)},n:function(e,t){e.setMonth(parseFloat(t)-1)},s:function(e,t){e.setSeconds(parseFloat(t))},u:function(e,t){return new Date(parseFloat(t))},w:g,y:function(e,t){e.setFullYear(2e3+parseFloat(t))}},w={D:"",F:"",G:"(\\d\\d|\\d)",H:"(\\d\\d|\\d)",J:"(\\d\\d|\\d)\\w+",K:"",M:"",S:"(\\d\\d|\\d)",U:"(.+)",W:"(\\d\\d|\\d)",Y:"(\\d{4})",Z:"(.+)",d:"(\\d\\d|\\d)",h:"(\\d\\d|\\d)",i:"(\\d\\d|\\d)",j:"(\\d\\d|\\d)",l:"",m:"(\\d\\d|\\d)",n:"(\\d\\d|\\d)",s:"(\\d\\d|\\d)",u:"(.+)",w:"(\\d\\d|\\d)",y:"(\\d{2})"},y={Z:function(e){return e.toISOString()},D:function(e,t,n){return t.weekdays.shorthand[y.w(e,t,n)]},F:function(e,t,n){return _(y.n(e,t,n)-1,!1,t)},G:function(e,t,n){return r(y.h(e,t,n))},H:function(e){return r(e.getHours())},J:function(e,t){return void 0!==t.ordinal?e.getDate()+t.ordinal(e.getDate()):e.getDate()},K:function(e,t){return t.amPM[s(e.getHours()>11)]},M:function(e,t){return _(e.getMonth(),!0,t)},S:function(e){return r(e.getSeconds())},U:function(e){return e.getTime()/1e3},W:function(e,t,n){return n.getWeek(e)},Y:function(e){return r(e.getFullYear(),4)},d:function(e){return r(e.getDate())},h:function(e){return e.getHours()%12?e.getHours()%12:12},i:function(e){return r(e.getMinutes())},j:function(e){return e.getDate()},l:function(e,t){return t.weekdays.longhand[e.getDay()]},m:function(e){return r(e.getMonth()+1)},n:function(e){return e.getMonth()+1},s:function(e){return e.getSeconds()},u:function(e){return e.getTime()},w:function(e){return e.getDay()},y:function(e){return String(e.getFullYear()).substring(2)}},b=function(e){var t=e.config,n=void 0===t?o:t,a=e.l10n,r=void 0===a?i:a,s=e.isMobile,c=void 0!==s&&s;return function(e,t,o){var i=o||r;return void 0===n.formatDate||c?t.split("").map(function(t,o,a){return y[t]&&"\\"!==a[o-1]?y[t](e,i,n):"\\"!==t?t:""}).join(""):n.formatDate(e,t,i)}},C=function(e){var t=e.config,n=void 0===t?o:t,a=e.l10n,r=void 0===a?i:a;return function(e,t,i,a){if(0===e||e){var s,c=a||r,l=e;if(e instanceof Date)s=new Date(e.getTime());else if("string"!=typeof e&&void 0!==e.toFixed)s=new Date(e);else if("string"==typeof e){var d=t||(n||o).dateFormat,u=String(e).trim();if("today"===u)s=new Date,i=!0;else if(n&&n.parseDate)s=n.parseDate(e,d);else if(/Z$/.test(u)||/GMT$/.test(u))s=new Date(e);else{for(var p=void 0,h=[],f=0,m=0,g="";f<d.length;f++){var _=d[f],y="\\"===_,b="\\"===d[f-1]||y;if(w[_]&&!b){g+=w[_];var C=new RegExp(g).exec(e);C&&(p=!0)&&h["Y"!==_?"push":"unshift"]({fn:v[_],val:C[++m]})}else y||(g+=".")}s=n&&n.noCalendar?new Date((new Date).setHours(0,0,0,0)):new Date((new Date).getFullYear(),0,1,0,0,0,0),h.forEach(function(e){var t=e.fn,n=e.val;return s=t(s,n,c)||s}),s=p?s:void 0}}if(s instanceof Date&&!isNaN(s.getTime()))return!0===i&&s.setHours(0,0,0,0),s;n.errorHandler(new Error("Invalid date provided: "+l))}}};function x(e,t,n){return void 0===n&&(n=!0),!1!==n?new Date(e.getTime()).setHours(0,0,0,0)-new Date(t.getTime()).setHours(0,0,0,0):e.getTime()-t.getTime()}var D=function(e,t,n){return 3600*e+60*t+n};function k(e){var t=e.defaultHour,n=e.defaultMinute,o=e.defaultSeconds;if(void 0!==e.minDate){var i=e.minDate.getHours(),a=e.minDate.getMinutes(),r=e.minDate.getSeconds();t<i&&(t=i),t===i&&n<a&&(n=a),t===i&&n===a&&o<r&&(o=e.minDate.getSeconds())}if(void 0!==e.maxDate){var s=e.maxDate.getHours(),c=e.maxDate.getMinutes();(t=Math.min(t,s))===s&&(n=Math.min(c,n)),t===s&&n===c&&(o=e.maxDate.getSeconds())}return{hours:t,minutes:n,seconds:o}}n(990);var $=function(){return $=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},$.apply(this,arguments)},A=function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var o=Array(e),i=0;for(t=0;t<n;t++)for(var a=arguments[t],r=0,s=a.length;r<s;r++,i++)o[i]=a[r];return o};function j(e,n){var i={config:$($({},o),E.defaultConfig),l10n:a};function g(){var e;return(null===(e=i.calendarContainer)||void 0===e?void 0:e.getRootNode()).activeElement||document.activeElement}function v(e){return e.bind(i)}function y(){var e=i.config;!1===e.weekNumbers&&1===e.showMonths||!0!==e.noCalendar&&window.requestAnimationFrame(function(){if(void 0!==i.calendarContainer&&(i.calendarContainer.style.visibility="hidden",i.calendarContainer.style.display="block"),void 0!==i.daysContainer){var t=(i.days.offsetWidth+1)*e.showMonths;i.daysContainer.style.width=t+"px",i.calendarContainer.style.width=t+(void 0!==i.weekWrapper?i.weekWrapper.offsetWidth:0)+"px",i.calendarContainer.style.removeProperty("visibility"),i.calendarContainer.style.removeProperty("display")}})}function j(e){if(0===i.selectedDates.length){var t=void 0===i.config.minDate||x(new Date,i.config.minDate)>=0?new Date:new Date(i.config.minDate.getTime()),n=k(i.config);t.setHours(n.hours,n.minutes,n.seconds,t.getMilliseconds()),i.selectedDates=[t],i.latestSelectedDateObj=t}void 0!==e&&"blur"!==e.type&&function(e){e.preventDefault();var t="keydown"===e.type,n=m(e),o=n;void 0!==i.amPM&&n===i.amPM&&(i.amPM.textContent=i.l10n.amPM[s(i.amPM.textContent===i.l10n.amPM[0])]);var a=parseFloat(o.getAttribute("min")),c=parseFloat(o.getAttribute("max")),l=parseFloat(o.getAttribute("step")),d=parseInt(o.value,10),u=d+l*(e.delta||(t?38===e.which?1:-1:0));if(void 0!==o.value&&2===o.value.length){var p=o===i.hourElement,h=o===i.minuteElement;u<a?(u=c+u+s(!p)+(s(p)&&s(!i.amPM)),h&&W(void 0,-1,i.hourElement)):u>c&&(u=o===i.hourElement?u-c-s(!i.amPM):a,h&&W(void 0,1,i.hourElement)),i.amPM&&p&&(1===l?u+d===23:Math.abs(u-d)>l)&&(i.amPM.textContent=i.l10n.amPM[s(i.amPM.textContent===i.l10n.amPM[0])]),o.value=r(u)}}(e);var o=i._input.value;M(),Ce(),i._input.value!==o&&i._debouncedChange()}function M(){if(void 0!==i.hourElement&&void 0!==i.minuteElement){var e,t,n=(parseInt(i.hourElement.value.slice(-2),10)||0)%24,o=(parseInt(i.minuteElement.value,10)||0)%60,a=void 0!==i.secondElement?(parseInt(i.secondElement.value,10)||0)%60:0;void 0!==i.amPM&&(e=n,t=i.amPM.textContent,n=e%12+12*s(t===i.l10n.amPM[1]));var r=void 0!==i.config.minTime||i.config.minDate&&i.minDateHasTime&&i.latestSelectedDateObj&&0===x(i.latestSelectedDateObj,i.config.minDate,!0),c=void 0!==i.config.maxTime||i.config.maxDate&&i.maxDateHasTime&&i.latestSelectedDateObj&&0===x(i.latestSelectedDateObj,i.config.maxDate,!0);if(void 0!==i.config.maxTime&&void 0!==i.config.minTime&&i.config.minTime>i.config.maxTime){var l=D(i.config.minTime.getHours(),i.config.minTime.getMinutes(),i.config.minTime.getSeconds()),d=D(i.config.maxTime.getHours(),i.config.maxTime.getMinutes(),i.config.maxTime.getSeconds()),u=D(n,o,a);if(u>d&&u<l){var p=function(e){var t=Math.floor(e/3600),n=(e-3600*t)/60;return[t,n,e-3600*t-60*n]}(l);n=p[0],o=p[1],a=p[2]}}else{if(c){var h=void 0!==i.config.maxTime?i.config.maxTime:i.config.maxDate;(n=Math.min(n,h.getHours()))===h.getHours()&&(o=Math.min(o,h.getMinutes())),o===h.getMinutes()&&(a=Math.min(a,h.getSeconds()))}if(r){var f=void 0!==i.config.minTime?i.config.minTime:i.config.minDate;(n=Math.max(n,f.getHours()))===f.getHours()&&o<f.getMinutes()&&(o=f.getMinutes()),o===f.getMinutes()&&(a=Math.max(a,f.getSeconds()))}}T(n,o,a)}}function P(e){var t=e||i.latestSelectedDateObj;t&&t instanceof Date&&T(t.getHours(),t.getMinutes(),t.getSeconds())}function T(e,t,n){void 0!==i.latestSelectedDateObj&&i.latestSelectedDateObj.setHours(e%24,t,n||0,0),i.hourElement&&i.minuteElement&&!i.isMobile&&(i.hourElement.value=r(i.config.time_24hr?e:(12+e)%12+12*s(e%12==0)),i.minuteElement.value=r(t),void 0!==i.amPM&&(i.amPM.textContent=i.l10n.amPM[s(e>=12)]),void 0!==i.secondElement&&(i.secondElement.value=r(n)))}function S(e){var t=m(e),n=parseInt(t.value)+(e.delta||0);(n/1e3>1||"Enter"===e.key&&!/[^\d]/.test(n.toString()))&&X(n)}function O(e,t,n,o){return t instanceof Array?t.forEach(function(t){return O(e,t,n,o)}):e instanceof Array?e.forEach(function(e){return O(e,t,n,o)}):(e.addEventListener(t,n,o),void i._handlers.push({remove:function(){return e.removeEventListener(t,n,o)}}))}function I(){_e("onChange")}function L(e,t){var n=void 0!==e?i.parseDate(e):i.latestSelectedDateObj||(i.config.minDate&&i.config.minDate>i.now?i.config.minDate:i.config.maxDate&&i.config.maxDate<i.now?i.config.maxDate:i.now),o=i.currentYear,a=i.currentMonth;try{void 0!==n&&(i.currentYear=n.getFullYear(),i.currentMonth=n.getMonth())}catch(e){e.message="Invalid date supplied: "+n,i.config.errorHandler(e)}t&&i.currentYear!==o&&(_e("onYearChange"),z()),!t||i.currentYear===o&&i.currentMonth===a||_e("onMonthChange"),i.redraw()}function H(e){var t=m(e);~t.className.indexOf("arrow")&&W(e,t.classList.contains("arrowUp")?1:-1)}function W(e,t,n){var o=e&&m(e),i=n||o&&o.parentNode&&o.parentNode.firstChild,a=ve("increment");a.delta=t,i&&i.dispatchEvent(a)}function N(e,t,n,o){var a=ee(t,!0),r=u("span",e,t.getDate().toString());return r.dateObj=t,r.$i=o,r.setAttribute("aria-label",i.formatDate(t,i.config.ariaDateFormat)),-1===e.indexOf("hidden")&&0===x(t,i.now)&&(i.todayDateElem=r,r.classList.add("today"),r.setAttribute("aria-current","date")),a?(r.tabIndex=-1,we(t)&&(r.classList.add("selected"),i.selectedDateElem=r,"range"===i.config.mode&&(d(r,"startRange",i.selectedDates[0]&&0===x(t,i.selectedDates[0],!0)),d(r,"endRange",i.selectedDates[1]&&0===x(t,i.selectedDates[1],!0)),"nextMonthDay"===e&&r.classList.add("inRange")))):r.classList.add("flatpickr-disabled"),"range"===i.config.mode&&function(e){return!("range"!==i.config.mode||i.selectedDates.length<2)&&x(e,i.selectedDates[0])>=0&&x(e,i.selectedDates[1])<=0}(t)&&!we(t)&&r.classList.add("inRange"),i.weekNumbers&&1===i.config.showMonths&&"prevMonthDay"!==e&&o%7==6&&i.weekNumbers.insertAdjacentHTML("beforeend","<span class='flatpickr-day'>"+i.config.getWeek(t)+"</span>"),_e("onDayCreate",r),r}function F(e){e.focus(),"range"===i.config.mode&&ie(e)}function Y(e){for(var t=e>0?0:i.config.showMonths-1,n=e>0?i.config.showMonths:-1,o=t;o!=n;o+=e)for(var a=i.daysContainer.children[o],r=e>0?0:a.children.length-1,s=e>0?a.children.length:-1,c=r;c!=s;c+=e){var l=a.children[c];if(-1===l.className.indexOf("hidden")&&ee(l.dateObj))return l}}function B(e,t){var n=g(),o=te(n||document.body),a=void 0!==e?e:o?n:void 0!==i.selectedDateElem&&te(i.selectedDateElem)?i.selectedDateElem:void 0!==i.todayDateElem&&te(i.todayDateElem)?i.todayDateElem:Y(t>0?1:-1);void 0===a?i._input.focus():o?function(e,t){for(var n=-1===e.className.indexOf("Month")?e.dateObj.getMonth():i.currentMonth,o=t>0?i.config.showMonths:-1,a=t>0?1:-1,r=n-i.currentMonth;r!=o;r+=a)for(var s=i.daysContainer.children[r],c=n-i.currentMonth===r?e.$i+t:t<0?s.children.length-1:0,l=s.children.length,d=c;d>=0&&d<l&&d!=(t>0?l:-1);d+=a){var u=s.children[d];if(-1===u.className.indexOf("hidden")&&ee(u.dateObj)&&Math.abs(e.$i-d)>=Math.abs(t))return F(u)}i.changeMonth(a),B(Y(a),0)}(a,t):F(a)}function R(e,t){for(var n=(new Date(e,t,1).getDay()-i.l10n.firstDayOfWeek+7)%7,o=i.utils.getDaysInMonth((t-1+12)%12,e),a=i.utils.getDaysInMonth(t,e),r=window.document.createDocumentFragment(),s=i.config.showMonths>1,c=s?"prevMonthDay hidden":"prevMonthDay",l=s?"nextMonthDay hidden":"nextMonthDay",d=o+1-n,p=0;d<=o;d++,p++)r.appendChild(N("flatpickr-day "+c,new Date(e,t-1,d),0,p));for(d=1;d<=a;d++,p++)r.appendChild(N("flatpickr-day",new Date(e,t,d),0,p));for(var h=a+1;h<=42-n&&(1===i.config.showMonths||p%7!=0);h++,p++)r.appendChild(N("flatpickr-day "+l,new Date(e,t+1,h%a),0,p));var f=u("div","dayContainer");return f.appendChild(r),f}function q(){if(void 0!==i.daysContainer){p(i.daysContainer),i.weekNumbers&&p(i.weekNumbers);for(var e=document.createDocumentFragment(),t=0;t<i.config.showMonths;t++){var n=new Date(i.currentYear,i.currentMonth,1);n.setMonth(i.currentMonth+t),e.appendChild(R(n.getFullYear(),n.getMonth()))}i.daysContainer.appendChild(e),i.days=i.daysContainer.firstChild,"range"===i.config.mode&&1===i.selectedDates.length&&ie()}}function z(){if(!(i.config.showMonths>1||"dropdown"!==i.config.monthSelectorType)){var e=function(e){return!(void 0!==i.config.minDate&&i.currentYear===i.config.minDate.getFullYear()&&e<i.config.minDate.getMonth()||void 0!==i.config.maxDate&&i.currentYear===i.config.maxDate.getFullYear()&&e>i.config.maxDate.getMonth())};i.monthsDropdownContainer.tabIndex=-1,i.monthsDropdownContainer.innerHTML="";for(var t=0;t<12;t++)if(e(t)){var n=u("option","flatpickr-monthDropdown-month");n.value=new Date(i.currentYear,t).getMonth().toString(),n.textContent=_(t,i.config.shorthandCurrentMonth,i.l10n),n.tabIndex=-1,i.currentMonth===t&&(n.selected=!0),i.monthsDropdownContainer.appendChild(n)}}}function U(){var e,t=u("div","flatpickr-month"),n=window.document.createDocumentFragment();i.config.showMonths>1||"static"===i.config.monthSelectorType?e=u("span","cur-month"):(i.monthsDropdownContainer=u("select","flatpickr-monthDropdown-months"),i.monthsDropdownContainer.setAttribute("aria-label",i.l10n.monthAriaLabel),O(i.monthsDropdownContainer,"change",function(e){var t=m(e),n=parseInt(t.value,10);i.changeMonth(n-i.currentMonth),_e("onMonthChange")}),z(),e=i.monthsDropdownContainer);var o=f("cur-year",{tabindex:"-1"}),a=o.getElementsByTagName("input")[0];a.setAttribute("aria-label",i.l10n.yearAriaLabel),i.config.minDate&&a.setAttribute("min",i.config.minDate.getFullYear().toString()),i.config.maxDate&&(a.setAttribute("max",i.config.maxDate.getFullYear().toString()),a.disabled=!!i.config.minDate&&i.config.minDate.getFullYear()===i.config.maxDate.getFullYear());var r=u("div","flatpickr-current-month");return r.appendChild(e),r.appendChild(o),n.appendChild(r),t.appendChild(n),{container:t,yearElement:a,monthElement:e}}function K(){p(i.monthNav),i.monthNav.appendChild(i.prevMonthNav),i.config.showMonths&&(i.yearElements=[],i.monthElements=[]);for(var e=i.config.showMonths;e--;){var t=U();i.yearElements.push(t.yearElement),i.monthElements.push(t.monthElement),i.monthNav.appendChild(t.container)}i.monthNav.appendChild(i.nextMonthNav)}function V(){i.weekdayContainer?p(i.weekdayContainer):i.weekdayContainer=u("div","flatpickr-weekdays");for(var e=i.config.showMonths;e--;){var t=u("div","flatpickr-weekdaycontainer");i.weekdayContainer.appendChild(t)}return Q(),i.weekdayContainer}function Q(){if(i.weekdayContainer){var e=i.l10n.firstDayOfWeek,t=A(i.l10n.weekdays.shorthand);e>0&&e<t.length&&(t=A(t.splice(e,t.length),t.splice(0,e)));for(var n=i.config.showMonths;n--;)i.weekdayContainer.children[n].innerHTML="\n      <span class='flatpickr-weekday'>\n        "+t.join("</span><span class='flatpickr-weekday'>")+"\n      </span>\n      "}}function G(e,t){void 0===t&&(t=!0);var n=t?e:e-i.currentMonth;n<0&&!0===i._hidePrevMonthArrow||n>0&&!0===i._hideNextMonthArrow||(i.currentMonth+=n,(i.currentMonth<0||i.currentMonth>11)&&(i.currentYear+=i.currentMonth>11?1:-1,i.currentMonth=(i.currentMonth+12)%12,_e("onYearChange"),z()),q(),_e("onMonthChange"),ye())}function J(e){return i.calendarContainer.contains(e)}function Z(e){if(i.isOpen&&!i.config.inline){var t=m(e),n=J(t),o=!(t===i.input||t===i.altInput||i.element.contains(t)||e.path&&e.path.indexOf&&(~e.path.indexOf(i.input)||~e.path.indexOf(i.altInput))||n||J(e.relatedTarget)),a=!i.config.ignoredFocusElements.some(function(e){return e.contains(t)});o&&a&&(i.config.allowInput&&i.setDate(i._input.value,!1,i.config.altInput?i.config.altFormat:i.config.dateFormat),void 0!==i.timeContainer&&void 0!==i.minuteElement&&void 0!==i.hourElement&&""!==i.input.value&&void 0!==i.input.value&&j(),i.close(),i.config&&"range"===i.config.mode&&1===i.selectedDates.length&&i.clear(!1))}}function X(e){if(!(!e||i.config.minDate&&e<i.config.minDate.getFullYear()||i.config.maxDate&&e>i.config.maxDate.getFullYear())){var t=e,n=i.currentYear!==t;i.currentYear=t||i.currentYear,i.config.maxDate&&i.currentYear===i.config.maxDate.getFullYear()?i.currentMonth=Math.min(i.config.maxDate.getMonth(),i.currentMonth):i.config.minDate&&i.currentYear===i.config.minDate.getFullYear()&&(i.currentMonth=Math.max(i.config.minDate.getMonth(),i.currentMonth)),n&&(i.redraw(),_e("onYearChange"),z())}}function ee(e,t){var n;void 0===t&&(t=!0);var o=i.parseDate(e,void 0,t);if(i.config.minDate&&o&&x(o,i.config.minDate,void 0!==t?t:!i.minDateHasTime)<0||i.config.maxDate&&o&&x(o,i.config.maxDate,void 0!==t?t:!i.maxDateHasTime)>0)return!1;if(!i.config.enable&&0===i.config.disable.length)return!0;if(void 0===o)return!1;for(var a=!!i.config.enable,r=null!==(n=i.config.enable)&&void 0!==n?n:i.config.disable,s=0,c=void 0;s<r.length;s++){if("function"==typeof(c=r[s])&&c(o))return a;if(c instanceof Date&&void 0!==o&&c.getTime()===o.getTime())return a;if("string"==typeof c){var l=i.parseDate(c,void 0,!0);return l&&l.getTime()===o.getTime()?a:!a}if("object"==typeof c&&void 0!==o&&c.from&&c.to&&o.getTime()>=c.from.getTime()&&o.getTime()<=c.to.getTime())return a}return!a}function te(e){return void 0!==i.daysContainer&&-1===e.className.indexOf("hidden")&&-1===e.className.indexOf("flatpickr-disabled")&&i.daysContainer.contains(e)}function ne(e){var t=e.target===i._input,n=i._input.value.trimEnd()!==be();!t||!n||e.relatedTarget&&J(e.relatedTarget)||i.setDate(i._input.value,!0,e.target===i.altInput?i.config.altFormat:i.config.dateFormat)}function oe(t){var n=m(t),o=i.config.wrap?e.contains(n):n===i._input,a=i.config.allowInput,r=i.isOpen&&(!a||!o),s=i.config.inline&&o&&!a;if(13===t.keyCode&&o){if(a)return i.setDate(i._input.value,!0,n===i.altInput?i.config.altFormat:i.config.dateFormat),i.close(),n.blur();i.open()}else if(J(n)||r||s){var c=!!i.timeContainer&&i.timeContainer.contains(n);switch(t.keyCode){case 13:c?(t.preventDefault(),j(),ue()):pe(t);break;case 27:t.preventDefault(),ue();break;case 8:case 46:o&&!i.config.allowInput&&(t.preventDefault(),i.clear());break;case 37:case 39:if(c||o)i.hourElement&&i.hourElement.focus();else{t.preventDefault();var l=g();if(void 0!==i.daysContainer&&(!1===a||l&&te(l))){var d=39===t.keyCode?1:-1;t.ctrlKey?(t.stopPropagation(),G(d),B(Y(1),0)):B(void 0,d)}}break;case 38:case 40:t.preventDefault();var u=40===t.keyCode?1:-1;i.daysContainer&&void 0!==n.$i||n===i.input||n===i.altInput?t.ctrlKey?(t.stopPropagation(),X(i.currentYear-u),B(Y(1),0)):c||B(void 0,7*u):n===i.currentYearElement?X(i.currentYear-u):i.config.enableTime&&(!c&&i.hourElement&&i.hourElement.focus(),j(t),i._debouncedChange());break;case 9:if(c){var p=[i.hourElement,i.minuteElement,i.secondElement,i.amPM].concat(i.pluginElements).filter(function(e){return e}),h=p.indexOf(n);if(-1!==h){var f=p[h+(t.shiftKey?-1:1)];t.preventDefault(),(f||i._input).focus()}}else!i.config.noCalendar&&i.daysContainer&&i.daysContainer.contains(n)&&t.shiftKey&&(t.preventDefault(),i._input.focus())}}if(void 0!==i.amPM&&n===i.amPM)switch(t.key){case i.l10n.amPM[0].charAt(0):case i.l10n.amPM[0].charAt(0).toLowerCase():i.amPM.textContent=i.l10n.amPM[0],M(),Ce();break;case i.l10n.amPM[1].charAt(0):case i.l10n.amPM[1].charAt(0).toLowerCase():i.amPM.textContent=i.l10n.amPM[1],M(),Ce()}(o||J(n))&&_e("onKeyDown",t)}function ie(e,t){if(void 0===t&&(t="flatpickr-day"),1===i.selectedDates.length&&(!e||e.classList.contains(t)&&!e.classList.contains("flatpickr-disabled"))){for(var n=e?e.dateObj.getTime():i.days.firstElementChild.dateObj.getTime(),o=i.parseDate(i.selectedDates[0],void 0,!0).getTime(),a=Math.min(n,i.selectedDates[0].getTime()),r=Math.max(n,i.selectedDates[0].getTime()),s=!1,c=0,l=0,d=a;d<r;d+=864e5)ee(new Date(d),!0)||(s=s||d>a&&d<r,d<o&&(!c||d>c)?c=d:d>o&&(!l||d<l)&&(l=d));Array.from(i.rContainer.querySelectorAll("*:nth-child(-n+"+i.config.showMonths+") > ."+t)).forEach(function(t){var a,r,d,u=t.dateObj.getTime(),p=c>0&&u<c||l>0&&u>l;if(p)return t.classList.add("notAllowed"),void["inRange","startRange","endRange"].forEach(function(e){t.classList.remove(e)});s&&!p||(["startRange","inRange","endRange","notAllowed"].forEach(function(e){t.classList.remove(e)}),void 0!==e&&(e.classList.add(n<=i.selectedDates[0].getTime()?"startRange":"endRange"),o<n&&u===o?t.classList.add("startRange"):o>n&&u===o&&t.classList.add("endRange"),u>=c&&(0===l||u<=l)&&(r=o,d=n,(a=u)>Math.min(r,d)&&a<Math.max(r,d))&&t.classList.add("inRange")))})}}function ae(){!i.isOpen||i.config.static||i.config.inline||le()}function re(e){return function(t){var n=i.config["_"+e+"Date"]=i.parseDate(t,i.config.dateFormat),o=i.config["_"+("min"===e?"max":"min")+"Date"];void 0!==n&&(i["min"===e?"minDateHasTime":"maxDateHasTime"]=n.getHours()>0||n.getMinutes()>0||n.getSeconds()>0),i.selectedDates&&(i.selectedDates=i.selectedDates.filter(function(e){return ee(e)}),i.selectedDates.length||"min"!==e||P(n),Ce()),i.daysContainer&&(de(),void 0!==n?i.currentYearElement[e]=n.getFullYear().toString():i.currentYearElement.removeAttribute(e),i.currentYearElement.disabled=!!o&&void 0!==n&&o.getFullYear()===n.getFullYear())}}function se(){return i.config.wrap?e.querySelector("[data-input]"):e}function ce(){"object"!=typeof i.config.locale&&void 0===E.l10ns[i.config.locale]&&i.config.errorHandler(new Error("flatpickr: invalid locale "+i.config.locale)),i.l10n=$($({},E.l10ns.default),"object"==typeof i.config.locale?i.config.locale:"default"!==i.config.locale?E.l10ns[i.config.locale]:void 0),w.D="("+i.l10n.weekdays.shorthand.join("|")+")",w.l="("+i.l10n.weekdays.longhand.join("|")+")",w.M="("+i.l10n.months.shorthand.join("|")+")",w.F="("+i.l10n.months.longhand.join("|")+")",w.K="("+i.l10n.amPM[0]+"|"+i.l10n.amPM[1]+"|"+i.l10n.amPM[0].toLowerCase()+"|"+i.l10n.amPM[1].toLowerCase()+")",void 0===$($({},n),JSON.parse(JSON.stringify(e.dataset||{}))).time_24hr&&void 0===E.defaultConfig.time_24hr&&(i.config.time_24hr=i.l10n.time_24hr),i.formatDate=b(i),i.parseDate=C({config:i.config,l10n:i.l10n})}function le(e){if("function"!=typeof i.config.position){if(void 0!==i.calendarContainer){_e("onPreCalendarPosition");var t=e||i._positionElement,n=Array.prototype.reduce.call(i.calendarContainer.children,function(e,t){return e+t.offsetHeight},0),o=i.calendarContainer.offsetWidth,a=i.config.position.split(" "),r=a[0],s=a.length>1?a[1]:null,c=t.getBoundingClientRect(),l=window.innerHeight-c.bottom,u="above"===r||"below"!==r&&l<n&&c.top>n,p=window.pageYOffset+c.top+(u?-n-2:t.offsetHeight+2);if(d(i.calendarContainer,"arrowTop",!u),d(i.calendarContainer,"arrowBottom",u),!i.config.inline){var h=window.pageXOffset+c.left,f=!1,m=!1;"center"===s?(h-=(o-c.width)/2,f=!0):"right"===s&&(h-=o-c.width,m=!0),d(i.calendarContainer,"arrowLeft",!f&&!m),d(i.calendarContainer,"arrowCenter",f),d(i.calendarContainer,"arrowRight",m);var g=window.document.body.offsetWidth-(window.pageXOffset+c.right),_=h+o>window.document.body.offsetWidth,v=g+o>window.document.body.offsetWidth;if(d(i.calendarContainer,"rightMost",_),!i.config.static)if(i.calendarContainer.style.top=p+"px",_)if(v){var w=function(){for(var e=null,t=0;t<document.styleSheets.length;t++){var n=document.styleSheets[t];if(n.cssRules){try{n.cssRules}catch(e){continue}e=n;break}}return null!=e?e:(o=document.createElement("style"),document.head.appendChild(o),o.sheet);var o}();if(void 0===w)return;var y=window.document.body.offsetWidth,b=Math.max(0,y/2-o/2),C=w.cssRules.length,x="{left:"+c.left+"px;right:auto;}";d(i.calendarContainer,"rightMost",!1),d(i.calendarContainer,"centerMost",!0),w.insertRule(".flatpickr-calendar.centerMost:before,.flatpickr-calendar.centerMost:after"+x,C),i.calendarContainer.style.left=b+"px",i.calendarContainer.style.right="auto"}else i.calendarContainer.style.left="auto",i.calendarContainer.style.right=g+"px";else i.calendarContainer.style.left=h+"px",i.calendarContainer.style.right="auto"}}}else i.config.position(i,e)}function de(){i.config.noCalendar||i.isMobile||(z(),ye(),q())}function ue(){i._input.focus(),-1!==window.navigator.userAgent.indexOf("MSIE")||void 0!==navigator.msMaxTouchPoints?setTimeout(i.close,0):i.close()}function pe(e){e.preventDefault(),e.stopPropagation();var t=h(m(e),function(e){return e.classList&&e.classList.contains("flatpickr-day")&&!e.classList.contains("flatpickr-disabled")&&!e.classList.contains("notAllowed")});if(void 0!==t){var n=t,o=i.latestSelectedDateObj=new Date(n.dateObj.getTime()),a=(o.getMonth()<i.currentMonth||o.getMonth()>i.currentMonth+i.config.showMonths-1)&&"range"!==i.config.mode;if(i.selectedDateElem=n,"single"===i.config.mode)i.selectedDates=[o];else if("multiple"===i.config.mode){var r=we(o);r?i.selectedDates.splice(parseInt(r),1):i.selectedDates.push(o)}else"range"===i.config.mode&&(2===i.selectedDates.length&&i.clear(!1,!1),i.latestSelectedDateObj=o,i.selectedDates.push(o),0!==x(o,i.selectedDates[0],!0)&&i.selectedDates.sort(function(e,t){return e.getTime()-t.getTime()}));if(M(),a){var s=i.currentYear!==o.getFullYear();i.currentYear=o.getFullYear(),i.currentMonth=o.getMonth(),s&&(_e("onYearChange"),z()),_e("onMonthChange")}if(ye(),q(),Ce(),a||"range"===i.config.mode||1!==i.config.showMonths?void 0!==i.selectedDateElem&&void 0===i.hourElement&&i.selectedDateElem&&i.selectedDateElem.focus():F(n),void 0!==i.hourElement&&void 0!==i.hourElement&&i.hourElement.focus(),i.config.closeOnSelect){var c="single"===i.config.mode&&!i.config.enableTime,l="range"===i.config.mode&&2===i.selectedDates.length&&!i.config.enableTime;(c||l)&&ue()}I()}}i.parseDate=C({config:i.config,l10n:i.l10n}),i._handlers=[],i.pluginElements=[],i.loadedPlugins=[],i._bind=O,i._setHoursFromDate=P,i._positionCalendar=le,i.changeMonth=G,i.changeYear=X,i.clear=function(e,t){if(void 0===e&&(e=!0),void 0===t&&(t=!0),i.input.value="",void 0!==i.altInput&&(i.altInput.value=""),void 0!==i.mobileInput&&(i.mobileInput.value=""),i.selectedDates=[],i.latestSelectedDateObj=void 0,!0===t&&(i.currentYear=i._initialDate.getFullYear(),i.currentMonth=i._initialDate.getMonth()),!0===i.config.enableTime){var n=k(i.config);T(n.hours,n.minutes,n.seconds)}i.redraw(),e&&_e("onChange")},i.close=function(){i.isOpen=!1,i.isMobile||(void 0!==i.calendarContainer&&i.calendarContainer.classList.remove("open"),void 0!==i._input&&i._input.classList.remove("active")),_e("onClose")},i.onMouseOver=ie,i._createElement=u,i.createDay=N,i.destroy=function(){void 0!==i.config&&_e("onDestroy");for(var e=i._handlers.length;e--;)i._handlers[e].remove();if(i._handlers=[],i.mobileInput)i.mobileInput.parentNode&&i.mobileInput.parentNode.removeChild(i.mobileInput),i.mobileInput=void 0;else if(i.calendarContainer&&i.calendarContainer.parentNode)if(i.config.static&&i.calendarContainer.parentNode){var t=i.calendarContainer.parentNode;if(t.lastChild&&t.removeChild(t.lastChild),t.parentNode){for(;t.firstChild;)t.parentNode.insertBefore(t.firstChild,t);t.parentNode.removeChild(t)}}else i.calendarContainer.parentNode.removeChild(i.calendarContainer);i.altInput&&(i.input.type="text",i.altInput.parentNode&&i.altInput.parentNode.removeChild(i.altInput),delete i.altInput),i.input&&(i.input.type=i.input._type,i.input.classList.remove("flatpickr-input"),i.input.removeAttribute("readonly")),["_showTimeInput","latestSelectedDateObj","_hideNextMonthArrow","_hidePrevMonthArrow","__hideNextMonthArrow","__hidePrevMonthArrow","isMobile","isOpen","selectedDateElem","minDateHasTime","maxDateHasTime","days","daysContainer","_input","_positionElement","innerContainer","rContainer","monthNav","todayDateElem","calendarContainer","weekdayContainer","prevMonthNav","nextMonthNav","monthsDropdownContainer","currentMonthElement","currentYearElement","navigationCurrentMonth","selectedDateElem","config"].forEach(function(e){try{delete i[e]}catch(e){}})},i.isEnabled=ee,i.jumpToDate=L,i.updateValue=Ce,i.open=function(e,t){if(void 0===t&&(t=i._positionElement),!0===i.isMobile){if(e){e.preventDefault();var n=m(e);n&&n.blur()}return void 0!==i.mobileInput&&(i.mobileInput.focus(),i.mobileInput.click()),void _e("onOpen")}if(!i._input.disabled&&!i.config.inline){var o=i.isOpen;i.isOpen=!0,o||(i.calendarContainer.classList.add("open"),i._input.classList.add("active"),_e("onOpen"),le(t)),!0===i.config.enableTime&&!0===i.config.noCalendar&&(!1!==i.config.allowInput||void 0!==e&&i.timeContainer.contains(e.relatedTarget)||setTimeout(function(){return i.hourElement.select()},50))}},i.redraw=de,i.set=function(e,n){if(null!==e&&"object"==typeof e)for(var o in Object.assign(i.config,e),e)void 0!==he[o]&&he[o].forEach(function(e){return e()});else i.config[e]=n,void 0!==he[e]?he[e].forEach(function(e){return e()}):t.indexOf(e)>-1&&(i.config[e]=l(n));i.redraw(),Ce(!0)},i.setDate=function(e,t,n){if(void 0===t&&(t=!1),void 0===n&&(n=i.config.dateFormat),0!==e&&!e||e instanceof Array&&0===e.length)return i.clear(t);fe(e,n),i.latestSelectedDateObj=i.selectedDates[i.selectedDates.length-1],i.redraw(),L(void 0,t),P(),0===i.selectedDates.length&&i.clear(!1),Ce(t),t&&_e("onChange")},i.toggle=function(e){if(!0===i.isOpen)return i.close();i.open(e)};var he={locale:[ce,Q],showMonths:[K,y,V],minDate:[L],maxDate:[L],positionElement:[ge],clickOpens:[function(){!0===i.config.clickOpens?(O(i._input,"focus",i.open),O(i._input,"click",i.open)):(i._input.removeEventListener("focus",i.open),i._input.removeEventListener("click",i.open))}]};function fe(e,t){var n=[];if(e instanceof Array)n=e.map(function(e){return i.parseDate(e,t)});else if(e instanceof Date||"number"==typeof e)n=[i.parseDate(e,t)];else if("string"==typeof e)switch(i.config.mode){case"single":case"time":n=[i.parseDate(e,t)];break;case"multiple":n=e.split(i.config.conjunction).map(function(e){return i.parseDate(e,t)});break;case"range":n=e.split(i.l10n.rangeSeparator).map(function(e){return i.parseDate(e,t)})}else i.config.errorHandler(new Error("Invalid date supplied: "+JSON.stringify(e)));i.selectedDates=i.config.allowInvalidPreload?n:n.filter(function(e){return e instanceof Date&&ee(e,!1)}),"range"===i.config.mode&&i.selectedDates.sort(function(e,t){return e.getTime()-t.getTime()})}function me(e){return e.slice().map(function(e){return"string"==typeof e||"number"==typeof e||e instanceof Date?i.parseDate(e,void 0,!0):e&&"object"==typeof e&&e.from&&e.to?{from:i.parseDate(e.from,void 0),to:i.parseDate(e.to,void 0)}:e}).filter(function(e){return e})}function ge(){i._positionElement=i.config.positionElement||i._input}function _e(e,t){if(void 0!==i.config){var n=i.config[e];if(void 0!==n&&n.length>0)for(var o=0;n[o]&&o<n.length;o++)n[o](i.selectedDates,i.input.value,i,t);"onChange"===e&&(i.input.dispatchEvent(ve("change")),i.input.dispatchEvent(ve("input")))}}function ve(e){var t=document.createEvent("Event");return t.initEvent(e,!0,!0),t}function we(e){for(var t=0;t<i.selectedDates.length;t++){var n=i.selectedDates[t];if(n instanceof Date&&0===x(n,e))return""+t}return!1}function ye(){i.config.noCalendar||i.isMobile||!i.monthNav||(i.yearElements.forEach(function(e,t){var n=new Date(i.currentYear,i.currentMonth,1);n.setMonth(i.currentMonth+t),i.config.showMonths>1||"static"===i.config.monthSelectorType?i.monthElements[t].textContent=_(n.getMonth(),i.config.shorthandCurrentMonth,i.l10n)+" ":i.monthsDropdownContainer.value=n.getMonth().toString(),e.value=n.getFullYear().toString()}),i._hidePrevMonthArrow=void 0!==i.config.minDate&&(i.currentYear===i.config.minDate.getFullYear()?i.currentMonth<=i.config.minDate.getMonth():i.currentYear<i.config.minDate.getFullYear()),i._hideNextMonthArrow=void 0!==i.config.maxDate&&(i.currentYear===i.config.maxDate.getFullYear()?i.currentMonth+1>i.config.maxDate.getMonth():i.currentYear>i.config.maxDate.getFullYear()))}function be(e){var t=e||(i.config.altInput?i.config.altFormat:i.config.dateFormat);return i.selectedDates.map(function(e){return i.formatDate(e,t)}).filter(function(e,t,n){return"range"!==i.config.mode||i.config.enableTime||n.indexOf(e)===t}).join("range"!==i.config.mode?i.config.conjunction:i.l10n.rangeSeparator)}function Ce(e){void 0===e&&(e=!0),void 0!==i.mobileInput&&i.mobileFormatStr&&(i.mobileInput.value=void 0!==i.latestSelectedDateObj?i.formatDate(i.latestSelectedDateObj,i.mobileFormatStr):""),i.input.value=be(i.config.dateFormat),void 0!==i.altInput&&(i.altInput.value=be(i.config.altFormat)),!1!==e&&_e("onValueUpdate")}function xe(e){var t=m(e),n=i.prevMonthNav.contains(t),o=i.nextMonthNav.contains(t);n||o?G(n?-1:1):i.yearElements.indexOf(t)>=0?t.select():t.classList.contains("arrowUp")?i.changeYear(i.currentYear+1):t.classList.contains("arrowDown")&&i.changeYear(i.currentYear-1)}return function(){i.element=i.input=e,i.isOpen=!1,function(){var a=["wrap","weekNumbers","allowInput","allowInvalidPreload","clickOpens","time_24hr","enableTime","noCalendar","altInput","shorthandCurrentMonth","inline","static","enableSeconds","disableMobile"],r=$($({},JSON.parse(JSON.stringify(e.dataset||{}))),n),s={};i.config.parseDate=r.parseDate,i.config.formatDate=r.formatDate,Object.defineProperty(i.config,"enable",{get:function(){return i.config._enable},set:function(e){i.config._enable=me(e)}}),Object.defineProperty(i.config,"disable",{get:function(){return i.config._disable},set:function(e){i.config._disable=me(e)}});var c="time"===r.mode;if(!r.dateFormat&&(r.enableTime||c)){var d=E.defaultConfig.dateFormat||o.dateFormat;s.dateFormat=r.noCalendar||c?"H:i"+(r.enableSeconds?":S":""):d+" H:i"+(r.enableSeconds?":S":"")}if(r.altInput&&(r.enableTime||c)&&!r.altFormat){var u=E.defaultConfig.altFormat||o.altFormat;s.altFormat=r.noCalendar||c?"h:i"+(r.enableSeconds?":S K":" K"):u+" h:i"+(r.enableSeconds?":S":"")+" K"}Object.defineProperty(i.config,"minDate",{get:function(){return i.config._minDate},set:re("min")}),Object.defineProperty(i.config,"maxDate",{get:function(){return i.config._maxDate},set:re("max")});var p=function(e){return function(t){i.config["min"===e?"_minTime":"_maxTime"]=i.parseDate(t,"H:i:S")}};Object.defineProperty(i.config,"minTime",{get:function(){return i.config._minTime},set:p("min")}),Object.defineProperty(i.config,"maxTime",{get:function(){return i.config._maxTime},set:p("max")}),"time"===r.mode&&(i.config.noCalendar=!0,i.config.enableTime=!0),Object.assign(i.config,s,r);for(var h=0;h<a.length;h++)i.config[a[h]]=!0===i.config[a[h]]||"true"===i.config[a[h]];for(t.filter(function(e){return void 0!==i.config[e]}).forEach(function(e){i.config[e]=l(i.config[e]||[]).map(v)}),i.isMobile=!i.config.disableMobile&&!i.config.inline&&"single"===i.config.mode&&!i.config.disable.length&&!i.config.enable&&!i.config.weekNumbers&&/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),h=0;h<i.config.plugins.length;h++){var f=i.config.plugins[h](i)||{};for(var m in f)t.indexOf(m)>-1?i.config[m]=l(f[m]).map(v).concat(i.config[m]):void 0===r[m]&&(i.config[m]=f[m])}r.altInputClass||(i.config.altInputClass=se().className+" "+i.config.altInputClass),_e("onParseConfig")}(),ce(),i.input=se(),i.input?(i.input._type=i.input.type,i.input.type="text",i.input.classList.add("flatpickr-input"),i._input=i.input,i.config.altInput&&(i.altInput=u(i.input.nodeName,i.config.altInputClass),i._input=i.altInput,i.altInput.placeholder=i.input.placeholder,i.altInput.disabled=i.input.disabled,i.altInput.required=i.input.required,i.altInput.tabIndex=i.input.tabIndex,i.altInput.type="text",i.input.setAttribute("type","hidden"),!i.config.static&&i.input.parentNode&&i.input.parentNode.insertBefore(i.altInput,i.input.nextSibling)),i.config.allowInput||i._input.setAttribute("readonly","readonly"),ge()):i.config.errorHandler(new Error("Invalid input element specified")),function(){i.selectedDates=[],i.now=i.parseDate(i.config.now)||new Date;var e=i.config.defaultDate||("INPUT"!==i.input.nodeName&&"TEXTAREA"!==i.input.nodeName||!i.input.placeholder||i.input.value!==i.input.placeholder?i.input.value:null);e&&fe(e,i.config.dateFormat),i._initialDate=i.selectedDates.length>0?i.selectedDates[0]:i.config.minDate&&i.config.minDate.getTime()>i.now.getTime()?i.config.minDate:i.config.maxDate&&i.config.maxDate.getTime()<i.now.getTime()?i.config.maxDate:i.now,i.currentYear=i._initialDate.getFullYear(),i.currentMonth=i._initialDate.getMonth(),i.selectedDates.length>0&&(i.latestSelectedDateObj=i.selectedDates[0]),void 0!==i.config.minTime&&(i.config.minTime=i.parseDate(i.config.minTime,"H:i")),void 0!==i.config.maxTime&&(i.config.maxTime=i.parseDate(i.config.maxTime,"H:i")),i.minDateHasTime=!!i.config.minDate&&(i.config.minDate.getHours()>0||i.config.minDate.getMinutes()>0||i.config.minDate.getSeconds()>0),i.maxDateHasTime=!!i.config.maxDate&&(i.config.maxDate.getHours()>0||i.config.maxDate.getMinutes()>0||i.config.maxDate.getSeconds()>0)}(),i.utils={getDaysInMonth:function(e,t){return void 0===e&&(e=i.currentMonth),void 0===t&&(t=i.currentYear),1===e&&(t%4==0&&t%100!=0||t%400==0)?29:i.l10n.daysInMonth[e]}},i.isMobile||function(){var e=window.document.createDocumentFragment();if(i.calendarContainer=u("div","flatpickr-calendar"),i.calendarContainer.tabIndex=-1,!i.config.noCalendar){if(e.appendChild((i.monthNav=u("div","flatpickr-months"),i.yearElements=[],i.monthElements=[],i.prevMonthNav=u("span","flatpickr-prev-month"),i.prevMonthNav.innerHTML=i.config.prevArrow,i.nextMonthNav=u("span","flatpickr-next-month"),i.nextMonthNav.innerHTML=i.config.nextArrow,K(),Object.defineProperty(i,"_hidePrevMonthArrow",{get:function(){return i.__hidePrevMonthArrow},set:function(e){i.__hidePrevMonthArrow!==e&&(d(i.prevMonthNav,"flatpickr-disabled",e),i.__hidePrevMonthArrow=e)}}),Object.defineProperty(i,"_hideNextMonthArrow",{get:function(){return i.__hideNextMonthArrow},set:function(e){i.__hideNextMonthArrow!==e&&(d(i.nextMonthNav,"flatpickr-disabled",e),i.__hideNextMonthArrow=e)}}),i.currentYearElement=i.yearElements[0],ye(),i.monthNav)),i.innerContainer=u("div","flatpickr-innerContainer"),i.config.weekNumbers){var t=function(){i.calendarContainer.classList.add("hasWeeks");var e=u("div","flatpickr-weekwrapper");e.appendChild(u("span","flatpickr-weekday",i.l10n.weekAbbreviation));var t=u("div","flatpickr-weeks");return e.appendChild(t),{weekWrapper:e,weekNumbers:t}}(),n=t.weekWrapper,o=t.weekNumbers;i.innerContainer.appendChild(n),i.weekNumbers=o,i.weekWrapper=n}i.rContainer=u("div","flatpickr-rContainer"),i.rContainer.appendChild(V()),i.daysContainer||(i.daysContainer=u("div","flatpickr-days"),i.daysContainer.tabIndex=-1),q(),i.rContainer.appendChild(i.daysContainer),i.innerContainer.appendChild(i.rContainer),e.appendChild(i.innerContainer)}i.config.enableTime&&e.appendChild(function(){i.calendarContainer.classList.add("hasTime"),i.config.noCalendar&&i.calendarContainer.classList.add("noCalendar");var e=k(i.config);i.timeContainer=u("div","flatpickr-time"),i.timeContainer.tabIndex=-1;var t=u("span","flatpickr-time-separator",":"),n=f("flatpickr-hour",{"aria-label":i.l10n.hourAriaLabel});i.hourElement=n.getElementsByTagName("input")[0];var o=f("flatpickr-minute",{"aria-label":i.l10n.minuteAriaLabel});if(i.minuteElement=o.getElementsByTagName("input")[0],i.hourElement.tabIndex=i.minuteElement.tabIndex=-1,i.hourElement.value=r(i.latestSelectedDateObj?i.latestSelectedDateObj.getHours():i.config.time_24hr?e.hours:function(e){switch(e%24){case 0:case 12:return 12;default:return e%12}}(e.hours)),i.minuteElement.value=r(i.latestSelectedDateObj?i.latestSelectedDateObj.getMinutes():e.minutes),i.hourElement.setAttribute("step",i.config.hourIncrement.toString()),i.minuteElement.setAttribute("step",i.config.minuteIncrement.toString()),i.hourElement.setAttribute("min",i.config.time_24hr?"0":"1"),i.hourElement.setAttribute("max",i.config.time_24hr?"23":"12"),i.hourElement.setAttribute("maxlength","2"),i.minuteElement.setAttribute("min","0"),i.minuteElement.setAttribute("max","59"),i.minuteElement.setAttribute("maxlength","2"),i.timeContainer.appendChild(n),i.timeContainer.appendChild(t),i.timeContainer.appendChild(o),i.config.time_24hr&&i.timeContainer.classList.add("time24hr"),i.config.enableSeconds){i.timeContainer.classList.add("hasSeconds");var a=f("flatpickr-second");i.secondElement=a.getElementsByTagName("input")[0],i.secondElement.value=r(i.latestSelectedDateObj?i.latestSelectedDateObj.getSeconds():e.seconds),i.secondElement.setAttribute("step",i.minuteElement.getAttribute("step")),i.secondElement.setAttribute("min","0"),i.secondElement.setAttribute("max","59"),i.secondElement.setAttribute("maxlength","2"),i.timeContainer.appendChild(u("span","flatpickr-time-separator",":")),i.timeContainer.appendChild(a)}return i.config.time_24hr||(i.amPM=u("span","flatpickr-am-pm",i.l10n.amPM[s((i.latestSelectedDateObj?i.hourElement.value:i.config.defaultHour)>11)]),i.amPM.title=i.l10n.toggleTitle,i.amPM.tabIndex=-1,i.timeContainer.appendChild(i.amPM)),i.timeContainer}()),d(i.calendarContainer,"rangeMode","range"===i.config.mode),d(i.calendarContainer,"animate",!0===i.config.animate),d(i.calendarContainer,"multiMonth",i.config.showMonths>1),i.calendarContainer.appendChild(e);var a=void 0!==i.config.appendTo&&void 0!==i.config.appendTo.nodeType;if((i.config.inline||i.config.static)&&(i.calendarContainer.classList.add(i.config.inline?"inline":"static"),i.config.inline&&(!a&&i.element.parentNode?i.element.parentNode.insertBefore(i.calendarContainer,i._input.nextSibling):void 0!==i.config.appendTo&&i.config.appendTo.appendChild(i.calendarContainer)),i.config.static)){var c=u("div","flatpickr-wrapper");i.element.parentNode&&i.element.parentNode.insertBefore(c,i.element),c.appendChild(i.element),i.altInput&&c.appendChild(i.altInput),c.appendChild(i.calendarContainer)}i.config.static||i.config.inline||(void 0!==i.config.appendTo?i.config.appendTo:window.document.body).appendChild(i.calendarContainer)}(),function(){if(i.config.wrap&&["open","close","toggle","clear"].forEach(function(e){Array.prototype.forEach.call(i.element.querySelectorAll("[data-"+e+"]"),function(t){return O(t,"click",i[e])})}),i.isMobile)!function(){var e=i.config.enableTime?i.config.noCalendar?"time":"datetime-local":"date";i.mobileInput=u("input",i.input.className+" flatpickr-mobile"),i.mobileInput.tabIndex=1,i.mobileInput.type=e,i.mobileInput.disabled=i.input.disabled,i.mobileInput.required=i.input.required,i.mobileInput.placeholder=i.input.placeholder,i.mobileFormatStr="datetime-local"===e?"Y-m-d\\TH:i:S":"date"===e?"Y-m-d":"H:i:S",i.selectedDates.length>0&&(i.mobileInput.defaultValue=i.mobileInput.value=i.formatDate(i.selectedDates[0],i.mobileFormatStr)),i.config.minDate&&(i.mobileInput.min=i.formatDate(i.config.minDate,"Y-m-d")),i.config.maxDate&&(i.mobileInput.max=i.formatDate(i.config.maxDate,"Y-m-d")),i.input.getAttribute("step")&&(i.mobileInput.step=String(i.input.getAttribute("step"))),i.input.type="hidden",void 0!==i.altInput&&(i.altInput.type="hidden");try{i.input.parentNode&&i.input.parentNode.insertBefore(i.mobileInput,i.input.nextSibling)}catch(e){}O(i.mobileInput,"change",function(e){i.setDate(m(e).value,!1,i.mobileFormatStr),_e("onChange"),_e("onClose")})}();else{var e=c(ae,50);if(i._debouncedChange=c(I,300),i.daysContainer&&!/iPhone|iPad|iPod/i.test(navigator.userAgent)&&O(i.daysContainer,"mouseover",function(e){"range"===i.config.mode&&ie(m(e))}),O(i._input,"keydown",oe),void 0!==i.calendarContainer&&O(i.calendarContainer,"keydown",oe),i.config.inline||i.config.static||O(window,"resize",e),void 0!==window.ontouchstart?O(window.document,"touchstart",Z):O(window.document,"mousedown",Z),O(window.document,"focus",Z,{capture:!0}),!0===i.config.clickOpens&&(O(i._input,"focus",i.open),O(i._input,"click",i.open)),void 0!==i.daysContainer&&(O(i.monthNav,"click",xe),O(i.monthNav,["keyup","increment"],S),O(i.daysContainer,"click",pe)),void 0!==i.timeContainer&&void 0!==i.minuteElement&&void 0!==i.hourElement){O(i.timeContainer,["increment"],j),O(i.timeContainer,"blur",j,{capture:!0}),O(i.timeContainer,"click",H),O([i.hourElement,i.minuteElement],["focus","click"],function(e){return m(e).select()}),void 0!==i.secondElement&&O(i.secondElement,"focus",function(){return i.secondElement&&i.secondElement.select()}),void 0!==i.amPM&&O(i.amPM,"click",function(e){j(e)})}i.config.allowInput&&O(i._input,"blur",ne)}}(),(i.selectedDates.length||i.config.noCalendar)&&(i.config.enableTime&&P(i.config.noCalendar?i.latestSelectedDateObj:void 0),Ce(!1)),y();var a=/^((?!chrome|android).)*safari/i.test(navigator.userAgent);!i.isMobile&&a&&le(),_e("onReady")}(),i}function M(e,t){for(var n=Array.prototype.slice.call(e).filter(function(e){return e instanceof HTMLElement}),o=[],i=0;i<n.length;i++){var a=n[i];try{if(null!==a.getAttribute("data-fp-omit"))continue;void 0!==a._flatpickr&&(a._flatpickr.destroy(),a._flatpickr=void 0),a._flatpickr=j(a,t||{}),o.push(a._flatpickr)}catch(e){console.error(e)}}return 1===o.length?o[0]:o}"undefined"!=typeof HTMLElement&&"undefined"!=typeof HTMLCollection&&"undefined"!=typeof NodeList&&(HTMLCollection.prototype.flatpickr=NodeList.prototype.flatpickr=function(e){return M(this,e)},HTMLElement.prototype.flatpickr=function(e){return M([this],e)});var E=function(e,t){return"string"==typeof e?M(window.document.querySelectorAll(e),t):e instanceof Node?M([e],t):M(e,t)};E.defaultConfig={},E.l10ns={en:$({},a),default:$({},a)},E.localize=function(e){E.l10ns.default=$($({},E.l10ns.default),e)},E.setDefaults=function(e){E.defaultConfig=$($({},E.defaultConfig),e)},E.parseDate=C({}),E.formatDate=b({}),E.compareDates=x,"undefined"!=typeof jQuery&&void 0!==jQuery.fn&&(jQuery.fn.flatpickr=function(e){return M(this,e)}),Date.prototype.fp_incr=function(e){return new Date(this.getFullYear(),this.getMonth(),this.getDate()+("string"==typeof e?parseInt(e,10):e))},"undefined"!=typeof window&&(window.flatpickr=E),(window.WPCodeConditionalLogic||function(e,t,n){const o={l10n:wpcode,current_select:null,init:function(){o.should_init()&&(o.find_elements(),o.add_events(),o.show_relations_for_all_rows(),o.maybe_switch_location())},should_init:function(){return void 0!==wpcode.conditions},find_elements:function(){o.conditions=wpcode.conditions,o.conditions_holder=n(e.getElementById("wpcode-conditions-holder")),o.conditions_input=n(e.getElementById("wpcode-cl-rules")),o.add_group_button=n(e.getElementById("wpcode-cl-add-group")),o.group_template=n(e.getElementById("wpcode-conditions-group-markup")).html(),o.row_template=n(e.getElementById("wpcode-conditions-group-row-markup")).html(),o.show_hide_input=n(e.getElementById("wpcode-cl-show-hide")),o.document=n(e),o.options_dropdown=n(e.getElementById("wpcode_cl_picker"))},add_events:function(){o.init_add_group(),o.init_add_row(),o.init_remove_row(),o.init_change_type(),o.init_capture_rules(),o.init_change_show_hide(),o.init_select2(),jQuery(function(){o.trigger_row_loaded()})},show_relations_for_all_rows(){o.conditions_holder.find(".wpcode-cl-rules-row").each(function(){o.show_hide_relation_options(n(this))})},init_add_group:function(){o.add_group_button.on("click",function(){o.add_group()})},add_group(){const e=o.get_new_group();o.conditions_holder.append(e),o.add_new_row(e.find(".wpcode-cl-group-rules")),o.build_rules_from_inputs()},get_new_group:()=>n(o.group_template),get_new_row:()=>n(o.row_template),init_add_row(){o.conditions_holder.on("click",".wpcode-cl-add-row",function(){o.add_new_row(n(this).closest(".wpcode-cl-group").find(".wpcode-cl-group-rules"))})},init_remove_row(){o.conditions_holder.on("click",".wpcode-cl-remove-row",function(){const e=n(this).closest(".wpcode-cl-group");n(this).closest(".wpcode-cl-rules-row").remove(),o.maybe_remove_group(e),o.build_rules_from_inputs(),o.options_dropdown.addClass("wpcode-hidden"),o.current_select=null})},maybe_remove_group(e){0===e.find(".wpcode-cl-group-rules .wpcode-cl-rules-row").length&&(o.options_dropdown.appendTo("body"),o.remove_group(e))},remove_group(e){e.remove(),o.build_rules_from_inputs()},add_new_row(e){const t=o.get_new_row();t.appendTo(e);const n=t.find(".wpcode-cl-rule-type");o.handle_type_change(n),o.build_rules_from_inputs(),n.focus()},init_change_type(){o.conditions_holder.on("click",".wpcode-cl-rule-type-container",function(e){e.preventDefault(),e.stopPropagation();const t=n(this).find(".wpcode-cl-rule-type");o.options_dropdown.hasClass("wpcode-hidden")||!t.is(o.current_select)?(o.options_dropdown.trigger("wpcode_select_item",t.val()),o.current_select=t,o.options_dropdown.insertAfter(t.closest(".wpcode-cl-rules-row")),o.options_dropdown.removeClass("wpcode-hidden"),o.options_dropdown.find(".wpcode-items-search-input").focus()):o.options_dropdown.addClass("wpcode-hidden")}),o.conditions_holder.on("change",".wpcode-cl-rule-type",function(){o.handle_type_change(n(this))}),o.options_dropdown.on("change",".wpcode-radio-cl-option",function(){const e=n(this).val();o.current_select.val(e).trigger("change"),o.options_dropdown.trigger("wpcode_select_item",e),o.options_dropdown.addClass("wpcode-hidden")})},init_change_show_hide(){o.show_hide_input.on("change",function(){o.build_rules_from_inputs()})},handle_type_change(e){const t=e.find("option:selected"),n=e.val(),i=t.closest("optgroup").data("type"),a=e.closest(".wpcode-cl-rules-row"),r=o.conditions[i].options[n];a.find(".wpcode-cl-rule-value").html(o.get_input_markup(r)),a.find(".wpcode-cl-rule-relation option").prop("selected",!1),o.show_hide_relation_options(a,!0),o.init_select2(),o.trigger_row_loaded(e)},get_input_markup(e){let t="";switch(e.type){case"select":t=o.get_input_select(e.options,e.multiple);break;case"text":t=o.get_input_text();break;case"ajax":t=o.get_input_ajax(e.options,e.multiple);break;case"time":t=o.get_input_time();break;case"date":t=o.get_input_date();break;case"datetime":t=o.get_input_datetime()}return t},get_input_select(e,t=!1){const o=n("<select/>");return t&&(o.attr("multiple","multiple"),o.addClass("wpcode-select2")),n.each(e,function(e,t){o.append(n("<option />",{value:t.value,disabled:!0===t.disabled}).text(t.label))}),o},get_input_text:()=>n('<input type="text" class="wpcode-input-text" />'),get_input_ajax(e,t){const o=n('<select data-action="'+e+'" class="wpcode-select2-ajax" />');return t&&o.attr("multiple","multiple"),o},get_input_time:()=>n('<input type="text" class="wpcode-input-text wpcode-input-time" />'),get_input_date:()=>n('<input type="text" class="wpcode-input-text wpcode-input-date" />'),get_input_datetime:()=>n('<input type="text" class="wpcode-input-text wpcode-input-datetime" />'),init_capture_rules(){o.conditions_holder.on("change","input,select",function(){o.build_rules_from_inputs()})},build_rules_from_inputs(){const e=o.conditions_holder.find(".wpcode-cl-group"),t=[];e.each(function(e){const i=n(this).find(".wpcode-cl-rules-row");t[e]=[],i.each(function(){const i=n(this),a=i.find(".wpcode-cl-rule-type"),r=a.find("option:selected").closest("optgroup").data("type"),s=a.val(),c={},l=o.conditions[r].options[s];if(c.type=r,c.option=s,c.relation=i.find(".wpcode-cl-rule-relation").val(),c.value=o.get_input_value(l,i),"post_meta"===s||"user_meta"===s){const e="post_meta"===s?".wpcode-cl-rule-meta-key":".wpcode-cl-rule-user-meta-key",t="post_meta"===s?"meta_key":"user_meta_key",n=i.find(e).val();c[t]=n||null}null!==c.value&&t[e].push(c)})});const i={show:o.show_hide_input.val(),groups:t};o.conditions_input.val(JSON.stringify(i))},get_input_value(e,t){let n="";switch(e.type){case"select":case"ajax":n=t.find(".wpcode-cl-rule-value select").val();break;case"text":case"time":case"date":case"datetime":n=t.find(".wpcode-cl-rule-value input").val()}return n},show_hide_relation_options(e,t=!1){const i=e.find(".wpcode-cl-rule-type"),a=i.val(),r=i.find("option:selected").closest("optgroup").data("type"),s=e.find(".wpcode-cl-rule-relation"),c=s.find("option"),l={select:["=","!="],ajax:["=","!="],text:["contains","notcontains","=","!="],date:["=","!=","before","after","before-or","after-or"],datetime:["=","!=","before","after","before-or","after-or"],time:["=","before","after","before-or","after-or"]}[o.conditions[r].options[a].type],d=o.l10n.cl_labels_custom[a];let u=null;if(c.each(function(){const e=n(this).attr("value");l.indexOf(e)>-1?(null===u&&(u=e),n(this).show(),d&&d[e]?n(this).text(d[e]):n(this).text(o.l10n.cl_labels[e])):n(this).hide()}),u&&t&&s.val(u),d&&d.placeholder){const t=e.find(".wpcode-select2-ajax"),n=e.find(".wpcode-input-text");t.length>0&&t.data("placeholder",d.placeholder),n.length>0&&n.attr("placeholder",d.placeholder)}},init_select2(){o.conditions_holder.find(".wpcode-select2").selectWoo(),o.conditions_holder.find(".wpcode-select2-ajax").selectWoo({ajax:{url:ajaxurl,data:function(e){return{action:n(this).data("action"),term:e.term,_wpnonce:o.l10n.nonce}}}})},trigger_row_loaded(t=null){n(e.body).trigger("wpcode_cl_type_loaded",{conditions_holder:o.conditions_holder,element:t})},maybe_switch_location(){const e=n('input[name="wpcode_auto_insert_location"]:checked'),t=n("#wpcode_snippet_type");n("#wpcode_conditional_logic_enable").on("change",function(){if("php"!==t.val()||"everywhere"!==e.val()||!n(this).is(":checked"))return;const i=n(this).closest(".wpcode-metabox-form-row");n('input[name="wpcode_auto_insert_location"][value="frontend_cl"]').prop("checked",!0).trigger("change"),WPCodeAdminNotices.get_notice(o.l10n.php_cl_location_notice,"notice-warning notice-alt").insertBefore(i),o.document.trigger("wp-updates-notice-added")})}};return o}(document,window,jQuery)).init(),n(209),n(197),n(719),n(132),n(609),n(429),n(615),n(875),n(405),n(332),n(512),n(327),n(866),n(967),n(799),n(634),n(596),n(960),n(695),n(790),n(481)})()})();