.gform-card__top-container-buttons {
	gap: 0.5rem;
}

.spellbook-app__search-highlight {
    background-color: rgba(144, 46, 239, 0.1);
    color: #902EEF;
    padding: 0 2px;
    border-radius: 2px;
    font-weight: 500;
}

.spellbook-plugin-settings-modal .components-modal__content {
	position: relative;
	margin: 0;
	padding: 0;
	width: 40vw;
}

.spellbook-plugin-settings-modal .components-modal__header {
	position: absolute;
	top: 0;
}

/* Hide text title in plugin settings modal */
.spellbook-plugin-settings-modal .components-modal__header-heading-container {
	display: none;
}

.registration-required-tag {
	background-color:rgb(251, 216, 208) !important;
	color: #dd301d !important;
	border: 1px solid rgb(241, 183, 170);
}

.product-card__tags {
	display: flex;
	gap: 0.5rem;
	margin-bottom: 0.5rem;
	flex-wrap: wrap;
}

.product-card__tags > button {
	cursor: pointer;
	border: none;
	background: none;
	padding: 0;
}

.product-card__tags .gform-tag {
	height: 22px;
	box-sizing: border-box;
	padding-block: 0;
	line-height: 21px;
}

.gform-card__top-container {
	display: flex;
	flex-direction: column;
}

.product-card__content {
	display: flex;
	flex-direction: column;
	min-height: 0;
	flex: 1;
}

.product-card__buttons {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
	margin-top: auto;
	align-self: flex-start;
	padding-top: 1rem;
}

.gform-card__top-container-description {
	margin: 0;
}

.product-card__header-buttons {
	display: flex;
	gap: 0.5rem;
}

.version-tag--outdated {
    background-color:rgb(249, 236, 197) !important;
    color: #a16938 !important;
	border: 1px solid rgb(233, 209, 187) !important;
    cursor: pointer;
}

.version-tag--available {
    background-color: #e1f6ed !important;
    color: #276a52 !important;
    border: 1px solid #aed9b6 !important;
    cursor: pointer;
}

.changelog-modal {
    width: 60vw !important;
}

.changelog-modal .components-modal__content {
    padding: 2rem;
}

.changelog-modal h1,
.changelog-modal h2,
.changelog-modal h3,
.changelog-modal h4 {
    margin-top: 1.5rem;
    margin-bottom: 1rem;
}

.changelog-modal h1:first-child,
.changelog-modal h2:first-child,
.changelog-modal h3:first-child,
.changelog-modal h4:first-child {
    margin-top: 0;
}

.changelog-modal ul {
    margin: 1rem 0;
    padding-left: 2rem;
}

.changelog-modal li {
    margin: 0.5rem 0;
	list-style: disc;
}

.changelog-modal code {
    background: #f5f5f5;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-size: 0.9em;
}

.gform-card--integration .gform-text.gform-text--color-dark-red {
	color: #af2718 !important;
}
