{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n* jQ<PERSON><PERSON>eater\n*\n* Easily create a section of repeatable items.\n*\n*/\n\njQuery.fn.repeater = function(options) {\n\n\tvar defaults = {\n\t\ttemplate: '',\n\t\tlimit: 5,\n\t\titems: [{}],\n\t\tsaveEvents: 'blur change',\n\t\tsaveElements: 'input, select',\n\t\taddImageSrc: '',\n\t\tremoveImageSrc: '',\n\t\tcallbacks: {\n\t\t\tsave: function() { },\n\t\t\tbeforeAdd: function() { },\n\t\t\tadd: function() { },\n\t\t\tbeforeAddNew: function() { },\n\t\t\taddNew: function() { },\n\t\t\tbeforeRemove: function() { },\n\t\t\tremove: function() { },\n\t\t\trepeaterButtons: function() { return false; }\n\t\t}\n\t}\n\n\tthis.options   = jQuery.extend( true, {}, defaults, options );\n\tthis.elem      = jQuery( this );\n\tthis.items     = this.options.items;\n\tthis.callbacks = this.options.callbacks;\n\tthis._template = this.options.template ? this.options.template : this.elem.next( '#' + this.elem.prop( 'id' ) + '-template' ).html();\n\n\t// add \"repeater\" namespace to all saveEvents\n\tvar saveEvents = this.options.saveEvents.split( ' ' );\n\n\tvar newSaveEvents = [];\n\n\tfor (i in saveEvents) {\n\t\tnewSaveEvents.push( saveEvents[i] + '.repeater' );\n\t}\n\n\tthis.options.saveEvents = newSaveEvents.join( ' ' );\n\n\tthis.init = function() {\n\n\t\t// if no template provided or in \"storage\", use current HTML\n\t\tif ( ! this._template) {\n\t\t\tthis._template = this.elem.html();\n\t\t}\n\n\t\t// move template html into \"storage\"\n\t\tthis.elem.after( '<div id=\"' + this.elem.prop( 'id' ) + '-template\" style=\"display:none\">' + this._template + '</div>' );\n\n\t\tthis.elem.addClass( 'repeater' );\n\t\tthis.elem.empty();\n\n\t\tfor (i in this.items) {\n\t\t\tthis.addItem( this.items[i], i );\n\t\t}\n\n\t\tvar repeater = this;\n\n\t\tjQuery( this.elem ).off( 'click.repeater', 'a.add-item:not(\".inactive\")' );\n\t\tjQuery( this.elem ).on('click.repeater', 'a.add-item:not(\".inactive\")', function(event){\n\t\t\trepeater.addNewItem( this );\n\t\t});\n\n\t\tjQuery( this.elem ).off( 'click.repeater', 'a.remove-item' );\n\t\tjQuery( this.elem ).on('click.repeater', 'a.remove-item', function(event){\n\t\t\trepeater.removeItem( this )\n\t\t});\n\n\t\tjQuery( this.elem ).off( this.options.saveEvents, this.options.saveElements );\n\t\tjQuery( this.elem ).on(this.options.saveEvents, this.options.saveElements, function(){\n\t\t\trepeater.save();\n\t\t});\n\n\t\treturn this;\n\t}\n\n\tthis.addItem = function(item, index) {\n\n\t\tvar itemHTML = this._template;\n\n\t\tfor (var property in item) {\n\n\t\t\titemHTML = itemHTML.replace( /{i}/g, index );\n\n\t\t\tvar repeaterButtonHTML = this.callbacks.repeaterButtons( this, index ) ? this.callbacks.repeaterButtons( this, index ) : this.addRepeaterButtons( index );\n\t\t\titemHTML               = itemHTML.replace( \"{buttons}\", repeaterButtonHTML );\n\n\t\t\tvar re   = new RegExp( '{' + property + '}', 'g' );\n\t\t\titemHTML = itemHTML.replace( re, item[property] );\n\n\t\t}\n\n\t\tvar itemObj = jQuery( itemHTML ).addClass( 'item-' + index );\n\n\t\tthis.callbacks.beforeAdd( this, itemObj, item );\n\t\tthis.append( itemObj );\n\t\tthis.callbacks.add( this, itemObj, item );\n\t}\n\n\tthis.addRepeaterButtons = function(index) {\n\n\t\tvar cssClass = this.items.length >= options.limit && options.limit !== 0 ? 'inactive' : '';\n\n\t\tvar str = '<div class=\"repeater-buttons\">';\n\t\tstr    += '<a class=\"add-item ' + cssClass + '\" data-index=\"' + index + '\">';\n\t\tstr    += '<img src=\"' + options.addImageSrc + '/images/add.png\" alt=\"Add\" /></a>';\n\n\t\tif (this.items.length > 1) {\n\t\t\tstr += '<a class=\"remove-item\" data-index=\"' + index + '\"><img src=\"' + options.removeImageSrc + '/images/remove.png\" alt=\"Remove\" /></a>';\n\t\t}\n\n\t\treturn str;\n\t}\n\n\tthis.addNewItem = function(elem) {\n\n\t\tvar index = jQuery( elem ).attr( 'data-index' );\n\n\t\tthis.callbacks.beforeAddNew( this, index );\n\t\tthis.items.splice( index + 1, 0, this.getBaseObject() );\n\t\tthis.callbacks.addNew( this, index );\n\n\t\tthis.refresh();\n\n\t}\n\n\tthis.removeItem = function(elem) {\n\n\t\tvar index = jQuery( elem ).attr( 'data-index' );\n\n\t\tthis.callbacks.beforeRemove( this, index );\n\t\t// using delete (over splice) to maintain the correct indexes for\n\t\t// the items array when saving the data from the UI\n\t\tdelete this.items[index];\n\t\tthis.callbacks.remove( this, index );\n\n\t\tthis.save();\n\t\tthis.refresh();\n\n\t}\n\n\tthis.refresh = function() {\n\n\t\tthis.elem.empty();\n\n\t\tfor (i in this.items) {\n\t\t\tthis.addItem( this.items[i], i );\n\t\t}\n\n\t}\n\n\tthis.save = function() {\n\n\t\tvar keys = this.getDataKeys();\n\t\tvar data = new Array();\n\n\t\tfor (i = 0; i < this.items.length; i++) {\n\n\t\t\tif (typeof this.items[i] == 'undefined') {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tvar item = {};\n\n\t\t\tfor (j in keys) {\n\t\t\t\tvar key = keys[j];\n\t\t\t\tvar id  = '#' + key + '_' + i;\n\n\t\t\t\titem[key] = jQuery( this.elem ).find( id ).val();\n\t\t\t}\n\n\t\t\tdata.push( item );\n\t\t}\n\n\t\t// save data to items\n\t\tthis.items = data;\n\n\t\t// save data externally via callback\n\t\tthis.callbacks.save( this, data );\n\n\t}\n\n\t/**\n\t* Loops through the current items array and retrieves the object properties of the\n\t* first valid item object. Originally this would simply pull the object keys from\n\t* the first index of the items array; however, when the first item has been\n\t* 'deleted' (see the save() method), it will be undefined.\n\t*/\n\tthis.getDataKeys = function() {\n\n\t\tvar keys = new Array();\n\n\t\tfor (var i in this.items) {\n\n\t\t\tif (typeof this.items[i] == 'undefined') {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tfor (var key in this.items[i]) {\n\t\t\t\tkeys[keys.length] = key;\n\t\t\t}\n\n\t\t\tbreak;\n\t\t}\n\n\t\treturn keys;\n\t}\n\n\tthis.getBaseObject = function() {\n\n\t\tvar item = {};\n\t\tvar keys = this.getDataKeys();\n\n\t\tfor (var i in keys) {\n\t\t\titem[keys[i]] = '';\n\t\t}\n\n\t\treturn item;\n\t}\n\n\treturn this.init( true );\n};\n"], "names": ["j<PERSON><PERSON><PERSON>", "fn", "repeater", "options", "extend", "template", "limit", "items", "saveEvents", "saveElements", "addImageSrc", "removeImageSrc", "callbacks", "save", "beforeAdd", "add", "before<PERSON>dd<PERSON>ew", "addNew", "beforeRemove", "remove", "repeaterButtons", "elem", "_template", "next", "prop", "html", "split", "newSaveEvents", "i", "push", "join", "init", "after", "addClass", "empty", "addItem", "off", "on", "event", "addNewItem", "removeItem", "item", "index", "itemHTML", "property", "replace", "repeaterButtonHTML", "addRepeaterButtons", "re", "RegExp", "itemObj", "append", "cssClass", "length", "str", "attr", "splice", "getBaseObject", "refresh", "keys", "getDataKeys", "data", "j", "key", "id", "find", "val"], "mappings": "AAOAA,OAAOC,EAAE,CAACC,QAAQ,CAAG,SAASC,CAAO,EAsBpC,IAAI,CAACA,OAAO,CAAKH,OAAOI,MAAM,CAAE,CAAA,EAAM,CAAC,EApBxB,CACdC,SAAU,GACVC,MAAO,EACPC,MAAO,CAAC,CAAC,EAAE,CACXC,WAAY,cACZC,aAAc,gBACdC,YAAa,GACbC,eAAgB,GAChBC,UAAW,CACVC,KAAM,WAAa,EACnBC,UAAW,WAAa,EACxBC,IAAK,WAAa,EAClBC,aAAc,WAAa,EAC3BC,OAAQ,WAAa,EACrBC,aAAc,WAAa,EAC3BC,OAAQ,WAAa,EACrBC,gBAAiB,WAAa,MAAO,CAAA,CAAO,CAC7C,CACD,EAEoDjB,GACpD,IAAI,CAACkB,IAAI,CAAQrB,OAAQ,IAAI,EAC7B,IAAI,CAACO,KAAK,CAAO,IAAI,CAACJ,OAAO,CAACI,KAAK,CACnC,IAAI,CAACK,SAAS,CAAG,IAAI,CAACT,OAAO,CAACS,SAAS,CACvC,IAAI,CAACU,SAAS,CAAG,IAAI,CAACnB,OAAO,CAACE,QAAQ,CAAG,IAAI,CAACF,OAAO,CAACE,QAAQ,CAAG,IAAI,CAACgB,IAAI,CAACE,IAAI,CAAE,IAAM,IAAI,CAACF,IAAI,CAACG,IAAI,CAAE,MAAS,aAAcC,IAAI,GAGlI,IAAIjB,EAAa,IAAI,CAACL,OAAO,CAACK,UAAU,CAACkB,KAAK,CAAE,KAE5CC,EAAgB,EAAE,CAEtB,IAAKC,KAAKpB,EACTmB,EAAcE,IAAI,CAAErB,CAAU,CAACoB,EAAE,CAAG,aA0LrC,OAvLA,IAAI,CAACzB,OAAO,CAACK,UAAU,CAAGmB,EAAcG,IAAI,CAAE,KAE9C,IAAI,CAACC,IAAI,CAAG,WAaX,IAAKH,KAVA,AAAE,IAAI,CAACN,SAAS,EACpB,CAAA,IAAI,CAACA,SAAS,CAAG,IAAI,CAACD,IAAI,CAACI,IAAI,EAAC,EAIjC,IAAI,CAACJ,IAAI,CAACW,KAAK,CAAE,YAAc,IAAI,CAACX,IAAI,CAACG,IAAI,CAAE,MAAS,mCAAqC,IAAI,CAACF,SAAS,CAAG,UAE9G,IAAI,CAACD,IAAI,CAACY,QAAQ,CAAE,YACpB,IAAI,CAACZ,IAAI,CAACa,KAAK,GAEL,IAAI,CAAC3B,KAAK,CACnB,IAAI,CAAC4B,OAAO,CAAE,IAAI,CAAC5B,KAAK,CAACqB,EAAE,CAAEA,GAG9B,IAAI1B,EAAW,IAAI,CAiBnB,OAfAF,OAAQ,IAAI,CAACqB,IAAI,EAAGe,GAAG,CAAE,iBAAkB,+BAC3CpC,OAAQ,IAAI,CAACqB,IAAI,EAAGgB,EAAE,CAAC,iBAAkB,8BAA+B,SAASC,CAAK,EACrFpC,EAASqC,UAAU,CAAE,IAAI,CAC1B,GAEAvC,OAAQ,IAAI,CAACqB,IAAI,EAAGe,GAAG,CAAE,iBAAkB,iBAC3CpC,OAAQ,IAAI,CAACqB,IAAI,EAAGgB,EAAE,CAAC,iBAAkB,gBAAiB,SAASC,CAAK,EACvEpC,EAASsC,UAAU,CAAE,IAAI,CAC1B,GAEAxC,OAAQ,IAAI,CAACqB,IAAI,EAAGe,GAAG,CAAE,IAAI,CAACjC,OAAO,CAACK,UAAU,CAAE,IAAI,CAACL,OAAO,CAACM,YAAY,EAC3ET,OAAQ,IAAI,CAACqB,IAAI,EAAGgB,EAAE,CAAC,IAAI,CAAClC,OAAO,CAACK,UAAU,CAAE,IAAI,CAACL,OAAO,CAACM,YAAY,CAAE,WAC1EP,EAASW,IAAI,EACd,GAEO,IAAI,AACZ,EAEA,IAAI,CAACsB,OAAO,CAAG,SAASM,CAAI,CAAEC,CAAK,EAElC,IAAIC,EAAW,IAAI,CAACrB,SAAS,CAE7B,IAAK,IAAIsB,KAAYH,EAAM,CAE1BE,EAAWA,EAASE,OAAO,CAAE,OAAQH,GAErC,IAAII,EAAqB,IAAI,CAAClC,SAAS,CAACQ,eAAe,CAAE,IAAI,CAAEsB,GAAU,IAAI,CAAC9B,SAAS,CAACQ,eAAe,CAAE,IAAI,CAAEsB,GAAU,IAAI,CAACK,kBAAkB,CAAEL,GAClJC,EAAyBA,EAASE,OAAO,CAAE,YAAaC,GAExD,IAAIE,EAAO,AAAIC,OAAQ,IAAML,EAAW,IAAK,KAC7CD,EAAWA,EAASE,OAAO,CAAEG,EAAIP,CAAI,CAACG,EAAS,CAEhD,CAEA,IAAIM,EAAUlD,OAAQ2C,GAAWV,QAAQ,CAAE,QAAUS,GAErD,IAAI,CAAC9B,SAAS,CAACE,SAAS,CAAE,IAAI,CAAEoC,EAAST,GACzC,IAAI,CAACU,MAAM,CAAED,GACb,IAAI,CAACtC,SAAS,CAACG,GAAG,CAAE,IAAI,CAAEmC,EAAST,EACpC,EAEA,IAAI,CAACM,kBAAkB,CAAG,SAASL,CAAK,EAEvC,IAAIU,EAAW,IAAI,CAAC7C,KAAK,CAAC8C,MAAM,EAAIlD,EAAQG,KAAK,EAAIH,AAAkB,IAAlBA,EAAQG,KAAK,CAAS,WAAa,GAEpFgD,EAAM,iCAQV,OANAA,GADU,sBAAwBF,EAAW,iBAAmBV,EAAQ,KAC9D,CAAA,aAAevC,EAAQO,WAAW,AAAD,EAAI,oCAE3C,IAAI,CAACH,KAAK,CAAC8C,MAAM,CAAG,GACvBC,CAAAA,GAAO,sCAAwCZ,EAAQ,eAAiBvC,EAAQQ,cAAc,CAAG,yCAAwC,EAGnI2C,CACR,EAEA,IAAI,CAACf,UAAU,CAAG,SAASlB,CAAI,EAE9B,IAAIqB,EAAQ1C,OAAQqB,GAAOkC,IAAI,CAAE,cAEjC,IAAI,CAAC3C,SAAS,CAACI,YAAY,CAAE,IAAI,CAAE0B,GACnC,IAAI,CAACnC,KAAK,CAACiD,MAAM,CAAEd,EAAQ,EAAG,EAAG,IAAI,CAACe,aAAa,IACnD,IAAI,CAAC7C,SAAS,CAACK,MAAM,CAAE,IAAI,CAAEyB,GAE7B,IAAI,CAACgB,OAAO,EAEb,EAEA,IAAI,CAAClB,UAAU,CAAG,SAASnB,CAAI,EAE9B,IAAIqB,EAAQ1C,OAAQqB,GAAOkC,IAAI,CAAE,cAEjC,IAAI,CAAC3C,SAAS,CAACM,YAAY,CAAE,IAAI,CAAEwB,GAGnC,OAAO,IAAI,CAACnC,KAAK,CAACmC,EAAM,CACxB,IAAI,CAAC9B,SAAS,CAACO,MAAM,CAAE,IAAI,CAAEuB,GAE7B,IAAI,CAAC7B,IAAI,GACT,IAAI,CAAC6C,OAAO,EAEb,EAEA,IAAI,CAACA,OAAO,CAAG,WAId,IAAK9B,KAFL,IAAI,CAACP,IAAI,CAACa,KAAK,GAEL,IAAI,CAAC3B,KAAK,CACnB,IAAI,CAAC4B,OAAO,CAAE,IAAI,CAAC5B,KAAK,CAACqB,EAAE,CAAEA,EAG/B,EAEA,IAAI,CAACf,IAAI,CAAG,WAEX,IAAI8C,EAAO,IAAI,CAACC,WAAW,GACvBC,EAAO,EAAW,CAEtB,IAAKjC,EAAI,EAAGA,EAAI,IAAI,CAACrB,KAAK,CAAC8C,MAAM,CAAEzB,IAElC,GAAI,AAAwB,KAAA,IAAjB,IAAI,CAACrB,KAAK,CAACqB,EAAE,EAIxB,IAAIa,EAAO,CAAC,EAEZ,IAAKqB,KAAKH,EAAM,CACf,IAAII,EAAMJ,CAAI,CAACG,EAAE,CACbE,EAAM,IAAMD,EAAM,IAAMnC,CAE5Ba,CAAAA,CAAI,CAACsB,EAAI,CAAG/D,OAAQ,IAAI,CAACqB,IAAI,EAAG4C,IAAI,CAAED,GAAKE,GAAG,EAC/C,CAEAL,EAAKhC,IAAI,CAAEY,GAIZ,IAAI,CAAClC,KAAK,CAAGsD,EAGb,IAAI,CAACjD,SAAS,CAACC,IAAI,CAAE,IAAI,CAAEgD,EAE5B,EAQA,IAAI,CAACD,WAAW,CAAG,WAElB,IAAID,EAAO,EAAW,CAEtB,IAAK,IAAI/B,KAAK,IAAI,CAACrB,KAAK,CAEvB,GAAI,AAAwB,KAAA,IAAjB,IAAI,CAACA,KAAK,CAACqB,EAAE,EAIxB,IAAK,IAAImC,KAAO,IAAI,CAACxD,KAAK,CAACqB,EAAE,CAC5B+B,CAAI,CAACA,EAAKN,MAAM,CAAC,CAAGU,EAGrB,MAGD,OAAOJ,CACR,EAEA,IAAI,CAACF,aAAa,CAAG,WAEpB,IAAIhB,EAAO,CAAC,EACRkB,EAAO,IAAI,CAACC,WAAW,GAE3B,IAAK,IAAIhC,KAAK+B,EACblB,CAAI,CAACkB,CAAI,CAAC/B,EAAE,CAAC,CAAG,GAGjB,OAAOa,CACR,EAEO,IAAI,CAACV,IAAI,CAAE,CAAA,EACnB"}