=== WPCode - Insert Headers and Footers + Custom Code Snippets - WordPress Code Manager ===
Contributors: WPbeginner, smub, gripgrip, wpcodeteam
Tags: code, css, php, header, code snippets
Requires at least: 4.6
Tested up to: 6.8
Requires PHP: 5.5
Stable tag: 2.2.9
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Easily add code snippets in WordPress. Insert header & footer scripts, add PHP code snippets with conditional logic, insert ads pixel code, and more.


== Description ==

= Insert Headers & Footers + Full WordPress Code Snippets Plugin =

[WPCode](https://wpcode.com/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin) (formerly known as Insert Headers and Footers by WPBeginner) is the most popular code snippets plugin for WordPress used by over 2 million websites.

We make it easy for you to add code snippets in WordPress without having to edit your theme's functions.php file.

Our simple insert headers and footers interface allows you to insert code like Google Analytics, custom CSS, Facebook Pixel, and more to your WordPress site's header and footer as well other areas of your website. No need to edit your theme files!

Aside from Header and Footer scripts, you can also use WPCode to insert custom PHP code snippets, JavaScript code snippets, CSS code snippets, HTML code snippets, and text snippets with full conditional logic support.

We took the pain out of adding custom code snippets in WordPress and made it easy.

> I have been using Insert Headers and Footers and it is such a useful tool. Super helpful and the very best of its kind. Highly recommend
> The_Gibble - WordPress user

= Quick Overview of WPCode from WPBeginner =

https://www.youtube.com/watch?v=Fo-7MKRRUec

> <strong>Introducing New WPCode Pro</strong><br />
> While WPCode Lite offers tons of powerful features for free, we listened to user feedback and created WPCode Pro with even more amazing features to improve your workflow. This includes smart conditional logic, saving code snippets to cloud library, code revisions, page-specific snippets, deeper integration with popular plugins like WooCommerce, Easy Digital Downloads, and so much more. [Click here to purchase the best premium WordPress code snippet plugin now!](https://wpcode.com/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin)

= Future Proof Code Snippet Management =

Most [WordPress tutorial websites](https://www.wpbeginner.com/category/wp-tutorials/) ask you to add code snippets to your theme's functions.php file. This makes managing code snippets messy, and it also prevents you from updating your theme.

If you ever update your theme or switch to another theme, then you will lose all custom code functions that you added in your functions.php file.

WPCode solves this by providing you an easy way to insert header and footer scripts along with other code snippets directly from your WordPress dashboard. These code snippets actually run as if they were in your theme's functions.php file.

Our smart code snippet validation helps you prevent common code errors to ensure you never break your website when adding code snippets or header and footer scripts.

<strong>New WPCode Cloud Library</strong> even allows you to store all your custom code snippets in a cloud library, so you can easily re-use code snippets across multiple website projects and save time. You can keep your code snippets completely private or [share it with the community](https://library.wpcode.com/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin) to give back while boosting your social profile.

> This plugin allows me to not only add things to my site whenever needed, but it takes me only seconds to accomplish it.
> David Weber - WordPress user

= Full Code Snippets Library and Code Generators =

Ever wanted a central place to find all the most popular WordPress code snippets that are tested and proven to work?

When we started Insert Headers and Footers plugin, we did too. So we built a WordPress code snippets library right inside the WPCode plugin.

You will find verified PHP code snippets for popular feature requests like disable REST API, disable XML-RPC, disable comments, allow SVG file uploads, disable Gutenberg, add Classic Editor, and more without installing separate plugins for each.

> I was very hesitant to get into any of the code for my website. Your plugin made it easy for me to do.
> Conbrio75 - WordPress user

We also built the ability to save your code snippets to [WPCode Cloud Library](https://library.wpcode.com/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin), so you can easily re-use it on your other websites, client projects, or even share it with the larger community.

WPCode Cloud Library helps you better organize your code snippets in one central location, so you can save more time and speed up your workflow when managing websites. No more wasting time looking for custom notes or Github gists.

Aside from our growing code snippets library, we also have WordPress code generators to help you quickly get ready-to-use custom code using the latest WordPress coding standards and API's.

= Conditional Logic for Code Snippets + Code Insertion Priority =

Our goal with WPCode was to create a WordPress code snippets plugin that's both EASY and POWERFUL.

That's why aside from our global header and footer scripts, we added advanced features like conditional logic for code snippets and made it easy.

Instead of learning WordPress conditional logic queries, you can use our beginner-friendly conditional logic user interface to:

* Load code snippets for logged in users only
* Load PHP code snippets for specific user roles
* Load PHP code snippets only on specific page URLs
* Insert header and footer pixel scripts on specific pages
* Show code snippets based on type of page
* Run code snippet only on certain post types
* Load header and footer code snippet based on referrer source
* and more...

We also added both automatic code insertion and manual code output using shortcodes.

Our Auto Insert feature allows you to run the code snippet everywhere or choose from custom options like:

* Run code snippet only on frontend
* Run code snippet only in WordPress admin area
* Add header and footer scripts sitewide
* Insert PHP code snippet before or after post content
* Insert code snippet before or after specific paragraph
* Insert code snippet on specific archive pages
* Insert code snippets after specific WooCommerce function ([Pro Feature](https://wpcode.com/?utm_source=wprepo&utm_medium=link&utm_campaign=liteplugin))

Aside from that, we also added a visual code snippet priority system, so you can choose the order for your custom functions to avoid code conflict.

> This is such a useful plugin! It makes it so much easier to include things on your website!
> Understoryliving - WordPress user

And for even more flexibility and customization, we have added the ability for you to add page-specific code snippets right from the WordPress classic editor as well as the Gutenberg editor. You can even load code snippets based on device type such as mobile only code snippets, desktop only code snippets, etc.

= Import and Export Code Snippets =

Managing multiple websites or developing in a staging environment?

We offer an easy way to import and export your custom code snippets, functions, and header and footer scripts to help you save time.

You can also save your code snippets to WPCode Cloud (Pro feature), so you can easily re-use the same code snippets across multiple websites. This also allows you to better organize your code snippets instead of wasting time searching for random Github gists.

> Simple plugin I use in quite every site. Very useful to insert scripts and tags.
> tommasoperego - WordPress user

= Full WPCode Feature List =

The simple interface of WPCode plugin (formerly known as Insert Headers and Footers) gives you one place where you can insert header and footer scripts as well as custom code snippets rather than dealing with dozens of different plugins.

Below is a full list of WPCode features:

* Quick to set up
* Unlimited code snippets
* Simple to insert header and footer scripts globally
* Beginner Friendly Code Editor with Syntax Highlighter for PHP, JavaScript, and HTML
* Smart Code Validation to Prevent PHP Errors
* Insert header code and/or footer code using Conditional Logic
* Add <strong>Google Analytics</strong> code to header and footer
* Add <strong>custom CSS</strong> code to any theme
* Insert <strong>Facebook pixel</strong> code in header and footer
* Insert any code or script, including HTML and Javascript
* Insert PHP Code Snippets
* Ready-made Code Snippet Library
* Custom WordPress Code Snippet Generator
* Show or Hide PHP Code Snippets based on conditional logic
* Run PHP code and custom code snippets everywhere or in select areas using smart auto-insert rules.
* Manually insert PHP code snippets using shortcodes anywhere on website
* Add Rich Text Ads and Content Snippets automatically on posts & pages.
* Export / Import Code Snippets
* <strong>New</strong> Save snippets to Cloud Library - All your snippets stored in your private code snippet cloud.
* <strong>New</strong> Deep integrations with Gutenberg, WooCommerce, and more.
* Device specific code snippets (such as load code snippets on mobile only, desktop only, etc)
* and more features coming soon.

= Credits =

Insert Headers and Footers plugin was first created by [Syed Balkhi](https://syedbalkhi.com/) and the [WPBeginner](http://www.wpbeginner.com/) team in 2011.

It was later rebranded to WPCode in 2022 by Syed Balkhi to add powerful code snippets features that users were requesting for.

= Branding Guideline =

WPCode™ is a trademark of WPCode LLC. When writing about the Insert Headers and Footers - Code Snippets plugin by WPCode, please make sure to uppercase the initial 3 letters.

WPCode (correct)
WP Code (incorrect)
wpcode (incorrect)
wp code snippets (incorrect)

== Installation ==

1. Install WPCode - Insert Headers, Footers, and Code Snippets plugin by uploading the `insert-headers-and-footers` directory to the `/wp-content/plugins/` directory. (See instructions on <a href="http://www.wpbeginner.com/beginners-guide/step-by-step-guide-to-install-a-wordpress-plugin-for-beginners/" rel="friend">how to install a WordPress plugin</a>.)
2. Activate WPCode - Insert Headers, Footers, and Code Snippets plugin through the `Plugins` menu in WordPress.
3. Insert code in your header and footer or add custom code snippets by going to the `Code Snippets` menu.

[youtube https://www.youtube.com/watch?v=QXbrdVjWaME]

== Screenshots ==

1. WordPress Code Snippets Management Screen
2. Ready-Made Code Snippets Library
3. Edit PHP Snippets with Code Syntax Highlighter
4. Show / Hide Code Snippets with Smart Conditional Logic
5. Custom WordPress Code Generators
6. Example of Custom Post Type Generator
7. Insert Header and Footer Scripts Globally
8. Import and Export Code Snippets
9. Private Snippets Library In Plugin
10. Page-Specific Scripts Metabox

== Frequently Asked Questions ==

= Can I use WPCode - Insert Headers and Footers to install Google Analytics? =

Yes, you can insert your Google Analytics code in the `Scripts in Header` field.

= Can I use WPCode - Insert Headers and Footers for Google AdSense? =

Yes, to verify your account or to tag your page for Auto ads, paste the code AdSense gives you, into the Scripts in Header field.

= Will I lose my snippets if I change my WordPress theme? =

No, the idea behind WPCode - Insert Headers, Footers, and Code Snippets plugin is so you can safely add code snippets.

All code snippets are stored in the WordPress database, independent of the theme upgrades.

= Can I switch back to the old version of Insert Headers and Footers? =

Yes, if you don't want the advanced code snippets functionality, then you can switch back to the old Insert Headers and Footers features by simply going to the Settings Menu and clicking on the Headers & Footers mode.

= What Type of Code Snippets can I add? =

With WPCode, you can add any type of code snippet that you would otherwise add in your theme's functions.php file or in a site-specific plugin.

This includes custom PHP snippets, JavaScript snippets, HTML snippet, CSS snippets, Text Snippets, Conversion pixels, Tracking scripts, AdSense or other banner ads code, and more.

= What are some example plugins WPCode can replace? =

WPCode comes with a ready-made code snippets library that allows you to replace several popular plugins including:

* Disable Comment plugins
* Disable XML-RPC plugins
* Disable Rest API plugins
* Disable Gutenberg plugins
* Classic Editor plugin
* Allow SVG File Upload plugins
* Disable RSS feed plugins
* Disable Search plugins
* Disable Automatic Updates plugins
* Disable Admin Bar plugins
* Disable Widget Blocks plugin
* Classic Widgets plugin
* Remove WordPress Version Number plugins
* Google Analytics plugins
* Facebook Pixel plugins
* Google AdSense plugins
* Custom Post Types UI plugins
* Other WordPress Generator plugins

... and basically any plugin that adds a functionality which can be added via custom code snippets.

== Changelog ==

= 2.2.9 =
* New: Added a new importer for the Post Snippets plugin to make it easier to migrate your code snippets.
* Tweak: We adjusted the way the safe mode works to limit its effects on frontend urls.

= 2.2.8 =
* New: Schema Generators - we added 20 new code generators to make it easier to manage schema code on your website.
* Tweak: We moved the button to update generated snippets to the top so that you can update snippets easier.
* Tweak: We added "suppress_filters" to WP_Query used by WPCode to avoid conflicts with other plugins.
* Fix: We fixed an issue where the generated snippets were not automatically inserted when activated.

= 2.2.7 =
* New: Added support for automatically importing snippets from 2 new plugins.

= 2.2.6 =
* New: Easily find where your snippet is used as a shortcode with our new shortcode finder tool. 1-click search through your site.
* New: Improved editor for snippet notes. You can now add links and format text with a WYSIWYG editor.
* New: Snippet notes column. Display the notes for each snippet in the list of snippets.
* Fix: We improved the code snippets list filtering in the admin to avoid issues when JS errors are encountered on the page from other scripts.

= 2.2.5 =
* New: We added a new way to quickly filter snippets by code type in the list of snippets.
* Tweak: Prevent content_save_pre from modifying snippet code.

= ******* =
* Fix: Shortcode attributes variables were not being set correctly.

= 2.2.4 =
* Tweak: We adjusted the way our plugin adds a version-specific body class to avoid conflicts with other plugins.
* Tweak: We improved the way custom PHP code is executed to avoid conflicts with variables in snippets.
* Fix: When removing the last Conditional Logic group the rule selector was no longer working.

= ******* =
* Tweak: Updated the way we load translatable strings in some components for compatibility with WordPress 6.7.

= 2.2.3 =
* Tweak: We updated the way we validate PHP snippets when making an edit to an active snippet for better feedback when debugging.
* Tweak: We replaced a dependency (SweetAlert2) for a more lightweight alternative to improve performance.

= 2.2.2 =
* New: The Add Snippet screen now makes it easier to get started with more than just our library of snippets, choose from expert-picked snippets, snippet generators or plugin-specific snippets.
* New: We updated the code type picker for more clarity on what each code type is useful for and the last code type you used is now saved for a streamlined experience when adding multiple snippets.
* Tweak: We extended the unfiltered HTML permissions notice when the DISALLOW_UNFILTERED_HTML constant is defined to add more clarity.

= 2.2.1 =
* Fix: We fixed a bug that was causing the selected conditional logic operator to not be displayed correctly for some rules.
* Fix: The auto-insert location picker was not opening again in some situations.

= 2.2.0 =
* New: We updated the conditional logic rules picker for more clarity and ease of use.
* New: We expanded our automatic cache clearing to include the Swift Performance plugin.
* Tweak: The Page URL conditional logic option now supports query params and wp-admin URLs.

= 2.1.14 =
* New: When making changes to the Global Header & Footer WPCode will attempt to automatically clear page cache for popular caching plugins.
* New: Save your snippet with a keyboard shortcut by pressing CTRL+S (Windows) or CMD+S (Mac) in the snippet editor.
* Tweak: Prevent error when TinyMCE is disabled by other plugins.

= 2.1.13 =
* New: We added admin-specific locations for header and footer to streamline customizations in the wp-admin area.
* New: The code editor height can now be adjusted directly from the snippet editor screen. Auto-height is still available as an option on the settings page.
* Fix: When making changes to a snippet, the function to prevent you from navigating away was no longer working.

= 2.1.12 =
* New: We added a new way to execute PHP snippets "On Demand" - look for the "On Demand" location in the Auto Insert settings.
* Tweak: We added checks in place to prevent malicious code patterns from being used in WPCode.
* Fix: We fixed a bug where the conditional logic for logged-in users was not being saved correctly the first time.

= 2.1.11 =
* Tweak: We improved the admin bar menu to fix an JS error in some scenarios and a html validation issue.
* Tweak: We added support for importing snippets from the Code Snippets Pro plugin.
* Fix: We fixed an issue with importing CSS snippets.

= 2.1.10 =
* New: When another user is editing the same snippet, we now show a warning to avoid conflicts.
* Tweak: Improved mobile styles across the plugin admin for better readability.

= 2.1.9 =
* New: Conditional logic rule for Blog home page (posts page)
* Tweak: Improved the connect to library flow to avoid the connect window from getting blocked.
* Fix: Author archive condition was not working as intended.

= 2.1.8 =
* Tweak: We changed the way the code generators are loaded to improve performance overall.
* Tweak: We changed the way translations are loaded for better performance.

= 2.1.7 =
* New: WPCode now has a Dark Mode for those late night coding sessions. Enable it on the settings page.
* Tweak: We improved the way the error messages are displayed in the snippet editor for more clarity.
* Fix: The snippet editor was no longer running syntax checks for PHP snippets upon activation.
* Fix: We fixed a bug where in certain versions of Safari the auto-insert location was being unset when saving a snippet.

= 2.1.6 =
* New: We added the option to delete all the plugin data on uninstall.
* Fix: The code of duplicated snippets was sometimes removing some slashes.
* Fix: Error count was not correctly updated when snippets were trashed or deleted.

= 2.1.5 =
* New: Priority column added to the list of snippets, order by snippet priority.
* Fix: Fixed a notice that was being thrown in new sites when running the installation routines.
* Fix: Filtering by tag or location in the list of snippets was not working correctly.

= ******* =
* Fix: Avoid notices for errors that don't include the file parameter.
* Fix: Improve compatibility with PHP 8.2 for the snippet cache class.

= 2.1.4 =
* New: Enhanced error tracking: Errors generated by PHP snippets on your website will be displayed within the snippet's context, simplifying the debugging process.
* New: We've refined the snippet auto-deactivation process. Now, only snippets that produce fatal errors in the admin section will be deactivated. All other errors will be reported through the newly implemented error tracker.
* New: We introduced three new columns to the snippet list: ID, Code Type, and Shortcode.
* Tweak: We've enhanced our file caching mechanism to prevent redundant requests in scenarios where files aren't written properly due to certain setups.
* Tweak: We've implemented an additional check to ensure that closing PHP tags don't disrupt the execution of snippets.
* Fix: We've corrected the sorting of snippets when arranged by their last updated date.

= ******* =
* Fix: Shortcode attributes were not being correctly parsed in some scenarios.

= 2.1.3 =
* New: We improved how we manage automatic snippet disabling when errors occur. You'll now get detailed insights into which snippet is responsible for the error and the exact line where the problem occurred.
* Tweak: We adjusted the way snippets are preloaded to improve compatibility with object cache.
* Fix: The snippets added as shortcodes were not correctly displayed in the admin bar.

= 2.1.2 =
* Tweak: We adjusted the way snippets are tracked in the Admin Bar Code Spotter to avoid wrong numbers in certain locations.
* Tweak: We improved the way the Admin Bar Code Spotter menu is loaded for better compatibility with other plugins and older themes.
* Tweak: Changed the way assets used by the admin bar are loaded to avoid errors if the head is loaded multiple times.

= 2.1.1 =
* New: Easily duplicate a snippet with all of its settings with the new Duplicate link in the list of snippets.
* Fix: Taxonomy page conditional logic was not being correctly assigned for category and tags archives.
* Tweak: Adjust admin bar markup to avoid using a heading.

= 2.1.0 =
* New: See exactly which scripts and snippets are loaded on the current page with the WPCode admin bar info menu.
* Tweak: Minor update to connect process.
