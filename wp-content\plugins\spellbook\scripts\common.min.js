if(void 0===gperk)var gperk={};gperk.is=function(i,e){return typeof i==e},gperk.isUndefined=function(i){return gperk.is(i,"undefined")},gperk.isSet=function(i){return gperk.is(i,"undefined")},gperk.ajaxSpinner=function(i,e){var e=void 0===e?gperk.gformBaseUrl+"/images/loading.gif":e;return this.elem=i,this.image='<img src="'+e+'" />',this.init=function(){return this.spinner=jQuery(this.image),jQuery(this.elem).after(this.spinner),this},this.destroy=function(){jQuery(this.spinner).remove()},this.init()},gperk.applyFilter=function(i,e){return void 0!==window[i]?window[i](e):e};