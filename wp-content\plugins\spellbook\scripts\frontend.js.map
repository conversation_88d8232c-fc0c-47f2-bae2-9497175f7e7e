{"version": 3, "sources": ["<anon>"], "sourcesContent": ["if (typeof gperk == 'undefined') {\n\tvar gperk = {};\n}\n\ngperk.getProductQuantity = function(formId, productFieldId) {\n\n\tvar quantity;\n\tvar quantityInput = jQuery( \"#ginput_quantity_\" + formId + \"_\" + productFieldId );\n\n\tif (quantityInput.length > 0) {\n\t\tquantity = ! gformIsNumber( quantityInput.val() ) ? 0 : quantityInput.val();\n\t} else {\n\t\tquantityElement = jQuery( \".gfield_quantity_\" + formId + \"_\" + productFieldId );\n\n\t\tquantity = 1;\n\t\tif (quantityElement.find( \"input\" ).length > 0) {\n\t\t\tquantity = quantityElement.find( \"input\" ).val();\n\t\t} else if (quantityElement.find( \"select\" ).length > 0) {\n\t\t\tquantity = quantityElement.find( \"select\" ).val();\n\t\t}\n\n\t\tif ( ! gformIsNumber( quantity )) {\n\t\t\tquantity = 0\n\t\t}\n\t}\n\tquantity = parseFloat( quantity );\n\n\t//setting global variable if quantity is more than 0 (a product was selected). Will be used when calculating total\n\tif (quantity > 0) {\n\t\t_anyProductSelected = true;\n\t}\n\n\treturn quantity;\n\n}\n"], "names": ["gperk", "getProductQuantity", "formId", "productFieldId", "quantity", "quantityInput", "j<PERSON><PERSON><PERSON>", "length", "gformIsNumber", "val", "quantityElement", "find", "parseFloat", "_anyProductSelected"], "mappings": "AAAA,GAAI,AAAgB,KAAA,IAATA,MACV,IAAIA,MAAQ,CAAC,CAGdA,CAAAA,MAAMC,kBAAkB,CAAG,SAASC,CAAM,CAAEC,CAAc,EAGzD,IADIC,EACAC,EAAgBC,OAAQ,oBAAsBJ,EAAS,IAAMC,GAyBjE,OAvBIE,EAAcE,MAAM,CAAG,EAC1BH,EAAW,AAAEI,cAAeH,EAAcI,GAAG,IAAWJ,EAAcI,GAAG,GAArB,GAEpDC,gBAAkBJ,OAAQ,oBAAsBJ,EAAS,IAAMC,GAE/DC,EAAW,EACPM,gBAAgBC,IAAI,CAAE,SAAUJ,MAAM,CAAG,EAC5CH,EAAWM,gBAAgBC,IAAI,CAAE,SAAUF,GAAG,GACpCC,gBAAgBC,IAAI,CAAE,UAAWJ,MAAM,CAAG,GACpDH,CAAAA,EAAWM,gBAAgBC,IAAI,CAAE,UAAWF,GAAG,EAAC,EAG5C,AAAED,cAAeJ,IACrBA,CAAAA,EAAW,CAAA,GAMTA,AAHJA,CAAAA,EAAWQ,WAAYR,EAAS,EAGjB,GACdS,CAAAA,oBAAsB,CAAA,CAAG,EAGnBT,CAER"}