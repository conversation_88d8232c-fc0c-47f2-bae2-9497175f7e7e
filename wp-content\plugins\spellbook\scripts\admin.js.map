{"version": 3, "sources": ["<anon>"], "sourcesContent": ["if (typeof gperk == 'undefined') {\n\tvar gperk = {};\n}\n\ngperk.confirmActionUrl = function(event, message, url) {\n\tevent.preventDefault();\n\n\tvar elem = jQuery( event.target );\n\n\tif ( ! url) {\n\t\turl = elem.prop( 'href' );\n\t}\n\n\tif (confirm( message )) {\n\t\tlocation.href = url;\n\t}\n\n}\n\n/**\n* Add a tab to the form editor\n*\n*/\ngperk.addTab = function(elem, id, label) {\n\n\tvar tabClass = id == '#gws_form_tab' ? 'gwp_form_tab' : 'gwp_field_tab';\n\n\tvar tabClass    = id.replace( '#', '' ),\n\t\taltTabClass = tabClass.replace( 'gws', 'gwp' ),\n\t\ttabClass    = tabClass != altTabClass ? tabClass + ' ' + altTabClass : tabClass;\n\n\t// destory tabs already initialized\n\telem.tabs( 'destroy' );\n\n\t// add new tab\n\telem.find( 'ul' ).eq( 0 ).append( '<li style=\"width:100px; padding:0px;\" class=\"' + tabClass + '\"> \\\n        <a href=\"' + id + '\">' + label + '</a> \\\n        </li>' )\n\n\t// add new tab content\n\telem.append( jQuery( id ) );\n\n\t// re-init tabs\n\telem.tabs({\n\t\tbeforeActivate: function(event, ui) {\n\t\t\tswitch ( jQuery( ui.newPanel ).prop( 'id' ) ) {\n\t\t\t\tcase 'gws_form_tab':\n\t\t\t\t\tjQuery( document ).trigger( 'gwsFormTabSelected', [ form ] );\n\t\t\t\tbreak;\n\t\t\t\tcase 'gws_field_tab':\n\t\t\t\t\tjQuery( document ).trigger( 'gwsFieldTabSelected', [ field ] );\n\t\t\t\tbreak;\n\t\t\t};\n\t\t}\n\t});\n\n}\n\ngperk.togglePerksTab = function() {\n\n\tvar fieldTab = jQuery( '.ui-tabs-nav li.gwp_field_tab' );\n\n\tfieldTab.hide();\n\n\tif ( gperk.fieldHasSettings() ) {\n\t\tfieldTab.show();\n\t}\n\n};\n\ngperk.fieldHasSettings = function() {\n\n\tvar hasSetting = false;\n\n\tjQuery( '#gws_field_tab' ).find( 'li.field_setting' ).each(function(){\n\t\tvar patt = /(display[: ]+none)/;\n\t\tif ( ! patt.test( jQuery( this ).attr( 'style' ) )) {\n\t\t\thasSetting = true;\n\t\t}\n\t});\n\n\treturn hasSetting;\n}\n\ngperk.toggleSection = function(elem, selector) {\n\tvar elem = jQuery( elem );\n\n\tif (elem.prop( 'checked' )) {\n\t\telem.parents( '.gwp-field' ).addClass( 'open' );\n\t\tjQuery( selector ).gwpSlide( 'down', '.perk-settings' );\n\t} else {\n\t\telem.parents( '.gwp-field' ).removeClass( 'open' );\n\t\tjQuery( selector ).gwpSlide( 'up', '.perk-settings' );\n\t}\n\n}\n\ngperk.isSingleProduct = function(field) {\n\tsingleFieldTypes = gperk.applyFilter( 'gwSingleFieldTypes', ['singleproduct', 'hiddenproduct', 'calculation'] );\n\treturn jQuery.inArray( field.inputType, singleFieldTypes ) != -1;\n}\n\ngperk.getFieldLabel = function(field, inputId) {\n\n\tif (gperk.isUndefined( inputId )) {\n\t\tinputId = false;\n\t}\n\n\tvar label = field.label;\n\tvar input = gperk.getInput( field, inputId );\n\n\tif (field.type == 'checkbox' && input != false) {\n\t\treturn input.label;\n\t} else if (input != false) {\n\t\treturn input.label;\n\t} else {\n\t\treturn label;\n\t}\n\n}\n\ngperk.getInput = function(field, inputId) {\n\n\tif (gperk.isUndefined( field['inputs'] ) && jQuery.isArray( field['inputs'] )) {\n\t\tfor (i in field['inputs']) {\n\t\t\tvar input = field['inputs'][i];\n\t\t\tif (input.id == inputId) {\n\t\t\t\treturn input;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn false;\n}\n\ngperk.toggleSettings = function(id, toggleSettingsId, isChecked, animate ) {\n\n\tvar elem         = jQuery( '#' + id );\n\tvar settingsElem = jQuery( '#' + toggleSettingsId );\n\tvar animate      = typeof animate !== 'undefined' ? animate : true;\n\n\t// if \"isChecked\" is passed, check the checkbox\n\tif ( ! gperk.is( isChecked, 'undefined' )) {\n\t\telem.prop( 'checked', isChecked );\n\t} else {\n\t\tisChecked = elem.is( ':checked' );\n\t}\n\n\tif ( isChecked ) {\n\t\tif ( animate ) {\n\t\t\tsettingsElem.gfSlide( 'down' );\n\t\t} else {\n\t\t\tsettingsElem.show();\n\t\t}\n\t} else {\n\t\tif ( animate ) {\n\t\t\tsettingsElem.gfSlide( 'up' );\n\t\t} else {\n\t\t\tsettingsElem.hide();\n\t\t}\n\t}\n\n\tSetFieldProperty( id, isChecked );\n\n}\n\ngperk.setInputProperty = function(inputId, property, value) {\n\n\tvar field = GetSelectedField();\n\n\tfor (i in field.inputs) {\n\t\tif (field.inputs[i].id == inputId) {\n\t\t\tfield.inputs[i][property] = value;\n\t\t}\n\t}\n\n}\n\n/**\n* Set a form property on current form.\n*\n* This function should only be used on the Gravity Forms form editor page where the \"form\" object is a global\n* variable and available for modification. Changes made to the form object on this page will be saved\n* when the user clicks \"Update Form\".\n*\n* @type Object\n*/\ngperk.setFormProperty = function(property, value) {\n\tform[property] = value;\n}\n\n/**\n* GWPerks version of the gfSlide jQuery plugin, used to show/hide/slideup/slidedown depending on whether\n* the settings are being init or already displayed\n*\n* @param isVisibleSelector Defaults to '#field_settings'; pass \"false\" to force \"hide()\"\n*\n*/\n\njQuery.fn.gwpSlide = function(direction, isVisibleSelector) {\n\n\tif (typeof isVisibleSelector == undefined) {\n\t\tisVisibleSelector = '#field_settings';\n\t}\n\n\tvar isVisible = isVisibleSelector === false || isVisibleSelector === true ? isVisibleSelector : jQuery( isVisibleSelector ).is( ':visible' );\n\n\tif (direction == 'up') {\n\t\tif ( ! isVisible) {\n\t\t\tthis.hide();\n\t\t} else {\n\t\t\tthis.slideUp();\n\t\t}\n\t} else {\n\t\tif ( ! isVisible) {\n\t\t\tthis.show();\n\t\t} else {\n\t\t\tthis.slideDown();\n\t\t}\n\t}\n\n\treturn this;\n};\n\njQuery( document ).ready( function ( $ ) {\n\n\t// Setup initialization action when Gravity Perks field settings are opened in GF 2.5.\n\t$( '.panel-block-tabs' ).on( 'accordionactivate', function( event, ui ) {\n\t\tif ( ui.newPanel.is( '#gravity-perks_tab' ) ) {\n\t\t\tgform.doAction( 'gperks_field_settings_tab_loaded', event, GetSelectedField(), window.form, ui );\n\t\t}\n\t} );\n\n\t/**\n\t * Hide custom field settings tab if no settings are displayed for the selected field type\n\t */\n\tjQuery( document ).bind( 'gform_load_field_settings', function( event, field ) {\n\t\tgperk.togglePerksTab()\n\t});\n\n\t/**\n\t * Add additional capabilities to Gravity Forms tooltips including a \"gp-tooltip-right\" class as well as being\n\t * able to pass data-gp-tooltip-options as an attribute to modify the jQuery UI Tooltip options for the current\n\t * tooltip.\n\t */\n\tvar perkifyTooltip = function(el) {\n\t\tvar options = $( el ).data( 'gp-tooltip-options' );\n\n\t\tif ( ! options && $( el ).hasClass( 'gp-tooltip-right' ) ) {\n\t\t\toptions = {\n\t\t\t\tclasses: {\n\t\t\t\t\t'ui-tooltip': 'gp-tooltip-right'\n\t\t\t\t},\n\t\t\t\tposition: {\n\t\t\t\t\tmy: 'right bottom',\n\t\t\t\t\tat: 'right+10 top-11',\n\t\t\t\t\tcollision: 'none'\n\t\t\t\t},\n\t\t\t\t// Fixes positional issues introduced by GF in [PR#1183](https://github.com/gravityforms/gravityforms/pull/1883/files).\n\t\t\t\topen: null\n\t\t\t};\n\t\t}\n\n\t\tif ( options ) {\n\t\t\t$( el ).tooltip( 'option', options );\n\t\t}\n\t};\n\n\t$( document ).on('gperks_tooltips_initialized', function() {\n\t\t$( '.gp-tooltip' ).each( function() {\n\t\t\tperkifyTooltip( this );\n\t\t} );\n\t});\n\n\tsetTimeout( function() {\n\t\t$( document ).trigger( 'gperks_tooltips_initialized' );\n\t} );\n\n} );\n"], "names": ["gperk", "confirmActionUrl", "event", "message", "url", "preventDefault", "elem", "j<PERSON><PERSON><PERSON>", "target", "prop", "confirm", "location", "href", "addTab", "id", "label", "tabClass", "replace", "altTabClass", "tabs", "find", "eq", "append", "beforeActivate", "ui", "newPanel", "document", "trigger", "form", "field", "togglePerksTab", "fieldTab", "hide", "fieldHasSettings", "show", "hasSetting", "each", "patt", "test", "attr", "toggleSection", "selector", "parents", "addClass", "gwpSlide", "removeClass", "isSingleProduct", "singleFieldTypes", "applyFilter", "inArray", "inputType", "getFieldLabel", "inputId", "isUndefined", "input", "getInput", "type", "isArray", "i", "toggleSettings", "toggleSettingsId", "isChecked", "animate", "settingsElem", "is", "gfSlide", "SetFieldProperty", "setInputProperty", "property", "value", "GetSelectedField", "inputs", "setFormProperty", "fn", "direction", "isVisibleSelector", "isVisible", "slideUp", "slideDown", "ready", "$", "on", "gform", "doAction", "window", "bind", "perkifyTooltip", "el", "options", "data", "hasClass", "classes", "position", "my", "at", "collision", "open", "tooltip", "setTimeout"], "mappings": "AAAA,GAAI,AAAgB,KAAA,IAATA,MACV,IAAIA,MAAQ,CAAC,CAGdA,CAAAA,MAAMC,gBAAgB,CAAG,SAASC,CAAK,CAAEC,CAAO,CAAEC,CAAG,EACpDF,EAAMG,cAAc,GAEpB,IAAIC,EAAOC,OAAQL,EAAMM,MAAM,CAE1B,CAAEJ,GACNA,CAAAA,EAAME,EAAKG,IAAI,CAAE,OAAO,EAGrBC,QAASP,IACZQ,CAAAA,SAASC,IAAI,CAAGR,CAAE,CAGpB,EAMAJ,MAAMa,MAAM,CAAG,SAASP,CAAI,CAAEQ,CAAE,CAAEC,CAAK,EAItC,IAAIC,EAAcF,EAAGG,OAAO,CAAE,IAAK,IAClCC,EAAcF,EAASC,OAAO,CAAE,MAAO,OACvCD,EAAcA,GAAYE,EAAcF,EAAW,IAAME,EAAcF,EAGxEV,EAAKa,IAAI,CAAE,WAGXb,EAAKc,IAAI,CAAE,MAAOC,EAAE,CAAE,GAAIC,MAAM,CAAE,gDAAkDN,EAAW,uBAC3EF,EAAK,KAAOC,EAAQ,sBAIxCT,EAAKgB,MAAM,CAAEf,OAAQO,IAGrBR,EAAKa,IAAI,CAAC,CACTI,eAAgB,SAASrB,CAAK,CAAEsB,CAAE,EACjC,OAASjB,OAAQiB,EAAGC,QAAQ,EAAGhB,IAAI,CAAE,OACpC,IAAK,eACJF,OAAQmB,UAAWC,OAAO,CAAE,qBAAsB,CAAEC,KAAM,EAC3D,KACA,KAAK,gBACJrB,OAAQmB,UAAWC,OAAO,CAAE,sBAAuB,CAAEE,MAAO,CAE9D,CACD,CACD,EAED,EAEA7B,MAAM8B,cAAc,CAAG,WAEtB,IAAIC,EAAWxB,OAAQ,iCAEvBwB,EAASC,IAAI,GAERhC,MAAMiC,gBAAgB,IAC1BF,EAASG,IAAI,EAGf,EAEAlC,MAAMiC,gBAAgB,CAAG,WAExB,IAAIE,EAAa,CAAA,EASjB,OAPA5B,OAAQ,kBAAmBa,IAAI,CAAE,oBAAqBgB,IAAI,CAAC,WAErD,AAAEC,AADI,qBACCC,IAAI,CAAE/B,OAAQ,IAAI,EAAGgC,IAAI,CAAE,WACtCJ,CAAAA,EAAa,CAAA,CAAG,CAElB,GAEOA,CACR,EAEAnC,MAAMwC,aAAa,CAAG,SAASlC,CAAI,CAAEmC,CAAQ,EAC5C,IAAInC,EAAOC,OAAQD,GAEfA,EAAKG,IAAI,CAAE,YACdH,EAAKoC,OAAO,CAAE,cAAeC,QAAQ,CAAE,QACvCpC,OAAQkC,GAAWG,QAAQ,CAAE,OAAQ,oBAErCtC,EAAKoC,OAAO,CAAE,cAAeG,WAAW,CAAE,QAC1CtC,OAAQkC,GAAWG,QAAQ,CAAE,KAAM,kBAGrC,EAEA5C,MAAM8C,eAAe,CAAG,SAASjB,CAAK,EAErC,OADAkB,iBAAmB/C,MAAMgD,WAAW,CAAE,qBAAsB,CAAC,gBAAiB,gBAAiB,cAAc,EACtGzC,AAAuD,IAAvDA,OAAO0C,OAAO,CAAEpB,EAAMqB,SAAS,CAAEH,iBACzC,EAEA/C,MAAMmD,aAAa,CAAG,SAAStB,CAAK,CAAEuB,CAAO,EAExCpD,MAAMqD,WAAW,CAAED,IACtBA,CAAAA,EAAU,CAAA,CAAI,EAGf,IAAIrC,EAAQc,EAAMd,KAAK,CACnBuC,EAAQtD,MAAMuD,QAAQ,CAAE1B,EAAOuB,SAEjB,YAAdvB,EAAM2B,IAAI,EAAkBF,AAAS,CAAA,GAATA,GAErBA,AAAS,CAAA,GAATA,EACHA,EAAMvC,KAAK,CAEXA,CAGT,EAEAf,MAAMuD,QAAQ,CAAG,SAAS1B,CAAK,CAAEuB,CAAO,EAEvC,GAAIpD,MAAMqD,WAAW,CAAExB,EAAM,MAAS,GAAMtB,OAAOkD,OAAO,CAAE5B,EAAM,MAAS,EAC1E,IAAK6B,KAAK7B,EAAM,MAAS,CAAE,CAC1B,IAAIyB,EAAQzB,EAAM,MAAS,CAAC6B,EAAE,CAC9B,GAAIJ,EAAMxC,EAAE,EAAIsC,EACf,OAAOE,CAET,CAGD,MAAO,CAAA,CACR,EAEAtD,MAAM2D,cAAc,CAAG,SAAS7C,CAAE,CAAE8C,CAAgB,CAAEC,CAAS,CAAEC,CAAO,EAEvE,IAAIxD,EAAeC,OAAQ,IAAMO,GAC7BiD,EAAexD,OAAQ,IAAMqD,GAC7BE,EAAe,AAAmB,KAAA,IAAZA,GAA0BA,EAG7C9D,MAAMgE,EAAE,CAAEH,EAAW,aAG3BA,EAAYvD,EAAK0D,EAAE,CAAE,YAFrB1D,EAAKG,IAAI,CAAE,UAAWoD,GAKlBA,EACCC,EACJC,EAAaE,OAAO,CAAE,QAEtBF,EAAa7B,IAAI,GAGb4B,EACJC,EAAaE,OAAO,CAAE,MAEtBF,EAAa/B,IAAI,GAInBkC,iBAAkBpD,EAAI+C,EAEvB,EAEA7D,MAAMmE,gBAAgB,CAAG,SAASf,CAAO,CAAEgB,CAAQ,CAAEC,CAAK,EAEzD,IAAIxC,EAAQyC,mBAEZ,IAAKZ,KAAK7B,EAAM0C,MAAM,CACjB1C,EAAM0C,MAAM,CAACb,EAAE,CAAC5C,EAAE,EAAIsC,GACzBvB,CAAAA,EAAM0C,MAAM,CAACb,EAAE,CAACU,EAAS,CAAGC,CAAI,CAInC,EAWArE,MAAMwE,eAAe,CAAG,SAASJ,CAAQ,CAAEC,CAAK,EAC/CzC,IAAI,CAACwC,EAAS,CAAGC,CAClB,EAUA9D,OAAOkE,EAAE,CAAC7B,QAAQ,CAAG,SAAS8B,CAAS,CAAEC,CAAiB,EAMzD,IAAIC,EAAYD,AAAsB,CAAA,IAAtBA,GAA+BA,AAAsB,CAAA,IAAtBA,EAA6BA,EAAoBpE,OAAQoE,GAAoBX,EAAE,CAAE,YAgBhI,MAdIU,AAAa,MAAbA,EACIE,EAGN,IAAI,CAACC,OAAO,GAFZ,IAAI,CAAC7C,IAAI,GAKH4C,EAGN,IAAI,CAACE,SAAS,GAFd,IAAI,CAAC5C,IAAI,GAMJ,IAAI,AACZ,EAEA3B,OAAQmB,UAAWqD,KAAK,CAAE,SAAWC,CAAC,EAGrCA,EAAG,qBAAsBC,EAAE,CAAE,oBAAqB,SAAU/E,CAAK,CAAEsB,CAAE,EAC/DA,EAAGC,QAAQ,CAACuC,EAAE,CAAE,uBACpBkB,MAAMC,QAAQ,CAAE,mCAAoCjF,EAAOoE,mBAAoBc,OAAOxD,IAAI,CAAEJ,EAE9F,GAKAjB,OAAQmB,UAAW2D,IAAI,CAAE,4BAA6B,SAAUnF,CAAK,CAAE2B,CAAK,EAC3E7B,MAAM8B,cAAc,EACrB,GAOA,IAAIwD,EAAiB,SAASC,CAAE,EAC/B,IAAIC,EAAUR,EAAGO,GAAKE,IAAI,CAAE,qBAEvB,EAAED,GAAWR,EAAGO,GAAKG,QAAQ,CAAE,qBACnCF,CAAAA,EAAU,CACTG,QAAS,CACR,aAAc,kBACf,EACAC,SAAU,CACTC,GAAI,eACJC,GAAI,kBACJC,UAAW,MACZ,EAEAC,KAAM,IACP,CAAA,EAGIR,GACJR,EAAGO,GAAKU,OAAO,CAAE,SAAUT,EAE7B,EAEAR,EAAGtD,UAAWuD,EAAE,CAAC,8BAA+B,WAC/CD,EAAG,eAAgB5C,IAAI,CAAE,WACxBkD,EAAgB,IAAI,CACrB,EACD,GAEAY,WAAY,WACXlB,EAAGtD,UAAWC,OAAO,CAAE,8BACxB,EAED"}