{"key": "post_type_686c268852abb", "title": "Service Locations", "menu_order": 0, "active": true, "post_type": "service-location", "advanced_configuration": true, "import_source": "", "import_date": "", "labels": {"name": "Service Locations", "singular_name": "Service Location", "menu_name": "Service Locations", "all_items": "All Service Locations", "edit_item": "Edit Service Location", "view_item": "View Service Location", "view_items": "View Service Locations", "add_new_item": "Add New Service Location", "add_new": "Add New Service Location", "new_item": "New Service Location", "parent_item_colon": "Parent Service Location:", "search_items": "Search Service Locations", "not_found": "No service locations found", "not_found_in_trash": "No service locations found in Trash", "archives": "Service Location Archives", "attributes": "Service Location Attributes", "featured_image": "", "set_featured_image": "", "remove_featured_image": "", "use_featured_image": "", "insert_into_item": "Insert into service location", "uploaded_to_this_item": "Uploaded to this service location", "filter_items_list": "Filter service locations list", "filter_by_date": "Filter service locations by date", "items_list_navigation": "Service Locations list navigation", "items_list": "Service Locations list", "item_published": "Service Location published.", "item_published_privately": "Service Location published privately.", "item_reverted_to_draft": "Service Location reverted to draft.", "item_scheduled": "Service Location scheduled.", "item_updated": "Service Location updated.", "item_link": "Service Location Link", "item_link_description": "A link to a service location."}, "description": "", "public": true, "hierarchical": true, "exclude_from_search": true, "publicly_queryable": true, "show_ui": true, "show_in_menu": true, "admin_menu_parent": "", "show_in_admin_bar": false, "show_in_nav_menus": false, "show_in_rest": false, "rest_base": "", "rest_namespace": "wp/v2", "rest_controller_class": "WP_REST_Posts_Controller", "menu_position": "", "menu_icon": {"type": "dashicons", "value": "dashicons-admin-post"}, "rename_capabilities": false, "singular_capability_name": "post", "plural_capability_name": "posts", "supports": ["title", "editor", "excerpt", "thumbnail", "custom-fields"], "taxonomies": "", "has_archive": false, "has_archive_slug": "", "rewrite": {"permalink_rewrite": "post_type_key", "with_front": "1", "feeds": "0", "pages": "1"}, "query_var": "post_type_key", "query_var_name": "", "can_export": true, "delete_with_user": false, "register_meta_box_cb": "", "enter_title_here": "", "modified": 1752139997}