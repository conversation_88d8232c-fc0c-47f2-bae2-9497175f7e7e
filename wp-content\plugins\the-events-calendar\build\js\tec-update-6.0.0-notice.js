!function(e,t){t.data=window.tecBlocksEditorUpdateNoticeData;const{__,sprintf:a}=e.i18n;e.data.dispatch("core/notices").createNotice("warning",`<b>${t.data.title}</b><p>${t.data.description}</p>`,{__unstableHTML:!0,isDismissible:!0,actions:[{url:t.data.upgrade_link,label:a(__("Upgrade your %1$s","the-events-calendar"),t.data.events_plural_lower)},{url:t.data.learn_link,label:__("Learn more","the-events-calendar")}]})}(window.wp,{}),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.tecUpdate600Notice={};