/**
* Styles for various Perk administration screens
*
*/

body.perk-iframe {
	background-color: #f7f7f7;
}

.perk-iframe .wrap {
	margin: 0;
}

.perk-iframe .wrap .content {
	padding: 30px;
	font-size: 15px;
	line-height: 22px;
	background-color: #fff;
}

.perk-iframe .wrap .content-footer {
	background-color: #f7f7f7;
	padding: 30px;
	border-top: 1px solid #eee;
	text-align: right;
}

.perk-iframe .header {
	background-color: #f7f7f7;
	border-bottom: 1px solid #eee;
}

.perk-iframe h1.page-title {
	padding: 30px;
	line-height: 1;
	margin: 0;
}

.perk-iframe .header .updated {
	margin: 0 30px 30px;
}

.perk-iframe .content h1 {
	font-weight: normal;
	margin: 50px 0 30px;
	line-height: 1;
	border-top: 1px solid #eee;
	padding-top: 40px;
}

.perk-iframe .content h1:first-child {
	margin-top: 20px;
	border-top: 0;
	padding-top: 0;
}

.perk-iframe .content h2 {
	font-weight: normal;
	margin: 30px 0 20px;
	line-height: 1;
	font-family: 'Merriweather', serif;
}

.perk-iframe .content form {
	margin: 0;
}

.perk-iframe .content ul {
	list-style: disc;
	margin-left: 20px;
	color: #555;
}

.perk-iframe .content li {
	margin: 0 0 20px;
}

.perk-iframe .content li span.description {
	margin: 0 0 20px;
	font-family: 'Merriweather', serif;
	display: block;
}

.perk-iframe .content p {
	line-height: 26px;
	margin: 0 0 24px;
	color: #555;
}

.perk-iframe .content-footer {
	background-color: #f7f7f7;
	padding: 30px;
	border-top: 1px solid #eee;
	line-height: 1;
	font-size: 15px;
}

.perk-settings { }

.perk-settings textarea {
	width: 75%;
	height: 150px;
}

.perk-settings-container {
	border-left: 2px solid #eee;
	margin: -18px 0 15px 6px;
	padding: 15px 0 15px 22px;
}

.gwp-field {
	margin: 0 0 18px;
	padding: 0 0 18px;
	border-bottom: 1px dotted #ddd;
}

.gwp-expandable {
}

.content > .gwp-field:nth-last-child(2),
.gwp-field:last-child,
.gwp-expandable.open {
	padding-bottom: 0;
	border-bottom: 0;
}

.gwp-field label {
	margin-bottom: 5px;
}

.gwp-field p.description {
	margin: 0 0 5px;
}

.gwp-checkbox input[type="checkbox"] {
	float: left;
}

.gwp-checkbox .label {
	margin-left: 30px;
}

.gwp-checkbox input[type="checkbox"]:checked + .label label {
	font-weight: bold !important;
}

.gwp-checkbox label + p {
	margin-bottom: 5px;
}

.gwp-select select {
	height: 2em;
	padding: 2px;
}

.gwp-select option {
	padding-right: 10px;
}

.gwp-field input[type="checkbox"],
.gwp-field input[type="radio"] {
	margin-top: 5px;
}

.documentation a.back {
	display: block;
	margin: 20px 0;
}

.documentation p {
	margin: 0 0 1.6em;
}

.documentation p img {
	border: 5px solid #eee;
}

.documentation p img.alignright {
	float: right;
	margin: 0 0 1.6em 20px;
}

.documentation p img.alignleft {
	float: left;
	margin: 0 20px 1.6em 0;
}

.documentation .notice {
	padding: 1em 1.2em;
	margin-bottom: 1.6em;
	color: #946E2B;
	text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
	background-color: #fcf8e3;
	border: 1px solid #fbeed5;
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	border-radius: 4px;
}

/* Form Editor Styles */

.field_setting .half {
	float: left;
	width: 47.5%;
	margin-left: 5%;
}

.field_setting .half:first-child {
	margin-left: 0;
}

.field_setting .third {
	float: left;
	width: 30%;
	margin-left: 5%;
}

.field_setting .third:first-child {
	margin-left: 0;
}

.gp-field-setting .gp-row {
	margin: 0 0 0.9375rem;
}

.gp-field-setting .gp-row:last-child {
	margin: 0;
}

.gp-field-setting .gp-row > label {
	margin-bottom: 0.7rem;
}

/* GF 2.5 provides awesome default styling for text inputs but not number inputs. Let's copy them here. */
body:not( .gf-legacy-ui ) .gp-field-setting input[type="number"] {
	max-height: 2.25rem;
	min-height: 2.25rem;
	width: 100%;
}

.gp-notice {
	background-color: #FFFFE0;
	border: 1px solid #F4EFB8;
	padding: 10px;
	border-radius: 2px;
}

.gp-notice i {
	color: #D4662C;
}

.gp-child-settings {
	border-left: 2px solid #ecedf8;
	padding: 0.875rem 0 0.875rem 0.875rem;
	margin-left: 11px;
	margin-top: 0.7rem;
}

.gf-legacy-ui .gp-child-settings {
	border-left: 2px solid #eee;
	padding: 15px;
	margin-left: 7px;
	margin-top: 5px;
}

.gp-setting {
	margin-bottom: 0.9375rem;
}

.panel-block-tabs__body--settings li.field_setting label.section_label + .gp-group {
	margin-top: 0;
}

.gp-group {
	display: flex;
	align-items: center;
}

.gp-group + .gp-group {
	margin-top: 0.5625rem;
}

.gp-group > div,
.gp-group > label,
.gp-group > input {
	flex: 1;
}

/*
 * Labels that are direct children of a group are displayed side-by-side with a corresponding input and vertically
 * centered. Prevent any margin from interfering with that.
 */
.gp-group > label {
	margin-bottom: 0 !important;
}

/* Force inputs in gp-group containers to fill their containers in < GF 2.4. */
#gws_field_tab .gp-group input,
#gws_field_tab .gp-group select {
	width: 100%;
	max-width: 100%;
}

/* WP Pointer */
.gwp-pointer .wp-pointer-buttons {
	display: inline-block;
}

.gwp-pointer .wp-pointer-buttons:first-of-type {
	margin: 0 0 0 15px;
}

.gwp-pointer .wp-pointer-buttons:last-of-type {
	float: right;
}

.gwp-pointer .wp-pointer-buttons:first-of-type:last-of-type {
	float: none;
	display: block;
}

.gwp-pointer a.next::before {
	content: '\f148';
	transform: scale(-1, 1);
	left: -9px;
}

.gp-tooltip-right:after {
	right: 12px;
}

.gp-tooltip-right.ui-tooltip.ui-widget-content {
	max-width: 12rem;
}




/**
 * Manage Perks
 */

#_gwp-header-links {
	float: right;
	margin-top: -36px;
}

#_gwp-header-links li {
	display: inline;
	padding-left: 10px;
}

.wrap div.gwp-message {
	margin-top: 15px;
}

.forms_page_gwp_perks .nav-tab-wrapper {
	padding-top: 0 !important;
}

.nav-tab-wrapper + .notice {
	margin: 20px 0 -15px;
}

.nav-tab-wrapper + .notice + .notice {
	margin: 35px 0 -15px;
}

.forms_page_gwp_perks .manage-menus {
	margin: 20px 0;
}

div.gwp-message + .manage-menus { }

#gwp_license_key {
	width: 50%;
	height: 29px;
	margin-top: 0px;
}

#gwp_license_submit,
#gw-buy-license {
	float: right;
}

#gw-buy-license {
	margin-left: 10px;
}

.gp-active-perks {}

.perks {
	padding: 20px 0 0;
}

.perks .manage-perks-intro,
.perks .install-perks-intro {
	border-bottom: 1px solid #eee;
	padding: 0 20px 20px;
	margin: 0 0 20px;
}

.perks .install-perks-intro h3 {
	margin-top: 0;
}

.perks .no-perks-installed,
.perks .all-perks-installed,
.perks .install-perks-api-message {
	padding: 60px 20px 20px;
	margin: 0 0 20px;
	text-align: center;
	font-size: 24px;
	line-height: 36px;
	width: 100%;
}

.perks .no-perks-installed a {
	cursor: pointer;
}

.perks .install-perks-api-message a {
	display: block;
	text-decoration: none;
}

#install.tab-container {
	position: relative;
}

#install #need-license-splash {
	background-color: #fff;
	position: absolute;
	z-index: 99;
	top: 100px;
	left: 50%;
	margin-left: -25%;
	width: 44%;
	padding: 3%;
	box-shadow: 0 0 100px 100px #fff;
}

#install #need-license-splash .perk-listing {
	width: auto;
}

#install #need-license-splash .perk-listing .wrap {
	min-height: 0;
}

#install #need-license-splash h3 {
	margin: 0 0 18px;
}

#install #need-license-splash p {
	margin: 0 0 18px;
}

#install #need-license-splash .dismiss-link {
	float: right;
	line-height: 22px;
}

.perk-listings {
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-flex-wrap: wrap;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
}

.perk-listing {
	background-color: #f4f8fc;
	box-shadow: 0 0 20px #D8E9FA inset;
	border: 1px solid #eee;
	float: left;
	margin: 0 10px 20px;
	min-width: 280px;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	width: calc( 25% - 30px );
}

@media only screen and (max-width: 1410px) {
	.perk-listing {
		width: calc( 33% - 20px );
	}
}

@media only screen and (max-width: 1110px) {
	.perk-listing {
		width: calc( 50% - 20px );
	}
}

.perk-listing .wrap {
	padding: 15px;
	border: 4px solid #fff;
	height: 170px;
	margin: 0;
	position: relative;
	display: flex;
	flex-direction: column;
	width: 100%;
}

.perk-listing h3 {
	margin: 0 0 4px;
}

.perk-listing p {
	margin: 0;
	padding: 0;
}

.perk-listing span.version {
	color: #6E9FB5;
	white-space: nowrap;
}

.perk-listing .actions {
	flex: 0;
	margin: auto 0;
}

.perk-listing .perk-description {
	flex: 1;
	position: relative;
	overflow: hidden;
	margin: 12px 0 0;
}

.perk-listing .perk-description::after {
	content: "";
	display: block;
	position: absolute;
	width: 100%;
	height: 20px;
	bottom: 0;
	background: -webkit-linear-gradient(transparent, #f7f7f7) left repeat;
	background: linear-gradient(transparent, #f7f7f7) left repeat;
}

.perks .perk-listing a.button-primary {
	color: #fff;
}

.perk-listing .network-activated-perk {
	background-color: #fff;
	padding: 5px 7px;
	line-height: 1;
	position: absolute;
	bottom: 0;
	right: 15px;
	border-top-left-radius: 4px;
	border-top-right-radius: 4px;
}

.perk-listing .network-activated-perk a {
	background: none repeat scroll 0 0 transparent;
	display: block;
	font-size: 10px;
	height: auto;
	text-decoration: none;
	text-indent: 0;
	text-transform: uppercase;
	width: auto;
}

.perk-listing .actions-buttons {
	margin: 7px 0 4px
}

.qtip-content ul {
	margin: 0;
}

.perk-listing.install,
.perk-listing.inactive {
	background-color: #f7f7f7;
	box-shadow: 0 0 20px #e7e7e7 inset;
	padding: 0;
}

.perk-listing.install span.version,
.perk-listing.inactive span.version {
	color: #999;
}

.perk-listing.perk-error {
	background-color: #FFEBE8;
	box-shadow: 0 0 20px #FCCCC8 inset;
}

.perk-listing.perk-error .perk-description::after {
	background: -webkit-linear-gradient(transparent, #FFEBE8) left repeat;
	background: linear-gradient(transparent, #FFEBE8) left repeat;
}

.perk-listing.perk-error span.version {
	color: #999;
}

.perk-listing.perk-error a {
	color: #7F564D;
}

.perk-listing.perk-error.failed-requirements a.gp-requirements {
	background-image: url(../images/icon-exclamation.png);
	text-indent: -999em;
	display: inline-block;
	height: 16px;
}

.perk-listing.perk-error .gp-unregistered {
	width: 20px;
	height: 20px;
	top: -10px;
	left: -10px;
	z-index: 2;
	position: absolute;
	opacity: 1;
	/* styles below override new GF defaults */
	text-decoration: none;
	margin: 0;
	border: 0;
	cursor: help;
}

.perk-listing.perk-error .gp-unregistered::before {
	width: 20px;
	height: 20px;
	color: #23282d;
	font-family: "dashicons";
	font-size: 24px;
	line-height: 20px;
	text-align: center;
	vertical-align: middle;
	text-indent: -2px;
	display: block;
	content: "\f534";
	background: white;
	border-radius: 100%;
}

.forms_page_gwp_perks .gwp_buy_license-pointer .wp-pointer-arrow {
	left: auto;
	right: 50px;
}

.forms_page_gwp_perks .gwp_register_license-pointer .wp-pointer-arrow {
	left: auto;
	right: 44px;
}

.forms_page_gwp_perks .gwp_get_support-pointer .wp-pointer-arrow {
	left: auto;
	right: 27px;
}

.forms_page_gwp_perks .gf_tooltip.uninstall {
	border: 0;
	background-color: transparent;
	margin: 0;
	width: auto;
	height: auto;
	border-radius: 0;
}

.forms_page_gwp_perks .ui-tooltip.gp-unregistered.ui-widget-content.arrow-bottom:after {
	left: 20px;
	margin: 0;
}

.forms_page_gwp_perks .ui-tooltip.ui-widget-content { }




.gp-selectwoo .select2-search--inline {
	margin: 0;
}


.gp-selectwoo.select2-container .select2-search--inline .select2-search__field {
	max-width: calc( 100% - 10px );
	padding: 0;
	margin: 0 0 5px;
	min-height: 28px;
}

.gp-selectwoo .select2-search--inline .select2-search__field:focus {
	border: 0 !important;
}

.gp-selectwoo.select2-container--default.select2-container--focus .select2-selection--multiple {
	/* Mirror WordPress focus styles */
	border-color: #007cba;
	box-shadow: 0 0 0 1px #007cba;
	outline: 2px solid transparent;
}

.gp-selectwoo.select2-selection {
	font-size: 0;
}

.gp-selectwoo.select2-selection * {
	font-size: 0.8rem;
}

.gp-selectwoo .select2-selection__rendered {
	padding-top: 5px !important;
}

.gp-selectwoo .select2-selection__choice {
	padding: 6px !important;
	margin-top: 0 !important;
	border: 0 !important;
}

.gp-selectwoo .select2-results__option {
	margin-bottom: 0;
}

.gp-selectwoo .select2-results__option:focus {
	outline: 0;
	background-color: #ddd;
}

.gp-selectwoo .select2-results__option[data-selected='true'] {
	padding-right: 30px;
	background: url( 'data:image/svg+xml;utf8,<svg width="12" height="10" viewBox="0 0 12 10" xmlns="http://www.w3.org/2000/svg"><path d="M4 6.58579L10.2929 0.292893C10.6834 -0.0976311 11.3166 -0.0976311 11.7071 0.292893C12.0976 0.683418 12.0976 1.31658 11.7071 1.70711L4 9.41421L0.292893 5.70711C-0.0976311 5.31658 -0.0976311 4.68342 0.292893 4.29289C0.683418 3.90237 1.31658 3.90237 1.70711 4.29289L4 6.58579Z" fill="%23C3C5DB" /></svg>' ) no-repeat right 10px center;
}

.gp-selectwoo .select2-results__option.select2-results__option--highlighted[data-selected='true'] {
	background: #5897fb url( 'data:image/svg+xml;utf8,<svg width="12" height="10" viewBox="0 0 12 10" xmlns="http://www.w3.org/2000/svg"><path d="M4 6.58579L10.2929 0.292893C10.6834 -0.0976311 11.3166 -0.0976311 11.7071 0.292893C12.0976 0.683418 12.0976 1.31658 11.7071 1.70711L4 9.41421L0.292893 5.70711C-0.0976311 5.31658 -0.0976311 4.68342 0.292893 4.29289C0.683418 3.90237 1.31658 3.90237 1.70711 4.29289L4 6.58579Z" fill="white" /></svg>' ) no-repeat right 10px center;
}
