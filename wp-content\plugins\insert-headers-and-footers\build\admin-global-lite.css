:root{--wpcode-background-gray:#F8F8F8;--wpcode-background-highlight:#F6FAFF;--wpcode-background-light:#F3F4F5;--wpcode-background-red:#DF2A35;--wpcode-background-white:#fff;--wpcode-border-color:#ddd;--wpcode-button-disabled-bg:#F5F5F5;--wpcode-button-disabled-border:1px solid #DDDDDD;--wpcode-button-disabled-text:#bbb;--wpcode-button-primary-bg:var(--wpcode-color-primary);--wpcode-button-primary-bg-hover:#397EEB;--wpcode-button-primary-text:#fff;--wpcode-button-primary-text-hover:#fff;--wpcode-button-secondary-bg:#F8F8F8;--wpcode-button-secondary-bg-hover:#fff;--wpcode-button-secondary-border:1px solid #DDDDDD;--wpcode-button-secondary-text:#777;--wpcode-button-orange-bg:#E06B44;--wpcode-button-orange-bg-hover:#e17f5e;--wpcode-button-secondary-text-hover:#454545;--wpcode-color-primary:#3568B7;--wpcode-color-red:#DF2A35;--wpcode-color-red-darker:#AB2028;--wpcode-font-size-l:18px;--wpcode-font-size-m:16px;--wpcode-font-size-s:14px;--wpcode-font-size-xl:22px;--wpcode-font-size-xs:12px;--wpcode-font-size-xxl:24px;--wpcode-input-border:1px solid #DDD;--wpcode-input-border-active:#3568B7;--wpcode-input-text-color:#454545;--wpcode-notice-success-bg:#09A347;--wpcode-notice-success-text:#fff;--wpcode-space-h:36px;--wpcode-space-v:24px;--wpcode-text-color-heading:#454545;--wpcode-text-color-highlight:#3568B7;--wpcode-text-color-light-bg:#848A8A;--wpcode-text-color-paragraph:#777777;--wpcode-text-color-placeholder:#bbb}@media screen and (max-width:782px){:root{--wpcode-space-h:24px}}.wpcode-admin-tabs{font-size:14px;list-style:none;margin:0;overflow:auto;padding:0}.wpcode-admin-tabs li{float:left;margin:0 30px 0 0;padding:0}.wpcode-admin-tabs li button{border:none;background:transparent;cursor:pointer}.wpcode-admin-tabs li button,.wpcode-admin-tabs li a{border-bottom:4px solid #fff;box-shadow:none;color:var(--wpcode-text-color-paragraph);display:block;font-weight:600;padding:20px 0 18px 0;text-decoration:none;transition:border 300ms ease}.wpcode-admin-tabs li button.wpcode_pro_type_lite,.wpcode-admin-tabs li a.wpcode_pro_type_lite{opacity:0.5}.wpcode-admin-tabs li button.wpcode_pro_type_lite:hover,.wpcode-admin-tabs li a.wpcode_pro_type_lite:hover{border:none}.wpcode-admin-tabs li button.wpcode_pro_type_lite:focus,.wpcode-admin-tabs li a.wpcode_pro_type_lite:focus{border:none}.wpcode-admin-tabs li button.active,.wpcode-admin-tabs li a.active{border-color:var(--wpcode-color-primary);color:var(--wpcode-text-color-heading)}.wpcode-admin-tabs li button:focus,.wpcode-admin-tabs li a:focus{border-color:var(--wpcode-color-primary)}.wpcode-admin-tabs li button:hover,.wpcode-admin-tabs li a:hover{border-color:var(--wpcode-text-color-paragraph)}.wpcode-admin-tab-content{display:none;position:relative}.wpcode-admin-tab-content.active{display:block}.wpcode-smart-tags{position:relative;display:inline-block;vertical-align:top}.wpcode-smart-tags.wpcode-smart-tags-unavailable .wpcode-smart-tags-toggle{opacity:0.6}.wpcode-smart-tags .wpcode-smart-tags-toggle{background:none;border:none;color:var(--wpcode-text-color-paragraph);font-size:var(--wpcode-font-size-s);cursor:pointer}.wpcode-smart-tags .wpcode-smart-tags-toggle svg{vertical-align:middle}.wpcode-smart-tags .wpcode-smart-tags-toggle span{text-decoration:underline}.wpcode-smart-tags .wpcode-smart-tags-toggle:hover span{text-decoration:none}.wpcode-smart-tags .wpcode-text-active{display:none}.wpcode-smart-tags .wpcode-smart-tags-dropdown{border:1px solid var(--wpcode-border-color);border-radius:4px;display:none;left:0;position:absolute;top:100%;z-index:1050;background:#fff;width:400px;margin-top:7px}.wpcode-smart-tags .wpcode-smart-tags-dropdown .wpcode-smart-tags-header{position:sticky;top:0;background:#fff;z-index:10;border-bottom:1px solid var(--wpcode-border-color);padding:10px 15px}.wpcode-smart-tags .wpcode-smart-tags-dropdown .wpcode-smart-tags-title{margin:0 0 10px 0;font-size:16px;font-weight:600;color:var(--wpcode-text-color-paragraph)}.wpcode-smart-tags .wpcode-smart-tags-dropdown .wpcode-smart-tags-search .wpcode-smart-tags-search-input{width:100%;padding:8px 10px;border:1px solid var(--wpcode-border-color);border-radius:4px;font-size:var(--wpcode-font-size-s);color:var(--wpcode-text-color-paragraph)}.wpcode-smart-tags .wpcode-smart-tags-dropdown .wpcode-smart-tags-search .wpcode-smart-tags-search-input:focus{outline:none;border-color:var(--wpcode-color-primary)}.wpcode-smart-tags .wpcode-smart-tags-dropdown .wpcode-smart-tags-list{max-height:400px;overflow-y:auto;padding-top:5px}.wpcode-smart-tags .wpcode-smart-tags-dropdown .wpcode-smart-tags-no-results{padding:15px;text-align:center;color:var(--wpcode-text-color-paragraph);font-size:var(--wpcode-font-size-s)}.wpcode-smart-tags .wpcode-smart-tags-dropdown ul{border-top:1px solid var(--wpcode-border-color)}.wpcode-smart-tags .wpcode-smart-tags-dropdown ul:first-child{border-top:none}.wpcode-smart-tags .wpcode-smart-tags-dropdown ul li{padding:0 15px;margin:12px 0}.wpcode-smart-tags .wpcode-smart-tags-dropdown .wpcode-smart-tag-category-label{font-weight:600;font-size:var(--wpcode-font-size-s);color:var(--wpcode-text-color-paragraph)}.wpcode-smart-tags.wpcode-smart-tags-open .wpcode-text-active{display:inline-block}.wpcode-smart-tags.wpcode-smart-tags-open .wpcode-text-default{display:none}.wpcode-smart-tags.wpcode-smart-tags-open .wpcode-smart-tags-dropdown{max-height:500px;overflow-y:auto;display:block}.wpcode-insert-smart-tag{border:0;color:var(--wpcode-text-color-paragraph);font-size:var(--wpcode-font-size-s);padding:0;text-align:left;background:none;cursor:pointer}.wpcode-insert-smart-tag:hover code{background-color:rgba(0,0,0,0.15)}.wpcode-smart-tags-dropdown-footer{padding:12px 15px;border-top:1px solid var(--wpcode-border-color);display:flex}.wpcode-smart-tags-dropdown-footer a{color:var(--wpcode-text-color-paragraph);display:inline-flex}.wpcode-smart-tags-dropdown-footer a .wpcode-icon{margin-right:5px}.wpcode-blur-area{filter:blur(2px);pointer-events:none}.wpcode-library .wpcode-content{position:relative}#poststuff .wpcode-upsell-box,.wpcode-upsell-box{position:absolute;z-index:20;background:var(--wpcode-background-white);width:662px;max-width:100%;top:50%;left:50%;transform:translate(-50%,-50%);padding:40px;text-align:center;border-radius:8px;border:1px solid var(--wpcode-border-color)}#poststuff .wpcode-upsell-box *,.wpcode-upsell-box *{box-sizing:border-box}#poststuff .wpcode-upsell-box h2,.wpcode-upsell-box h2{font-size:var(--wpcode-font-size-xxl);margin-top:0;line-height:1.2}#poststuff .wpcode-upsell-box .wpcode-button,.wpcode-upsell-box .wpcode-button{margin-top:6px}#poststuff .wpcode-upsell-box .wpcode-upsell-button-text,.wpcode-upsell-box .wpcode-upsell-button-text{margin-top:16px;display:inline-block;font-size:var(--wpcode-font-size-s);color:var(--wpcode-text-color-light-bg)}#poststuff .wpcode-upsell-box .wpcode-upsell-button-text:hover,.wpcode-upsell-box .wpcode-upsell-button-text:hover{text-decoration:none}#poststuff .wpcode-upsell-box p,.wpcode-upsell-box p{color:var(--wpcode-text-color-paragraph);font-size:var(--wpcode-font-size-m);line-height:1.5}#poststuff .wpcode-upsell-box.wpcode-upsell-box-with-features,.wpcode-upsell-box.wpcode-upsell-box-with-features{width:892px;padding:56px}#poststuff .wpcode-upsell-box.wpcode-upsell-box-with-features .wpcode-upsell-text,.wpcode-upsell-box.wpcode-upsell-box-with-features .wpcode-upsell-text{max-width:600px;margin:0 auto}#poststuff .wpcode-upsell-box .wpcode-upsell-features,.wpcode-upsell-box .wpcode-upsell-features{display:flex;flex-wrap:wrap;justify-content:space-between;margin-top:40px;margin-bottom:24px;text-align:left}#poststuff .wpcode-upsell-box .wpcode-upsell-features .wpcode-upsell-feature,.wpcode-upsell-box .wpcode-upsell-features .wpcode-upsell-feature{width:50%;color:var(--wpcode-text-color-heading);font-size:var(--wpcode-font-size-m);margin-bottom:16px;line-height:1.5;padding-left:34px;position:relative;padding-right:10px}#poststuff .wpcode-upsell-box .wpcode-upsell-features .wpcode-upsell-feature:before,.wpcode-upsell-box .wpcode-upsell-features .wpcode-upsell-feature:before{content:'';background-image:url("data:image/svg+xml,%3Csvg width=%2719%27 height=%2718%27 viewBox=%270 0 19 18%27 fill=%27none%27 xmlns=%27http://www.w3.org/2000/svg%27%3E%3Cpath fill-rule=%27evenodd%27 clip-rule=%27evenodd%27 d=%27M9.5 0.416016C4.67 0.416016 0.75 4.33602 0.75 9.16602C0.75 13.996 4.67 17.916 9.5 17.916C14.33 17.916 18.25 13.996 18.25 9.16602C18.25 4.33602 14.33 0.416016 9.5 0.416016ZM9.5 16.166C5.64125 16.166 2.5 13.0248 2.5 9.16602C2.5 5.30727 5.64125 2.16602 9.5 2.16602C13.3587 2.16602 16.5 5.30727 16.5 9.16602C16.5 13.0248 13.3587 16.166 9.5 16.166ZM7.75 11.0648L13.5163 5.29852L14.75 6.54102L7.75 13.541L4.25 10.041L5.48375 8.80727L7.75 11.0648Z%27 fill=%27%2309A347%27/%3E%3C/svg%3E");display:block;width:19px;height:18px;position:absolute;left:0;top:3px}.wpcode-pixel .wpcode-upsell-box .wpcode-upsell-text{max-width:750px}@media (max-width:1440px){.wpcode-library .wpcode-upsell-box{top:100px;transform:translate(-50%,0)}}.wpcode-button{background-color:var(--wpcode-button-primary-bg);border:1px solid var(--wpcode-button-primary-bg);border-radius:4px;color:var(--wpcode-button-primary-text);cursor:pointer;display:inline-block;font-size:var(--wpcode-font-size-s);font-weight:700;line-height:1;padding:10px 16px;text-decoration:none}.wpcode-button.wpcode-button-icon{align-items:center;display:inline-flex;padding-bottom:12px;padding-top:12px}.wpcode-button.wpcode-button-icon svg{margin-right:5px}.wpcode-button.wpcode-button-icon.wpcode-copy-target{padding-bottom:10px;padding-top:10px}.wpcode-button.wpcode-button-wide{padding-left:50px;padding-right:50px}.wpcode-button:hover,.wpcode-button:focus{background-color:var(--wpcode-button-primary-bg-hover);border-color:var(--wpcode-button-primary-bg-hover);color:var(--wpcode-button-primary-text-hover)}.wpcode-button.wpcode-button-secondary{background-color:var(--wpcode-button-secondary-bg);border:var(--wpcode-button-secondary-border);color:var(--wpcode-button-secondary-text)}.wpcode-button.wpcode-button-secondary:hover,.wpcode-button.wpcode-button-secondary:focus{background-color:var(--wpcode-button-secondary-bg-hover);color:var(--wpcode-button-secondary-text-hover)}.wpcode-button.wpcode-button-secondary.wpcode-button-secondary-inactive{background-color:var(--wpcode-button-disabled-bg);border-color:var(--wpcode-button-disabled-bg)}.wpcode-button.wpcode-button-secondary.wpcode-button-secondary-selected{border-color:var(--wpcode-button-primary-bg)}.wpcode-button.wpcode-button-orange{background-color:var(--wpcode-button-orange-bg);border-color:var(--wpcode-button-orange-bg);color:var(--wpcode-button-primary-text)}.wpcode-button.wpcode-button-orange:hover,.wpcode-button.wpcode-button-orange:focus{background-color:var(--wpcode-button-orange-bg-hover);color:var(--wpcode-button-primary-text)}.wpcode-button.wpcode-button-large{align-items:center;display:inline-flex;font-size:var(--wpcode-font-size-m);height:56px;justify-content:center;padding-left:var(--wpcode-space-h);padding-right:var(--wpcode-space-h);text-align:center}.wpcode-button.wpcode-button-large svg{margin-right:7px}.wpcode-button.wpcode-button-small{font-size:var(--wpcode-font-size-xs);padding:9px}.wpcode-button:disabled:hover,.wpcode-button:disabled{background-color:var(--wpcode-button-disabled-bg);border:var(--wpcode-button-disabled-border);color:var(--wpcode-button-disabled-text)}.wpcode-button-toggle{align-items:center;display:flex;justify-content:space-between}@media screen and (min-width:783px){.wpcode-button-toggle{min-width:424px}}.wpcode-button-toggle .wpcode-button{width:calc(50% - 5px);background:var(--wpcode-background-white);color:var(--wpcode-input-text-color)}.wpcode-button-toggle .wpcode-button-secondary{border:2px solid var(--wpcode-color-primary)}.wpcode-success-icon{display:none}.wpcode-show-success-icon .wpcode-success-icon{display:inline-block}.wpcode-show-success-icon .wpcode-default-icon{display:none}.wpcode-button-just-icon{background:none;border:none;cursor:pointer;padding:0}.wpcode-button-just-icon .wpcode-icon{display:block}.wpcode-text-button-icon{align-items:center;background:none;border:none;color:var(--wpcode-text-color-paragraph);cursor:pointer;display:inline-flex;font-size:var(--wpcode-font-size-s);font-weight:600;padding:0}.wpcode-text-button-icon:hover{color:var(--wpcode-text-color-heading)}.wpcode-text-button-icon:hover path{fill:var(--wpcode-text-color-heading)}.wpcode-text-button-icon .wpcode-icon{margin-right:5px}.wpcode-just-icon-button{background:none;border:none;cursor:pointer}.wpcode-button-text{background:none;border:none;color:var(--wpcode-text-color-paragraph);cursor:pointer;font-size:var(--wpcode-font-size-xs);padding:0;text-decoration:underline}.wpcode-button-text .wpcode-icon{margin-right:11px}.wpcode-button-text:focus,.wpcode-button-text:hover{text-decoration:none;background:none;color:var(--wpcode-text-color-heading)}.wpcode-button-text:focus svg path,.wpcode-button-text:hover svg path{fill:var(--wpcode-text-color-heading)}#wpbody-content .wpcode-button-ai-generate{position:relative;background:var(--wpcode-color-primary);border:none;color:white;cursor:pointer;overflow:hidden;z-index:1;border-radius:5px;transition:background-color 0.5s ease 0s,color 0.5s ease 0s}#wpbody-content .wpcode-button-ai-generate svg path{fill:#fff;transition:fill 0.5s ease 0s}#wpbody-content .wpcode-button-ai-generate .wpcode-button-ai-text-done,#wpbody-content .wpcode-button-ai-generate .wpcode-button-ai-text-loading{display:none}#wpbody-content .wpcode-button-ai-generate:focus,#wpbody-content .wpcode-button-ai-generate:hover{background:var(--wpcode-color-primary);color:#fff}#wpbody-content .wpcode-button-ai-generate:focus:before,#wpbody-content .wpcode-button-ai-generate:hover:before{opacity:0}#wpbody-content .wpcode-button-ai-generate:before{content:'';position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(90deg,#6e2dfb 0%,#a01cfd 100%);transition:opacity 0.5s ease;z-index:-1}#wpbody-content .wpcode-button-ai-generate.wpcode-button-ai-done,#wpbody-content .wpcode-button-ai-generate.wpcode-button-ai-loading{background:white;color:#a01cfd}#wpbody-content .wpcode-button-ai-generate.wpcode-button-ai-done:before,#wpbody-content .wpcode-button-ai-generate.wpcode-button-ai-loading:before{display:none}#wpbody-content .wpcode-button-ai-generate.wpcode-button-ai-done svg path,#wpbody-content .wpcode-button-ai-generate.wpcode-button-ai-loading svg path{fill:#a01cfd;animation-name:wpcode-shape-scale;animation-duration:2s;animation-iteration-count:infinite;transform-origin:center center;transform-box:fill-box}#wpbody-content .wpcode-button-ai-generate.wpcode-button-ai-done g:nth-child(2) path,#wpbody-content .wpcode-button-ai-generate.wpcode-button-ai-loading g:nth-child(2) path{animation-delay:0.75s}#wpbody-content .wpcode-button-ai-generate.wpcode-button-ai-done g:nth-child(3) path,#wpbody-content .wpcode-button-ai-generate.wpcode-button-ai-loading g:nth-child(3) path{animation-delay:1.5s}#wpbody-content .wpcode-button-ai-generate.wpcode-button-ai-done .wpcode-button-ai-text-default,#wpbody-content .wpcode-button-ai-generate.wpcode-button-ai-loading .wpcode-button-ai-text-default{display:none}#wpbody-content .wpcode-button-ai-generate.wpcode-button-ai-done .wpcode-button-ai-text-loading,#wpbody-content .wpcode-button-ai-generate.wpcode-button-ai-loading .wpcode-button-ai-text-loading{display:block}#wpbody-content .wpcode-button-ai-generate.wpcode-button-ai-done .wpcode-button-ai-text-done{display:block}#wpbody-content .wpcode-button-ai-generate.wpcode-button-ai-done .wpcode-button-ai-text-default,#wpbody-content .wpcode-button-ai-generate.wpcode-button-ai-done .wpcode-button-ai-text-loading{display:none}#wpbody-content .wpcode-button-ai-generate.wpcode-button-ai-not-available{filter:grayscale(1)}.wpcode-ai-improve{margin-right:14px;padding:10px 10px}.wpcode-ai-improve svg path{fill:#a01cfd}.wpcode-items-metabox{display:flex;padding:0}@media screen and (max-width:782px){.wpcode-items-metabox{flex-flow:column}}.wpcode-items-sidebar{flex-shrink:0;padding:var(--wpcode-space-v);width:242px}@media screen and (max-width:782px){.wpcode-items-sidebar{width:100%}}.wpcode-items-list{border-left:1px solid var(--wpcode-border-color);min-height:400px;padding:calc(var(--wpcode-space-v) / 2) 12px;width:100%}@media screen and (max-width:782px){.wpcode-items-list{border-top:1px solid var(--wpcode-border-color);border-left:0}}.wpcode-items-categories-list{margin:0}.wpcode-items-categories-list li{margin:0}.wpcode-items-categories-list button{background:transparent;border:none;color:var(--wpcode-text-color-paragraph);cursor:pointer;display:block;font-size:var(--wpcode-font-size-m);font-weight:500;padding:12px 8px;position:relative;text-align:left;width:100%}.wpcode-items-categories-list button:focus,.wpcode-items-categories-list button.wpcode-active{background:var(--wpcode-background-highlight);color:var(--wpcode-text-color-highlight)}.wpcode-items-categories-list button:hover{text-decoration:underline}.wpcode-items-categories-list button .wpcode-items-count{float:right;color:var(--wpcode-text-color-paragraph);opacity:0.5}.wpcode-items-categories-list button.wpcode-active{font-weight:700}.wpcode-items-categories-list button.wpcode-active:hover{text-decoration:none}.wpcode-items-categories-list button.wpcode-active .wpcode-items-count{opacity:1;color:var(--wpcode-text-color-highlight);font-weight:700}.wpcode-items-list-category{align-content:stretch;display:flex;flex-wrap:wrap;justify-content:flex-start}.wpcode-list-item{border:1px solid var(--wpcode-border-color);border-radius:4px;margin-bottom:24px;margin-right:12px;margin-left:12px;max-width:100%;padding:16px 20px;position:relative;width:100%}@media (min-width:961px){.wpcode-list-item{width:calc(50% - 24px)}}@media (min-width:1440px){.wpcode-list-item{width:calc(100% / 3 - 24px)}}.wpcode-list-item h3,.wpcode-list-item .wpcode-list-item-title{font-size:var(--wpcode-font-size-m);line-height:1.2;margin:0;overflow:hidden;position:relative;text-overflow:ellipsis;white-space:nowrap}.wpcode-list-item p{margin-bottom:0}.wpcode-list-item:hover .wpcode-list-item-description,.wpcode-list-item:focus .wpcode-list-item-description{opacity:0}.wpcode-list-item:hover .wpcode-list-item-buttons,.wpcode-list-item:focus .wpcode-list-item-buttons{opacity:1}@media (hover:none){.wpcode-list-item .wpcode-list-item-description{opacity:0}.wpcode-list-item .wpcode-list-item-buttons{opacity:1}}.wpcode-list-item .wpcode-list-item-pill{position:absolute;top:10px;right:10px;font-size:8px;font-weight:700;text-transform:uppercase;line-height:1;padding:4px 8px;border-radius:40px}.wpcode-list-item .wpcode-list-item-pill.wpcode-list-item-pill-blue{background:var(--wpcode-color-primary);color:#fff}.wpcode-list-item .wpcode-list-item-pill.wpcode-list-item-pill-green{background:var(--wpcode-notice-success-bg);color:#fff}.wpcode-list-item .wpcode-list-item-pill.wpcode-list-item-pill-gray{background:var(--wpcode-text-color-heading);color:#fff}.wpcode-list-item .wpcode-list-item-pill.wpcode-list-item-pill-light{background:var(--wpcode-text-color-placeholder);color:#fff}.wpcode-list-item.wpcode-list-item-has-pill h3{max-width:calc(100% - 60px)}.wpcode-list-item.wpcode-custom-snippet,.wpcode-list-item.wpcode-library-item-ai{background:var(--wpcode-background-highlight)}.wpcode-list-item-actions{position:relative}.wpcode-list-item-description{min-height:40px}.wpcode-list-item-buttons{display:flex;justify-content:space-between;opacity:0;position:absolute;top:0;width:100%;z-index:10}.wpcode-list-item-buttons .wpcode-button{flex-grow:1;margin-left:10px;text-align:center}.wpcode-list-item-buttons .wpcode-button:first-child{margin-left:0}.wpcode-items-search{margin-bottom:20px;position:relative}.wpcode-items-search input{border-color:var(--wpcode-border-color);font-size:var(--wpcode-font-size-s);height:38px;padding-left:32px;width:100%}.wpcode-items-search input::-moz-placeholder{color:var(--wpcode-text-color-placeholder)}.wpcode-items-search input::placeholder{color:var(--wpcode-text-color-placeholder)}.wpcode-items-search label{left:10px;position:absolute;top:11px}.wpcode-library-preview-header{padding-bottom:25px}.wpcode-library-preview-header h2{margin:0}.wpcode-library-preview-header .wpcode-close-modal{float:right}.wpcode-library-preview-content .CodeMirror{background:var(--wpcode-background-gray)}.wpcode-library-preview-content .CodeMirror-activeline-background{background:transparent !important}.wpcode-library-preview-content .CodeMirror-focused .CodeMirror-activeline-background{background:rgba(100,100,100,0.1) !important}.wpcode-library-preview-buttons{margin-top:25px}.wpcode-item-use-button.wpcode-start-auth{padding-top:6px;padding-bottom:6px;justify-content:center}.wpcode-item-use-button.wpcode-start-auth svg{margin-right:9px}#wpcode-library-connect-banner{background:var(--wpcode-button-disabled-bg);margin-bottom:24px;margin-left:12px;border-radius:6px;display:flex;flex-direction:column;justify-content:space-between;align-items:center;padding-right:20px;margin-right:12px}@media (min-width:961px){#wpcode-library-connect-banner{flex-direction:row}}#wpcode-library-connect-banner .wpcode-template-content{padding:20px;color:#444444}@media (min-width:961px){#wpcode-library-connect-banner .wpcode-template-content{width:80%}}#wpcode-library-connect-banner .wpcode-template-upgrade-button{text-align:right;flex-shrink:0;padding-bottom:20px}@media (min-width:961px){#wpcode-library-connect-banner .wpcode-template-upgrade-button{padding-bottom:0}}#wpcode-library-connect-banner .wpcode-template-upgrade-button button{white-space:pre}#wpcode-library-connect-banner h3{margin:0 0 5px}#wpcode-library-connect-banner p{margin:0}.wpcode-library-item-ai-not-available .wpcode-button{opacity:0.7;pointer-events:none}.wpcode-content *{box-sizing:border-box}.wpcode-metabox-snippets:after{display:table;clear:both;content:''}#wpcode-picked-snippets{align-content:stretch;display:flex;flex-wrap:wrap;justify-content:flex-start}.wpcode-snippet-picker-item{border-bottom:1px solid var(--wpcode-border-color);padding:15px 0;display:flex;justify-content:space-between;color:var(--wpcode-text-color-paragraph);font-size:var(--wpcode-font-size-m)}.wpcode-snippet-picker-item .wpcode-checkbox-toggle{flex-shrink:0;flex-grow:0}.wpcode-selected-snippet-item h4{margin-top:12px}.wpcode-selected-snippet-item label{margin:5px 0;display:block}#wpcode-snippet-chooser{width:30%;float:left}#wpcode-snippet-chooser .wpcode-chooser-fixed-height{height:400px;overflow:auto}.wpcode-picked-snippets-area{margin-left:30%;padding-left:16px}.wpcode-picked-snippets-area>h3{margin-left:12px}.wpcode-snippet-chooser-closed #wpcode-snippet-chooser{width:0;overflow:hidden}.wpcode-snippet-chooser-closed .wpcode-picked-snippets-area{margin-left:0;padding-left:0}.wpcode-snippet-chooser-closed .wpcode-picked-snippets-area>h3{margin-left:0}.wpcode-snippet-chooser-closed #wpcode-picked-snippets{margin:0 -12px}.wpcode-snippets-search{padding-bottom:16px;position:relative}.wpcode-snippets-search input{width:100%}#wpcode-close-drawer{float:right}.wpcode-input-row{margin:24px 0}.wpcode-input-row label{font-size:var(--wpcode-font-size-s);color:var(--wpcode-text-color-heading);margin-bottom:20px;display:inline-block;font-weight:600}#wpcode-no-snippets-selected{margin-left:12px}.wpcode-choose-actions{text-align:center}#wpcode-metabox-load-more{margin:15px 0}#wpcode-chooser-spinner{right:0;top:12px}.wpcode-auto-insert-number-wrap.wpcode-hidden{display:none}.wpcode-list-item-location-details{display:flex}.wpcode-list-item-location-details .wpcode-auto-insert-location-wrap{width:100%}.wpcode-list-item-location-details .wpcode-auto-insert-number-wrap input{width:100px}.wpcode-input-row textarea{width:100%;min-height:300px}.wpcode-admin-tabs .wpcode-page-scripts-testing-mode-indicator{float:right;margin-right:0}.wpcode-admin-tabs .wpcode-page-scripts-testing-mode-indicator a{background:#09A347;color:#fff;padding-left:10px;padding-right:10px}#wpcode-tab-revisions .wpcode-revisions-list-area{min-height:250px}#wpcode-tab-revisions .wpcode-loading-spinner{display:block;top:50%;left:50%;width:48px;height:48px;background-size:48px 48px;transform:translate(-50%,-50%)}.wpcode-revisions-list-area{margin:24px 0 24px;position:relative}.wpcode-revisions-list-area #wpcode-show-all-snippets{margin-top:24px}.wpcode-revisions-list{border-left:1px solid var(--wpcode-border-color);padding-left:22px;margin:0}.wpcode-revisions-list.wpcode-revisions-list-collapsed{display:none}.wpcode-revisions-list.wpcode-revisions-list-extra{margin-top:0}.wpcode-revisions-list.wpcode-revisions-list-extra .wpcode-revision-list-item:first-child{background-color:var(--wpcode-button-disabled-bg)}.wpcode-revision-list-item{background:var(--wpcode-button-disabled-bg);padding:6px 9px;font-size:var(--wpcode-font-size-s);position:relative}.wpcode-revision-list-item .avatar{border-radius:50%;vertical-align:middle}.wpcode-revision-list-item:before{position:absolute;content:'';width:7px;height:7px;border-radius:50%;background-color:#C4C4C4;border:2px solid #FFF;right:100%;margin-right:17px;top:50%;margin-top:-5px}.wpcode-revision-list-item:first-child{background-color:var(--wpcode-background-highlight)}.wpcode-revision-list-author{display:inline-block;margin:0 10px 0 12px;font-weight:600;color:var(--wpcode-text-color-heading)}.wpcode-revision-list-date{color:var(--wpcode-text-color-light-bg)}.wpcode-remote-icon{background:#c5c5c6;border-radius:50%;display:inline-block;width:30px;height:30px;vertical-align:middle;text-align:center;line-height:30px}.wpcode-remote-icon svg path{fill:#fff}.wpcode-revision-list-item-actions{float:right;display:inline-flex;vertical-align:middle;align-items:center;margin-top:7px;margin-right:6px}.wpcode-revision-list-item-actions span,.wpcode-revision-list-item-actions a{font-size:12px;color:var(--wpcode-text-color-paragraph);margin-left:14px}.wpcode-revision-list-item-actions span:hover,.wpcode-revision-list-item-actions a:hover{text-decoration:none}

