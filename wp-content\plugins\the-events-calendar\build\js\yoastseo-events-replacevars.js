(()=>{class e{static PLUGIN_NAME="eventsVariablePlugin";static MODIFIABLE_FIELDS=["content","title","snippet_title","snippet_meta","primary_category","data_page_title","data_meta_desc"];constructor(){this.isReplaceVarPluginAvailable()&&(this.app=YoastSEO.app,this.store=YoastSEO.store,this.placeholders=new Map,this.app.registerPlugin(e.PLUGIN_NAME,{status:"ready"}),this.registerReplacements(),this.registerModifications(),this.registerEvents())}isReplaceVarPluginAvailable(){const e=window.YoastReplaceVarPlugin?.ReplaceVar;return e?(this.ReplaceVar=e,!0):(window.tecYoastEvents?.debug&&console.log("Replacing Event variables in the Snippet Window requires Yoast SEO >= 5.3."),!1)}registerEvents(){const e=["#EventStartDate","#EventEndDate",'[name="venue[City]"]','[name="venue[State]"]','[name="organizer[Organizer]"]'].join(", ");jQuery(document).on("change",e,(()=>this.declareReloaded()))}registerReplacements(){[{name:"event_start_date",getter:()=>this.getEventStartDate()},{name:"event_end_date",getter:()=>this.getEventEndDate()},{name:"venue_title",getter:()=>this.getVenueTitle()},{name:"venue_city",getter:()=>this.getVenueCity()},{name:"venue_state",getter:()=>this.getVenueState()},{name:"organizer_title",getter:()=>this.getOrganizerTitle()}].forEach((({name:e,getter:t})=>{const n=`%%${e}%%`,a=new this.ReplaceVar(n,e);this.placeholders.set(n,{replacement:a,getter:t}),this.store.dispatch({type:"SNIPPET_EDITOR_UPDATE_REPLACEMENT_VARIABLE",name:e,value:n})}))}registerModifications(){const t=this.replaceVariables.bind(this);e.MODIFIABLE_FIELDS.forEach((n=>{this.app.registerModification(n,t,e.PLUGIN_NAME,10)}))}replaceVariables(e){if(void 0===e)return e;let t=e;for(const[e,{getter:n}]of this.placeholders){const a=n();t=t.replace(new RegExp(e,"g"),a)}return t}declareReloaded(){this.app.pluginReloaded(e.PLUGIN_NAME),this.store.dispatch({type:"SNIPPET_EDITOR_REFRESH"})}getEventStartDate(){return jQuery("#EventStartDate").val()||""}getEventEndDate(){return jQuery("#EventEndDate").val()||""}getVenueTitle(){const e=jQuery('select[name="venue[VenueID][]"]');return e.length?e.find("option:selected").text().trim()||"":jQuery('input[name="venue[Venue][]"]').val()||""}getVenueCity(){return jQuery('[name="venue[City]"]').val()||""}getVenueState(){return jQuery('[name="venue[State]"]').val()||""}getOrganizerTitle(){const e=jQuery('select[name="organizer[OrganizerID][]"]');return e.length?e.find("option:selected").text().trim()||"":jQuery('input[name="organizer[Organizer][]"]').val()||""}}"undefined"==typeof YoastSEO||void 0===YoastSEO.app?jQuery(window).on("YoastSEO:ready",(()=>{new e})):new e,window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.yoastseoEventsReplacevars={}})();