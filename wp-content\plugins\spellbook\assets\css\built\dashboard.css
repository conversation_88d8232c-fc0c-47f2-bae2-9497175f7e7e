.error-boundary {
    padding: 2rem;
    text-align: center;
    max-width: 800px;
    margin: 2rem auto;
    background-color: #fff;
    border-radius: 3px;
    box-shadow: 0 1px 4px rgba(18, 25, 97, 0.0779552);
    border: 1px solid #E4E4EE;
}

.error-boundary__title {
    margin-bottom: 1rem;
    color: #23282D;
}

.error-boundary__message {
    margin-bottom: 1rem;
    color: #50575E;
}

.error-boundary__details {
    margin: 1.5rem 0;
    padding: 1rem;
    background-color: #F8F9FA;
    border-radius: 3px;
    text-align: left;
    font-family: monospace;
    font-size: 0.9em;
    color: #50575E;
    border: 1px solid #E4E4EE;
}

.error-boundary__error-name {
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.error-boundary__error-message {
    margin-bottom: 0.5rem;
}

.error-boundary__stack-trace {
    margin: 0;
    white-space: pre-wrap;
    word-break: break-word;
    font-size: 0.8em;
    opacity: 0.8;
}

.error-boundary__support {
    margin-bottom: 2rem;
    color: #50575E;
}

.error-boundary__support-link {
    color: #2271B1;
    text-decoration: none;
}

.error-boundary__support-link:hover {
    text-decoration: underline;
}

.error-boundary__actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.spellbook-admin .spellbook-app__header {
    background: #fff;
    block-size: 62px;
    border-bottom: 1px solid #ecedf8;
    display: flex;
    inline-size: 100%
}

.spellbook-admin .spellbook-app__header-logo {
    align-items: center;
    background: #902EEF;
    block-size: 62px;
    border-right: 1px solid #ecedf8;
    display: flex;
    inline-size: 95px;
    justify-content: center;
	overflow: hidden;
}

@container spellbook (max-width: 900px) {
    .spellbook-admin .spellbook-app__header-logo {
        inline-size: 70px;
    }
}

.spellbook-admin .spellbook-app__header-logo svg {
	width: 46px;
	height: auto;
}

.spellbook-admin .spellbook-app__header-main {
    align-items: center;
    block-size: 62px;
    display: flex;
    justify-content: space-between;
    padding: 0 16px
}

.spellbook-app {
	position: fixed;
	height: calc(100vh - 32px);
	width: calc(100% - 160px);
    container-type: size;
    container-name: spellbook;
}

/* Folded menu state */
body.folded .spellbook-app {
	width: calc(100% - 36px);
}

/* WordPress auto-fold breakpoint */
@media screen and (max-width: 960px) {
    .spellbook-app {
        width: calc(100% - 36px);
    }
}

@container spellbook (max-width: 900px) {
    /* Container-specific styles */
}

@container spellbook (max-height: 560px) {
    .gform-router-nav-bar__item-link {
        height: 1.875rem !important;
        padding: .375rem .5rem !important;
    }

    .gform-router-nav-bar__item-text {
        font-size: var(--gform-font-size-text-xs);
    }
}

/* WordPress tablet breakpoint */
@media screen and (max-width: 782px) {
	.spellbook-app {
		width: 100%;
		position: relative;
		height: auto;
		margin-left: 0;
	}

	/* When mobile menu is opened */
	.wp-responsive-open .spellbook-app {
		margin-left: -20px;
	}
}

/* WordPress mobile breakpoint */
@media screen and (max-width: 600px) {
	.spellbook-app {
		margin-left: 0;
	}

	.wp-responsive-open .spellbook-app {
		margin-left: 0;
	}
}

/* Adjust admin bar spacing on mobile */
@media screen and (max-width: 782px) {
	.spellbook-app {
		height: calc(100vh - 46px); /* WordPress mobile admin bar height */
	}
}

.forms_page_gwp_perks #wpcontent {
	padding-left: 0;
}

.forms_page_gwp_perks #wpfooter {
	display: none !important;
}

.spellbook-admin .gform-modular-sidebar__content {
    background: #f7f9fc;
}

.spellbook-app__content {
	padding-block: 1.5rem 6rem;
	padding-inline: 1.5rem;
}

/* Global button styles */
.spellbook-app__content .gform-button--primary-new {
	height: 34px !important;
	background: #902EEF;
	border: 0 !important;
}

.spellbook-app__content .gform-button--primary-new:hover {
	background: #561c8f;
}

.spellbook-app__content .gform-button--primary-new:disabled {
	background: #bc82f5;
}

.spellbook-app__content .gform-input {
	height: 34px
}

.spellbook-admin .gform-modular-sidebar__primary-sidebar {
    border-right: 1px solid #ecedf8;
    inline-size: 95px;
	background: #fff;
}

@container spellbook (max-width: 900px) {
    .spellbook-admin .gform-modular-sidebar__primary-sidebar {
        inline-size: 70px;
    }
}

.gform-router-nav-bar {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.gform-router-nav-bar__list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1rem 0;
}

.gform-admin .gform-router-nav-bar__list.gform-router-nav-bar__list--bottom {
    margin-top: auto;
}

/* Handle text switching at different sizes */
.spellbook-app__nav-bar-item .nav-text-short {
    display: none;
}

@container spellbook ((max-width: 900px) or (max-height: 560px)) {
    .spellbook-app__nav-bar-item .nav-text-full {
        display: none;
    }

    .spellbook-app__nav-bar-item .nav-text-short {
        display: inline;
    }

    .gform-router-nav-bar__item-link {
        height: 1.875rem !important;
        padding: .375rem .5rem !important;
    }
}

.gform-card__top-container-buttons {
	gap: 0.5rem;
}

.spellbook-app__search-highlight {
    background-color: rgba(144, 46, 239, 0.1);
    color: #902EEF;
    padding: 0 2px;
    border-radius: 2px;
    font-weight: 500;
}

.spellbook-plugin-settings-modal .components-modal__content {
	position: relative;
	margin: 0;
	padding: 0;
	width: 40vw;
}

.spellbook-plugin-settings-modal .components-modal__header {
	position: absolute;
	top: 0;
}

/* Hide text title in plugin settings modal */
.spellbook-plugin-settings-modal .components-modal__header-heading-container {
	display: none;
}

.registration-required-tag {
	background-color:rgb(251, 216, 208) !important;
	color: #dd301d !important;
	border: 1px solid rgb(241, 183, 170);
}

.product-card__tags {
	display: flex;
	gap: 0.5rem;
	margin-bottom: 0.5rem;
	flex-wrap: wrap;
}

.product-card__tags > button {
	cursor: pointer;
	border: none;
	background: none;
	padding: 0;
}

.product-card__tags .gform-tag {
	height: 22px;
	box-sizing: border-box;
	padding-block: 0;
	line-height: 21px;
}

.gform-card__top-container {
	display: flex;
	flex-direction: column;
}

.product-card__content {
	display: flex;
	flex-direction: column;
	min-height: 0;
	flex: 1;
}

.product-card__buttons {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
	margin-top: auto;
	align-self: flex-start;
	padding-top: 1rem;
}

.gform-card__top-container-description {
	margin: 0;
}

.product-card__header-buttons {
	display: flex;
	gap: 0.5rem;
}

.version-tag--outdated {
    background-color:rgb(249, 236, 197) !important;
    color: #a16938 !important;
	border: 1px solid rgb(233, 209, 187) !important;
    cursor: pointer;
}

.version-tag--available {
    background-color: #e1f6ed !important;
    color: #276a52 !important;
    border: 1px solid #aed9b6 !important;
    cursor: pointer;
}

.changelog-modal {
    width: 60vw !important;
}

.changelog-modal .components-modal__content {
    padding: 2rem;
}

.changelog-modal h1,
.changelog-modal h2,
.changelog-modal h3,
.changelog-modal h4 {
    margin-top: 1.5rem;
    margin-bottom: 1rem;
}

.changelog-modal h1:first-child,
.changelog-modal h2:first-child,
.changelog-modal h3:first-child,
.changelog-modal h4:first-child {
    margin-top: 0;
}

.changelog-modal ul {
    margin: 1rem 0;
    padding-left: 2rem;
}

.changelog-modal li {
    margin: 0.5rem 0;
	list-style: disc;
}

.changelog-modal code {
    background: #f5f5f5;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-size: 0.9em;
}

.gform-card--integration .gform-text.gform-text--color-dark-red {
	color: #af2718 !important;
}

.gform-grid--container.spellbook-app__product-card-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(18.5rem, 1fr));
	grid-auto-rows: 1fr;
}

.gform-card--integration {
	height: 100%;
}

.license-bar-all {
    position: relative;
    background: radial-gradient(ellipse 50.04% 360.95% at 100.09% 50.21%, rgba(144, 46, 239, 0.24) 0%, rgba(7, 28, 38, 0.24) 100%), #071C26;
    border-radius: 3px;
	box-shadow: 0 2px 2px rgba(58, 58, 87, 0.0596411);
    overflow: hidden;
    font-family: inter, -apple-system, blinkmacsystemfont, "Segoe UI", roboto, oxygen-sans, ubuntu, cantarell, "Helvetica Neue", sans-serif;
    margin-bottom: 20px;
    padding: 24px;
    display: flex;
    gap: 24px;
}

.license-bar-all .spellbook-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
    min-height: 0;
}

@container spellbook (max-width: 960px) {
    .license-bar-all {
        flex-direction: column;
    }
}

.license-bar-all .spellbook-section-title {
    color: white;
    font-size: 20px;
    font-weight: 700;
    line-height: 20px;
    margin: 0;
    padding: 0;
}

.license-bar-all .spellbook-form-container {
    display: flex;
    flex-direction: row;
    gap: 8px;
	flex-wrap: wrap;
}

.license-bar-all .spellbook-input-group {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    width: 100%;
}

@container spellbook (min-width: 1100px) {
    .license-bar-all .spellbook-section:last-child .spellbook-input-group {
        flex-wrap: nowrap;
    }
}

.license-bar-all .spellbook-input-wrapper {
    flex: 1;
    min-width: 0;
}

.license-bar-all .spellbook-form-input {
    padding: 8px 16px;
    border-radius: 4px;
    border: 1px solid #D4ECF7;
    background: transparent;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 400;
    line-height: 14px;
    min-height: 0;
    box-shadow: none;
	flex: 1;
}

/* Brighter placeholder text */
.license-bar-all .spellbook-form-input::placeholder {
    color: rgba(255, 255, 255, 0.45);
}

/* Override WordPress focus styles */
.license-bar-all .spellbook-form-input:focus {
    border: 1px solid #bc82f5 !important;
    box-shadow: 0 0 0 1px #bc82f5 !important;
    outline: none !important;
}

/* Adjust width for registration form inputs */
.license-bar-all .spellbook-section:last-child .spellbook-form-input {
    min-width: 200px;
    max-width: 300px;
}

.license-bar-all .spellbook-form-button {
    padding: 8px 16px;
    background: #902EEF;
    border-radius: 4px;
    color: white;
    border: none;
    font-size: 14px;
    line-height: 14px;
    cursor: pointer;
    white-space: nowrap;
    min-height: 0;
    box-shadow: none;
    display: flex;
    align-items: center;
	flex: 0;
}

.license-bar-all .spellbook-license-link {
    font-size: 14px;
    font-weight: 400;
    line-height: 16.8px;
    margin-top: 8px;
}

.license-bar-all .spellbook-license-link .spellbook-error-message {
	color:rgb(226, 97, 97);
}

.license-bar-all .spellbook-license-link span {
    color: #D4ECF7;
}

.license-bar-all .spellbook-license-link a {
    color: #d3abf9;
    text-decoration: underline;
    cursor: pointer;
}

.license-bar-all .spellbook-vertical-divider {
    width: 1px;
    background: rgba(255, 255, 255, 0.16);
    margin: 0;
}

.license-bar-all .spellbook-stars-background {
    position: absolute;
    right: -44px;
    top: 0;
    width: 171.65px;
    height: 133.20px;
    opacity: 0.16;
    pointer-events: none;
    z-index: 1;
}

.license-bar-all .spellbook-star-large {
    position: absolute;
    left: 38.45px;
    top: 0;
    width: 88px;
    height: 134px;
}

.license-bar-all .spellbook-star-small {
    position: absolute;
    left: 0;
    top: 5.55px;
    width: 31px;
    height: 31px;
}

/* Reset any WordPress default styles */
.license-bar-all h2 {
    margin: 0;
    padding: 0;
}

.license-bar-all input,
.license-bar-all button {
    min-height: 0;
    box-shadow: none;
}

/* Ensure consistent heights */
.license-bar-all .spellbook-form-input,
.license-bar-all .spellbook-form-button {
    height: 36px;
    box-sizing: border-box;
}

/* Badge styles */
.gform-tabs__tab-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 18px;
    height: 18px;
    padding: 0 4px;
    margin-left: 1px;
    border-radius: 9px;
    font-size: 11px;
    line-height: 1;
    color: white;
}

.gform-tabs__tab-badge--unregistered {
    background: #B73C22;
}

.gform-tabs__tab-badge--update {
    background: #B77B1B;
}

.spellbook-app__filter-bar {
    margin-bottom: 1rem;
}

.spellbook-app__filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.spellbook-app__filter-buttons button {
    margin: 0;
}

.spellbook-app__search-bar .gform-meta-box__content {
	padding: 0;
}

.spellbook-app__search-filter-container {
    display: flex;
    gap: 1rem;
    align-items: stretch;
}

.spellbook-app__search-input {
    flex: 1;
	margin: 1rem;
	max-width: 330px;
}

/* Override GF tabs styles for our filter tabs */
.spellbook-app__search-filter-container .gform-tabs {
    margin: 0 0 0 auto;
}

.spellbook-app__search-filter-container .gform-tabs__tablist {
    border-bottom: none;
    padding: 0;
	height: 100%;
	margin: 0 1rem 0 auto;
}

.spellbook-app__search-filter-container .gform-tabs__tab {
	margin-block-end: 0;
	padding-block: 0;
}

@container spellbook (max-width: 1100px) {
    .spellbook-app__search-filter-container .gform-tabs__tab[data-test-id="filter-active"],
    .spellbook-app__search-filter-container .gform-tabs__tab[data-test-id="filter-inactive"],
    .spellbook-app__search-filter-container .gform-tabs__tab[data-test-id="filter-not-installed"] {
        display: none;
    }
}

@container spellbook (max-width: 750px) {
    .spellbook-app__search-filter-container .gform-tabs {
        display: none;
    }

    .spellbook-app__search-input {
        max-width: none;
    }
}

.license-bar-suite {
    padding: 1rem;
    position: relative;
    background: white;
    box-shadow: 0 2px 2px rgba(58, 58, 87, 0.0596411);
    border-radius: 3px;
    outline: 1px solid #d4d4e4;
    overflow: hidden;
    font-family: inter, -apple-system, blinkmacsystemfont, "Segoe UI", roboto, oxygen-sans, ubuntu, cantarell, "Helvetica Neue", sans-serif;
    margin-bottom: 20px;
}

/* Unactivated state */
.license-bar-suite .spellbook-license {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
	flex-wrap: wrap;
    gap: 24px;
}

.license-bar-suite .spellbook-form-container {
    flex: 3;
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    min-height: 30px;
}

@container spellbook (min-width: 1100px) {
    .license-bar-suite .spellbook-form-container {
        flex-wrap: nowrap;
    }

	.license-bar-suite .spellbook-license {
		flex-wrap: nowrap;
	}
}

.spellbook-error-message {
	color: #d32f2f;
}

.license-bar-suite .spellbook-form-input {
    flex: 1;
	max-width: 330px;
    padding: 6px 12px;
    border-radius: 3px;
    border: 1px solid #9092b0;
    background: transparent;
    color: #757575;
    font-size: 14px;
    font-weight: 400;
    line-height: 14px;
    min-height: 0;
    box-shadow: none;
    height: 34px;
    box-sizing: border-box;
}

.license-bar-suite .spellbook-form-input::placeholder {
    color: #757575;
}

.license-bar-suite .spellbook-form-input:focus {
    border: 1px solid #bc82f5 !important;
    box-shadow: 0 0 0 1px #bc82f5 !important;
    outline: none !important;
}

.license-bar-suite .spellbook-form-button {
    padding: 8px 16px;
    background: #902EEF;
    border-radius: 3px;
    color: white;
    border: none;
    font-size: 14px;
    line-height: 14px;
    cursor: pointer;
    white-space: nowrap;
    min-height: 0;
    box-shadow: none;
    height: 34px;
    box-sizing: border-box;
}

.license-bar-suite .spellbook-form-button:hover {
	background: #561c8f;
}

.license-bar-suite .spellbook-form-button:disabled {
	background: #bc82f5;
}

.license-bar-suite .spellbook-link-button {
	color: #902EEF;
	background: transparent;
	border: none;
	font-size: 14px;
	font-weight: 400;
	line-height: 14px;
	cursor: pointer;
	text-decoration: underline;
	padding: 0;
}

.license-bar-suite a:hover,
.license-bar-suite .spellbook-link-button:hover {
	text-decoration: none;
}

.license-bar-suite .spellbook-links {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 6px;
    height: 30px;
}

.license-bar-suite a {
    color: #902EEF;
    font-weight: 400;
    text-decoration: underline;
}

.license-bar-suite .spellbook-vertical-divider {
    width: 1px;
    height: 16px;
    background: #E0E0E0;
    margin: 0;
}

/* Stars background (only for unactivated state) */
.license-bar-suite .spellbook-stars-background {
    position: absolute;
    right: -44px;
    top: 0;
    width: 171.65px;
    height: 133.20px;
    opacity: 0.10;
    pointer-events: none;
    z-index: 1;
}

.license-bar-suite .spellbook-star-large {
    position: absolute;
    left: 38.45px;
    top: 0;
}

.license-bar-suite .spellbook-star-large svg {
    width: 88px;
    height: 78px;
}

.license-bar-suite .spellbook-star-small {
    position: absolute;
    left: 0;
    top: 5.55px;
}

.license-bar-suite .spellbook-star-small svg {
    width: 31px;
    height: 31px;
}

/* Activated state */
.license-bar-suite--activated {
    display: flex;
    align-items: center;
    gap: 0 16px;
	padding: 0 12px 0 0;
	font-size: 14px;
}

.license-bar-suite--activated .suite-icon {
	padding: 8px 10px 8px 6px;
	width: 40px;
	height: 40px;
	border-radius: 0 30px 30px 0;
}

.license-bar-suite--activated .suite-icon--perk {
	background: #F0F9F4;
}

.license-bar-suite--activated .suite-icon--perk path[opacity="0.08"] {
	display: none;
}

.license-bar-suite--activated .suite-icon--connect {
	background: #FFEFED;
}

.license-bar-suite--activated .suite-icon--connect path[opacity="0.1"] {
	display: none;
}

.license-bar-suite--activated .suite-icon--shop {
	background: #F1F8FB;
}

.license-bar-suite--activated .suite-icon--shop svg {
	margin-top: -3px;
}

.license-bar-suite--activated .suite-icon--shop path[opacity="0.08"] {
	display: none;
}

.license-bar-suite--activated .spellbook-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.license-bar-suite--activated .spellbook-title {
    font-weight: 500;
    color: #1d2327;
    display: flex;
    align-items: center;
    gap: 8px;
}

.license-bar-suite--activated .spellbook-usage {
    color: #757575;
    display: flex;
    align-items: center;
    gap: 8px;
}

.license-bar-suite--activated .spellbook-usage a {
    color: #902EEF;
    text-decoration: underline;
}

/* Right side */
.license-bar-suite__right {
	display: flex;
	align-items: center;
	gap: 1rem;
	margin-left: auto;
	margin-right: .5rem;
}

@container spellbook (max-width: 800px) {
    .license-bar-suite--activated .spellbook-usage,
    .license-bar-suite__right .spellbook-upgrade,
    .license-bar-suite__right .spellbook-renew {
        display: none;
    }

    .license-bar-suite__right {
        gap: 0;
    }
}

/* Reset any WordPress default styles */
.license-bar-suite input,
.license-bar-suite button {
    min-height: 0;
    box-shadow: none;
}

.license-status--expired {
	display: flex;
	width: fit-content;
	align-items: center;
	gap: 0.25rem;

	background-color: rgb(251, 216, 208) !important;
    color: #dd301d !important;
    border: 1px solid rgb(241, 183, 170);
	box-shadow: 0 1px 4px rgba(18, 25, 97, 0.0779552);
	padding-inline: 7px;
	padding-block: 3px;
	border-radius: 3px;
}

.license-status--expired svg {
	flex-shrink: 0;
	fill: #d32f2f;
}

.license-box__content {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 1.5rem;
}

.license-box--empty .license-box__content {
	gap: 0 1.5rem;
}

.license-box--empty .gform-meta-box__content {
    align-items: center;
}

.license-box__icon {
    grid-row: span 2;
}

.license-box__details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, auto));
    gap: 1rem;
    align-items: baseline;
    justify-content: start;
}

.license-box__stat {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.license-box__key-value {
    font-family: monospace !important;
}

.license-box__key-dots-long {
    display: inline;
}

.license-box__key-dots-medium,
.license-box__key-dots-short {
    display: none;
}

@container spellbook (max-width: 1150px) {
    .license-box__key-dots-long {
        display: none;
    }
    .license-box__key-dots-medium {
        display: inline;
    }
}

@container spellbook (max-width: 1000px) {
    .license-box__key-dots-medium {
        display: none;
    }
    .license-box__key-dots-short {
        display: inline;
    }
}

.license-box__actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    grid-column: 2 / -1;
}

.spellbook-app__content .license-box__actions > button {
	height: 2.375rem !important;
}

/* Unactivated state */
.license-box__empty {
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    align-items: start;
    gap: 1.5rem;
}

.license-box__info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
}

.license-box__separator {
	width: 1px;
	height: 100%;
	background-color: #e4e4ee;
    display: none;
}

@container spellbook (min-width: 900px) {
    .license-box__empty {
        grid-template-columns: minmax(0, 1fr) auto minmax(0, 1fr);
    }

    .license-box__separator {
        display: block;
    }
}

.license-box__form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
}

.license-box__form:has(.license-box__error-message) {
    gap: 0;
}

.license-box__form-inputs {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: flex-start;
}

.license-box__input-wrapper {
    flex: 1;
    min-width: 250px;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.license-box__error-message {
    margin: 0.5rem 0;
    color: #d63638 !important; /* WordPress error red */
}

@container spellbook (min-width: 600px) {
    .license-box__form-inputs {
        flex-wrap: wrap;
    }

    .license-box__form-inputs > div:first-child {
        width: auto;
    }
}

@container spellbook (max-width: 600px) {
    .license-box__content {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .license-box__icon {
        grid-row: auto;
        margin-bottom: 1rem;
    }

    .license-box__actions {
        grid-column: 1;
    }
}

.licenses-page {
    margin: 0;
}

.licenses-page__grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}


/*# sourceMappingURL=dashboard.css.map*/