<?php
/**
 * Set constants for Google Maps JS API key--used for ACF's backend map--and Google Maps
 * Embed API Key, used for generating maps on the site front end.
 *
 * @link https://developers.google.com/maps/documentation/javascript/get-api-key
 * @link https://developers.google.com/maps/documentation/embed/get-api-key
 */

/**
 * Get Google Maps JS API Key from ACF field
 */
function get_google_maps_js_api_key() {
    $api_key = get_field('google_map_js_api_key', 'option');
    return !empty($api_key) ? $api_key : 'MAPS-JS-API-KEY';
}

/**
 * Get Google Maps Embed API Key from ACF field
 */
function get_google_maps_embed_api_key() {
    $api_key = get_field('google_map_embed_api_key', 'option');
    return !empty($api_key) ? $api_key : 'MAPS-EMBED-API-KEY';
}

// Define constants using ACF values
define('GOOGLE_MAPS_JS_API_KEY', get_google_maps_js_api_key());
define('GOOGLE_MAPS_EMBED_API_KEY', get_google_maps_embed_api_key());

/**
 * Hook Maps JS API into ACF intialization.
 */
function add_google_map_to_acf_init() {
    acf_update_setting( 'google_api_key', GOOGLE_MAPS_JS_API_KEY );
}

add_action( 'acf/init', 'add_google_map_to_acf_init' );

/**
 * Pass in ACF Google Map field to generate HTML for
 * Google maps embed on the front end of the site.
 *
 * @param array  $acf_map_field  The array generated by ACF Google Maps field.
 *
 * @link https://developers.google.com/maps/documentation/embed/guide
 */

function acf_make_map( $acf_map_field ){
    $address_field = $acf_map_field['address'];
    $encoded_address = urlencode( $address_field );
    echo '
        <iframe
            width="600"
            height="450"
            frameborder="0" style="border:0"
            src="https://www.google.com/maps/embed/v1/place?key=' . GOOGLE_MAPS_EMBED_API_KEY . '&q=' . $encoded_address . '" allowfullscreen>
        </iframe>';
}