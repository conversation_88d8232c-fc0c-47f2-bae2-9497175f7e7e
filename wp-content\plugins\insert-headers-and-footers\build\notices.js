(()=>{"use strict";(window.WPCodeAdminWideNotices||function(i,e,c){var o={init:function(){c(o.ready)},ready:function(){o.events()},events:function(){c(i).on("click",".wpcode-notice .notice-dismiss, .wpcode-notice .wpcode-notice-dismiss",o.dismissNotice)},dismissNotice:function(i){i.target.classList.contains("wpcode-notice-dismiss")&&c(this).closest(".wpcode-notice").slideUp(),c.post(wpcode_admin_notices.ajax_url,{action:"wpcode_notice_dismiss",_wpnonce:wpcode_admin_notices.nonce,id:(c(this).closest(".wpcode-notice").attr("id")||"").replace("wpcode-notice-","")})}};return o}(document,window,jQuery)).init()})();