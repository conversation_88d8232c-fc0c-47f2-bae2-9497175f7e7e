(()=>{"use strict";(window.WPCodeConnect||function(n,o,t){jconfirm.defaults={closeIcon:!1,backgroundDismiss:!1,escapeKey:!0,animationBounce:1,useBootstrap:!1,theme:"modern",boxWidth:"560px",type:"blue",animateFromElement:!1,scrollToPreviousElement:!1};var e={$connectBtn:t("#wpcode-settings-connect-btn"),$connectKey:t("#wpcode-settings-upgrade-license-key")},c="<div class='excl-mark'>!</div>",i={init:function(){console.log("WPCodeConnect: init"),t(i.ready)},ready:function(){i.events()},events:function(){i.connectBtnClick()},connectBtnClick:function(){e.$connectBtn.on("click",function(){i.gotoUpgradeUrl()})},proAlreadyInstalled:function(n){const e=WPCodeSVG.WPCodeIcon("checkmark",88,88,"0 0 130.2 130.2","iconId","path circle");t.confirm({title:e+wpcode.almost_done,content:n.data.message,type:"blue",buttons:{confirm:{text:wpcode.plugin_activate_btn,btnClass:"wpcode-btn-confirm",action:function(){o.location.reload()}}}})},gotoUpgradeUrl:function(){var n={action:"wpcode_connect_url",key:e.$connectKey.val(),_wpnonce:wpcode.nonce};t.post(ajaxurl,n).done(function(n){if(n.success)return n.data.reload?void i.proAlreadyInstalled(n):void(o.location.href=n.data.url);t.confirm({title:c+wpcode.oops,closeIcon:!1,content:n.data.message,type:"blue",buttons:{ok:{text:wpcode.ok,btnClass:"wpcode-btn-confirm",action:function(){}}}})}).fail(function(n){i.failAlert(n)})},failAlert:function(n){t.confirm({title:c+wpcode.oops,content:wpcode.server_error+"<br>"+n.status+" "+n.statusText+" "+n.responseText,type:"blue",buttons:{ok:{text:wpcode.ok,btnClass:"wpcode-btn-confirm",action:function(){}}}})}};return i}(document,window,jQuery)).init()})();