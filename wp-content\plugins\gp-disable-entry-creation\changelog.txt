
# Changelog

## 2.0.3

	- Fixed an issue with the conditional rules setting not showing in the Form Settings after saving.
	- Updated `require` call to use `plugin_dir_path()`.

## 2.0.2

	- Fixed improperly named filter: `gpbua_should_delete_entry` has been changed to `gpdec_should_delete_entry`.

## 2.0.1

	- Added `gpbua_should_delete_entry` PHP filter.
	- Added "Update URI" to plugin header to improve security.

## 2.0

	- Improved support for Gravity Forms 2.5.
	- Updated to use GP_Plugin framework.

## 1.0.9

	- Fixed conflict with Nested Forms where attempting to delete the same entry twice created fatal error.

## 1.0.8

	- Added support for conditional logic to determine when to disable entry creation.

## 1.0.7

	- Converted GP_Disable_Entry_Creation class to a singleton to allow 3rd party developers to more easily interact with hooks created for it.
	- Added GP_DISABLE_ENTRY_CREATION constant for version.

## 1.0.6

	- Fixed fatal error when GF User Registration add-on was not active (double doh!)

## 1.0.5

	- Fixed issue where entry was not correctly deleted if UR feed was attached

## 1.0.4

    - Updated perk to use GP_Bootstrap

## 1.0.3

	- Updated to work with GFUR 3.0

## 1.0.2

    - Fixed issue where entry was not disabled when GFUR was not active (doh!)

## 1.0.1

    - Updated priority of some hooks to avoid order-of-events conflicts, props @richardW8k
    - Fixed issue where $config 'active' index may not be defined

## 1.0

    - Oh, oh, oh, oh, staying alive!