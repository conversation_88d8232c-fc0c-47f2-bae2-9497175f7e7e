jQuery.fn.repeater=function(e){this.options=jQuery.extend(!0,{},{template:"",limit:5,items:[{}],saveEvents:"blur change",saveElements:"input, select",addImageSrc:"",removeImageSrc:"",callbacks:{save:function(){},beforeAdd:function(){},add:function(){},beforeAddNew:function(){},addNew:function(){},beforeRemove:function(){},remove:function(){},repeaterButtons:function(){return!1}}},e),this.elem=jQuery(this),this.items=this.options.items,this.callbacks=this.options.callbacks,this._template=this.options.template?this.options.template:this.elem.next("#"+this.elem.prop("id")+"-template").html();var t=this.options.saveEvents.split(" "),s=[];for(i in t)s.push(t[i]+".repeater");return this.options.saveEvents=s.join(" "),this.init=function(){for(i in this._template||(this._template=this.elem.html()),this.elem.after('<div id="'+this.elem.prop("id")+'-template" style="display:none">'+this._template+"</div>"),this.elem.addClass("repeater"),this.elem.empty(),this.items)this.addItem(this.items[i],i);var e=this;return jQuery(this.elem).off("click.repeater",'a.add-item:not(".inactive")'),jQuery(this.elem).on("click.repeater",'a.add-item:not(".inactive")',function(t){e.addNewItem(this)}),jQuery(this.elem).off("click.repeater","a.remove-item"),jQuery(this.elem).on("click.repeater","a.remove-item",function(t){e.removeItem(this)}),jQuery(this.elem).off(this.options.saveEvents,this.options.saveElements),jQuery(this.elem).on(this.options.saveEvents,this.options.saveElements,function(){e.save()}),this},this.addItem=function(e,t){var s=this._template;for(var a in e){s=s.replace(/{i}/g,t);var r=this.callbacks.repeaterButtons(this,t)?this.callbacks.repeaterButtons(this,t):this.addRepeaterButtons(t);s=s.replace("{buttons}",r);var n=RegExp("{"+a+"}","g");s=s.replace(n,e[a])}var h=jQuery(s).addClass("item-"+t);this.callbacks.beforeAdd(this,h,e),this.append(h),this.callbacks.add(this,h,e)},this.addRepeaterButtons=function(t){var s=this.items.length>=e.limit&&0!==e.limit?"inactive":"",a='<div class="repeater-buttons">';return a+='<a class="add-item '+s+'" data-index="'+t+'">'+('<img src="'+e.addImageSrc)+'/images/add.png" alt="Add" /></a>',this.items.length>1&&(a+='<a class="remove-item" data-index="'+t+'"><img src="'+e.removeImageSrc+'/images/remove.png" alt="Remove" /></a>'),a},this.addNewItem=function(e){var t=jQuery(e).attr("data-index");this.callbacks.beforeAddNew(this,t),this.items.splice(t+1,0,this.getBaseObject()),this.callbacks.addNew(this,t),this.refresh()},this.removeItem=function(e){var t=jQuery(e).attr("data-index");this.callbacks.beforeRemove(this,t),delete this.items[t],this.callbacks.remove(this,t),this.save(),this.refresh()},this.refresh=function(){for(i in this.elem.empty(),this.items)this.addItem(this.items[i],i)},this.save=function(){var e=this.getDataKeys(),t=[];for(i=0;i<this.items.length;i++)if(void 0!==this.items[i]){var s={};for(j in e){var a=e[j],r="#"+a+"_"+i;s[a]=jQuery(this.elem).find(r).val()}t.push(s)}this.items=t,this.callbacks.save(this,t)},this.getDataKeys=function(){var e=[];for(var t in this.items)if(void 0!==this.items[t]){for(var s in this.items[t])e[e.length]=s;break}return e},this.getBaseObject=function(){var e={},t=this.getDataKeys();for(var s in t)e[t[s]]="";return e},this.init(!0)};