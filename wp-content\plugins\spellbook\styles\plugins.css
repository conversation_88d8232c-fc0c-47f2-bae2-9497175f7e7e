div.gp-requirements-notice ul {
	padding: 0 0 0 20px;
}

div.gp-requirements-notice ul li {
	list-style: disc;
}

#message.gwp-message.error {
	display: none;
}

.wrap #message.gwp-message.error {
	display: block;
}

.wrap #message.gwp-message.error+#message.gwp-message.error {
	border-top-style: dotted;
	margin-top: -16px;
	border-top: 1px dotted rgba( 0, 0, 0, 0.25);
}

.gwp-plugin-notice {}

.gwp-plugin-notice td {
	padding: 0 !important;
	box-shadow: none !important;
}

.gwp-plugin-notice ul {
	margin: 5px 0;
	position: relative;
	padding: 0 0 0 26px;
}

.gwp-plugin-notice .update-message ul:before {
	font-family: "dashicons";
	content: '\f534';
	position: absolute;
	font-size: 18px;
	color: #dc3232;
	left: 0;
}

.gwp-plugin-notice li:last-child {
	margin: 0;
}
