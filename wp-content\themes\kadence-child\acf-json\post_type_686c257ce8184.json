{"key": "post_type_686c257ce8184", "title": "Services", "menu_order": 0, "active": true, "post_type": "service", "advanced_configuration": true, "import_source": "", "import_date": "", "labels": {"name": "Services", "singular_name": "Service", "menu_name": "Services", "all_items": "All Services", "edit_item": "Edit Service", "view_item": "View Service", "view_items": "View Services", "add_new_item": "Add New Service", "add_new": "Add New Service", "new_item": "New Service", "parent_item_colon": "Parent Service:", "search_items": "Search Services", "not_found": "No services found", "not_found_in_trash": "No services found in Trash", "archives": "Service Archives", "attributes": "Service Attributes", "featured_image": "", "set_featured_image": "", "remove_featured_image": "", "use_featured_image": "", "insert_into_item": "Insert into service", "uploaded_to_this_item": "Uploaded to this service", "filter_items_list": "Filter services list", "filter_by_date": "Filter services by date", "items_list_navigation": "Services list navigation", "items_list": "Services list", "item_published": "Service published.", "item_published_privately": "Service published privately.", "item_reverted_to_draft": "Service reverted to draft.", "item_scheduled": "Service scheduled.", "item_updated": "Service updated.", "item_link": "Service Link", "item_link_description": "A link to a service."}, "description": "", "public": true, "hierarchical": true, "exclude_from_search": true, "publicly_queryable": true, "show_ui": true, "show_in_menu": true, "admin_menu_parent": "", "show_in_admin_bar": false, "show_in_nav_menus": false, "show_in_rest": false, "rest_base": "", "rest_namespace": "wp/v2", "rest_controller_class": "WP_REST_Posts_Controller", "menu_position": "", "menu_icon": {"type": "dashicons", "value": "dashicons-admin-post"}, "rename_capabilities": false, "singular_capability_name": "post", "plural_capability_name": "posts", "supports": ["title", "editor", "excerpt", "thumbnail", "custom-fields"], "taxonomies": "", "has_archive": false, "has_archive_slug": "", "rewrite": {"permalink_rewrite": "post_type_key", "with_front": "1", "feeds": "0", "pages": "1"}, "query_var": "post_type_key", "query_var_name": "", "can_export": true, "delete_with_user": false, "register_meta_box_cb": "", "enter_title_here": "", "modified": 1752140022}