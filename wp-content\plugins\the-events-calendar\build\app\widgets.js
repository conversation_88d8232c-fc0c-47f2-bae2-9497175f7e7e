(()=>{"use strict";var e,t={2818:(e,t,n)=>{n.r(t),n.d(t,{default:()=>q});const r=window.React,o=window.ReactJSXRuntime,{InnerBlocks:i}=wp.blockEditor,s=[["core/legacy-widget",{idBase:"tribe-widget-events-list",instance:{}}]];var a,l,d,c,p,v,w;function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},u.apply(null,arguments)}const h=e=>r.createElement("svg",u({width:25,height:25,viewBox:"0 0 25 25",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),a||(a=r.createElement("rect",{x:.629639,y:.46405,width:24,height:24,rx:2,fill:"#499FD1"})),l||(l=r.createElement("line",{x1:9.90588,y1:7.19293,x2:19.5663,y2:7.19293,stroke:"white",strokeWidth:2,strokeLinecap:"round"})),d||(d=r.createElement("line",{x1:5.23474,y1:7.19293,x2:5.86738,y2:7.19293,stroke:"white",strokeWidth:2,strokeLinecap:"round"})),c||(c=r.createElement("line",{x1:9.90588,y1:12.2855,x2:19.5663,y2:12.2855,stroke:"white",strokeWidth:2,strokeLinecap:"round"})),p||(p=r.createElement("line",{x1:5.23474,y1:12.2855,x2:5.86738,y2:12.2855,stroke:"white",strokeWidth:2,strokeLinecap:"round"})),v||(v=r.createElement("line",{x1:9.90588,y1:17.3781,x2:19.5663,y2:17.3781,stroke:"white",strokeWidth:2,strokeLinecap:"round"})),w||(w=r.createElement("line",{x1:5.23474,y1:17.3781,x2:5.86738,y2:17.3781,stroke:"white",strokeWidth:2,strokeLinecap:"round"})));var f,g,y,b;function m(){return m=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},m.apply(null,arguments)}const k=e=>r.createElement("svg",m({width:24,height:24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),f||(f=r.createElement("path",{d:"M3 3H9V9H3V3ZM5 5V7H7V5H5Z",fill:"currentColor"})),g||(g=r.createElement("path",{d:"M15 3H21V9H15V3ZM17 5V7H19V5H17Z",fill:"currentColor"})),y||(y=r.createElement("path",{d:"M3 15H9V21H3V15ZM5 17V19H7V17H5Z",fill:"currentColor"})),b||(b=r.createElement("path",{d:"M15 15H21V21H15V15ZM17 17V19H19V17H17Z",fill:"currentColor"}))),{__}=wp.i18n,{InnerBlocks:x}=wp.blockEditor,E={id:"events-list",title:__("Events List","the-events-calendar"),description:__("Shows a list of upcoming events.","the-events-calendar"),icon:(0,o.jsx)(h,{}),category:"tribe-events",keywords:["event","events list","list","events-gutenberg","tribe"],example:{},edit:()=>(0,o.jsx)(i,{template:s,templateLock:"all"}),save:()=>(0,o.jsx)(x.Content,{})},{InnerBlocks:O}=wp.blockEditor,V=[["core/legacy-widget",{idBase:"tribe-widget-events-qr-code",instance:{}}]],j=()=>(0,o.jsx)(O,{template:V,templateLock:"all"}),{__:H}=wp.i18n,{InnerBlocks:L}=wp.blockEditor,S={id:"qr-code",title:H("Events QR Code","the-events-calendar"),description:H("Display a QR code for an event.","the-events-calendar"),icon:(0,o.jsx)(k,{}),category:"tribe-events",keywords:["event","qr code","events-gutenberg","tribe"],example:{},edit:e=>((0,r.useEffect)((()=>{const e=()=>{document.querySelectorAll(".tribe-widget-form-control--dropdown[data-depends]").forEach((e=>{const t=e.dataset.depends,n=e.dataset.condition,r=document.querySelector(t);if(r){const t=()=>{r.value===n?e.classList.remove("hidden"):e.classList.add("hidden")};t(),r.addEventListener("change",t),e._updateVisibility=t}}))},t=new MutationObserver((t=>{t.forEach((t=>{t.addedNodes.length&&Array.from(t.addedNodes).some((e=>!!e.querySelector&&e.querySelector(".tribe-widget-form-control--dropdown[data-depends]")))&&e()}))}));return t.observe(document.body,{childList:!0,subtree:!0}),e(),()=>{t.disconnect(),document.querySelectorAll(".tribe-widget-form-control--dropdown[data-depends]").forEach((e=>{const t=document.querySelector(e.dataset.depends);t&&e._updateVisibility&&t.removeEventListener("change",e._updateVisibility)}))}}),[]),(0,o.jsx)(j,{...e})),save:()=>(0,o.jsx)(L.Content,{})},{registerBlockType:_}=wp.blocks,M=[E,S];M.forEach((e=>{const t=`tribe/${e.id}`;_(t,e)}));const q=M}},n={};function r(e){var o=n[e];if(void 0!==o)return o.exports;var i=n[e]={exports:{}};return t[e](i,i.exports,r),i.exports}r.m=t,e=[],r.O=(t,n,o,i)=>{if(!n){var s=1/0;for(c=0;c<e.length;c++){for(var[n,o,i]=e[c],a=!0,l=0;l<n.length;l++)(!1&i||s>=i)&&Object.keys(r.O).every((e=>r.O[e](n[l])))?n.splice(l--,1):(a=!1,i<s&&(s=i));if(a){e.splice(c--,1);var d=o();void 0!==d&&(t=d)}}return t}i=i||0;for(var c=e.length;c>0&&e[c-1][2]>i;c--)e[c]=e[c-1];e[c]=[n,o,i]},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e={1602:0,6358:0};r.O.j=t=>0===e[t];var t=(t,n)=>{var o,i,[s,a,l]=n,d=0;if(s.some((t=>0!==e[t]))){for(o in a)r.o(a,o)&&(r.m[o]=a[o]);if(l)var c=l(r)}for(t&&t(n);d<s.length;d++)i=s[d],r.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return r.O(c)},n=globalThis.webpackChunkthe_events_calendar=globalThis.webpackChunkthe_events_calendar||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var o=r.O(void 0,[6358],(()=>r(2818)));o=r.O(o),window.tec=window.tec||{},window.tec.events=window.tec.events||{},window.tec.events.app=window.tec.events.app||{},window.tec.events.app.widgets=o})();