.error-boundary {
    padding: 2rem;
    text-align: center;
    max-width: 800px;
    margin: 2rem auto;
    background-color: #fff;
    border-radius: 3px;
    box-shadow: 0 1px 4px rgba(18, 25, 97, 0.0779552);
    border: 1px solid #E4E4EE;
}

.error-boundary__title {
    margin-bottom: 1rem;
    color: #23282D;
}

.error-boundary__message {
    margin-bottom: 1rem;
    color: #50575E;
}

.error-boundary__details {
    margin: 1.5rem 0;
    padding: 1rem;
    background-color: #F8F9FA;
    border-radius: 3px;
    text-align: left;
    font-family: monospace;
    font-size: 0.9em;
    color: #50575E;
    border: 1px solid #E4E4EE;
}

.error-boundary__error-name {
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.error-boundary__error-message {
    margin-bottom: 0.5rem;
}

.error-boundary__stack-trace {
    margin: 0;
    white-space: pre-wrap;
    word-break: break-word;
    font-size: 0.8em;
    opacity: 0.8;
}

.error-boundary__support {
    margin-bottom: 2rem;
    color: #50575E;
}

.error-boundary__support-link {
    color: #2271B1;
    text-decoration: none;
}

.error-boundary__support-link:hover {
    text-decoration: underline;
}

.error-boundary__actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}
