if(void 0===gperk)var gperk={};gperk.confirmActionUrl=function(e,t,n){e.preventDefault();var r=jQuery(e.target);n||(n=r.prop("href")),confirm(t)&&(location.href=n)},gperk.addTab=function(e,t,n){var r=t.replace("#",""),o=r.replace("gws","gwp"),r=r!=o?r+" "+o:r;e.tabs("destroy"),e.find("ul").eq(0).append('<li style="width:100px; padding:0px;" class="'+r+'">         <a href="'+t+'">'+n+"</a>         </li>"),e.append(jQuery(t)),e.tabs({beforeActivate:function(e,t){switch(jQuery(t.newPanel).prop("id")){case"gws_form_tab":jQuery(document).trigger("gwsFormTabSelected",[form]);break;case"gws_field_tab":jQuery(document).trigger("gwsFieldTabSelected",[field])}}})},gperk.togglePerksTab=function(){var e=jQuery(".ui-tabs-nav li.gwp_field_tab");e.hide(),gperk.fieldHasSettings()&&e.show()},gperk.fieldHasSettings=function(){var e=!1;return jQuery("#gws_field_tab").find("li.field_setting").each(function(){/(display[: ]+none)/.test(jQuery(this).attr("style"))||(e=!0)}),e},gperk.toggleSection=function(e,t){var e=jQuery(e);e.prop("checked")?(e.parents(".gwp-field").addClass("open"),jQuery(t).gwpSlide("down",".perk-settings")):(e.parents(".gwp-field").removeClass("open"),jQuery(t).gwpSlide("up",".perk-settings"))},gperk.isSingleProduct=function(e){return singleFieldTypes=gperk.applyFilter("gwSingleFieldTypes",["singleproduct","hiddenproduct","calculation"]),-1!=jQuery.inArray(e.inputType,singleFieldTypes)},gperk.getFieldLabel=function(e,t){gperk.isUndefined(t)&&(t=!1);var n=e.label,r=gperk.getInput(e,t);return"checkbox"==e.type&&!1!=r||!1!=r?r.label:n},gperk.getInput=function(e,t){if(gperk.isUndefined(e.inputs)&&jQuery.isArray(e.inputs))for(i in e.inputs){var n=e.inputs[i];if(n.id==t)return n}return!1},gperk.toggleSettings=function(e,t,n,r){var o=jQuery("#"+e),p=jQuery("#"+t),r=void 0===r||r;gperk.is(n,"undefined")?n=o.is(":checked"):o.prop("checked",n),n?r?p.gfSlide("down"):p.show():r?p.gfSlide("up"):p.hide(),SetFieldProperty(e,n)},gperk.setInputProperty=function(e,t,n){var r=GetSelectedField();for(i in r.inputs)r.inputs[i].id==e&&(r.inputs[i][t]=n)},gperk.setFormProperty=function(e,t){form[e]=t},jQuery.fn.gwpSlide=function(e,t){var n=!1===t||!0===t?t:jQuery(t).is(":visible");return"up"==e?n?this.slideUp():this.hide():n?this.slideDown():this.show(),this},jQuery(document).ready(function(e){e(".panel-block-tabs").on("accordionactivate",function(e,t){t.newPanel.is("#gravity-perks_tab")&&gform.doAction("gperks_field_settings_tab_loaded",e,GetSelectedField(),window.form,t)}),jQuery(document).bind("gform_load_field_settings",function(e,t){gperk.togglePerksTab()});var t=function(t){var n=e(t).data("gp-tooltip-options");!n&&e(t).hasClass("gp-tooltip-right")&&(n={classes:{"ui-tooltip":"gp-tooltip-right"},position:{my:"right bottom",at:"right+10 top-11",collision:"none"},open:null}),n&&e(t).tooltip("option",n)};e(document).on("gperks_tooltips_initialized",function(){e(".gp-tooltip").each(function(){t(this)})}),setTimeout(function(){e(document).trigger("gperks_tooltips_initialized")})});