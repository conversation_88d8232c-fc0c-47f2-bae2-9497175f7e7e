function SetEntryCreationConditionalLogic(o){form.entryCreation=o?{conditionalLogic:new ConditionalLogic}:null}jQuery(document).ready(function(o){gform.addFilter("gform_conditional_logic_description",function(o,n,i,t){return"entry_creation"!==i?o:(delete n.actionType,n.objectDescription="Disable entry creation if",makeArray(n).join(" "))}),gform.addFilter("gform_conditional_object",function(o,n){return"entry_creation"!==n?o:(void 0===form.entryCreation&&(form.entryCreation={},form.entryCreation.conditionalLogic=new ConditionalLogic),form.entryCreation)}),void 0!==form.entryCreation&&null!==form.entryCreation.conditionalLogic&&void 0!==form.entryCreation.conditionalLogic.logicType&&o("#entry_creation_conditional_logic").prop("disabled",!1).prop("checked",!0),ToggleConditionalLogic(!0,"entry_creation")});
//# sourceMappingURL=gp-disable-entry-creation-admin.js.map