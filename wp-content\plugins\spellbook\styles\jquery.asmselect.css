.gp-field-setting .asmContainer {
	/* container that surrounds entire asmSelect widget */
}

.gp-field-setting .asmSelect {
	/* the newly created regular 'select' */
	display: inline;
}

.gp-field-setting .asmOptionDisabled {
	/* disabled options in new select */
	color: #999;
}

.gp-field-setting .asmHighlight {
	/* the highlight span */
	padding: 0;
	margin: 0 0 0 1em;
	display: none !important;
}

.gp-field-setting .asmList {
	/* html list that contains selected items */
	position: relative;
	display: block;
	padding-left: 0;
	list-style: none;
	overflow: hidden !important;
	margin: 0.5rem 0 0;
	border: 1px solid #d5d7e9;
	border-radius: 3px;
}

.gf-legacy-ui .gp-field-setting .asmList {
	border: 1px solid #ddd;
	background-color: transparent;
	border-radius: 0;
	border-radius: 4px;
}

.gp-field-setting .asmListItem {
	/* li item from the html list above */
	position: relative;
	list-style: none;
	background: #f4f5fb;
	border-bottom: 0;
	border-radius: 0;
	padding: 0 0.8125rem !important;
	border-top: 1px dashed #d5d8e9;
	margin: 0;
	height: 2.25rem;
	line-height: 2.25rem;
}

.gf-legacy-ui .gp-field-setting .asmListItem {
	border-top: 1px dashed #ddd !important;
	border-radius: 0;
	margin: -1px 0 0;
	background: #fff;
}

.gp-field-setting .asmListItem:hover {
	background-color: #ecedf8;
}

.gf-legacy-ui .gp-field-setting .asmListItem:hover {
	background-color: #f7f7f7;
}

.gp-field-setting .asmListItemLabel {
	/* this is a span that surrounds the text in the item, except for the remove link */
}

.gp-field-setting .asmListSortable .asmListItemLabel {
	cursor: move;
}

.gp-field-setting .asmList:empty {
	display: none !important;
}

.gp-field-setting .asmListItem:first-child {
	border-top: 0;
}

.gf-legacy-ui .gp-field-setting .asmListItem:first-child {
	border-top: 0 !important;
	/* background: #f6fbfd; */
}

.gp-field-setting .asmListItemRemove {
	display: none;
	/* float: right; */
	text-decoration: none;
	visibility: hidden;
}

.gp-field-setting .asmListItemRemove:after {
	content: '\f057';
	font-family: 'GFFontAwesome', 'FontAwesome';
	visibility: visible;
	color: #c3c5db;
}

.gp-field-setting .asmListItemRemove:hover:after {
	color: #242748;
}

.gp-field-setting .asmListItem:hover .asmListItemRemove {
	display: inline-block;
	position: absolute;
	right: 0.8125rem;
}
