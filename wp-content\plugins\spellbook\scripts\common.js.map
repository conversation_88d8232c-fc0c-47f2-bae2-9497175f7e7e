{"version": 3, "sources": ["<anon>"], "sourcesContent": ["if (typeof gperk == 'undefined') {\n\tvar gperk = {};\n}\n\ngperk.is = function(variable, type) {\n\treturn typeof variable == type;\n}\n\ngperk.isUndefined = function(variable) {\n\treturn gperk.is( variable, 'undefined' );\n}\n\ngperk.isSet = function( variable ) {\n\treturn gperk.is( variable, 'undefined' );\n}\n\ngperk.ajaxSpinner = function(elem, imageSrc) {\n\n\tvar imageSrc = typeof imageSrc == 'undefined' ? gperk.gformBaseUrl + '/images/loading.gif' : imageSrc;\n\n\tthis.elem  = elem;\n\tthis.image = '<img src=\"' + imageSrc + '\" />';\n\n\tthis.init = function() {\n\t\tthis.spinner = jQuery( this.image );\n\t\tjQuery( this.elem ).after( this.spinner );\n\t\treturn this;\n\t}\n\n\tthis.destroy = function() {\n\t\tjQuery( this.spinner ).remove();\n\t}\n\n\treturn this.init();\n}\n\ngperk.applyFilter = function(filterName, value) {\n\treturn typeof window[filterName] != 'undefined' ? window[filterName]( value ) : value;\n}\n"], "names": ["gperk", "is", "variable", "type", "isUndefined", "isSet", "ajax<PERSON><PERSON><PERSON>", "elem", "imageSrc", "gformBaseUrl", "image", "init", "spinner", "j<PERSON><PERSON><PERSON>", "after", "destroy", "remove", "applyFilter", "filterName", "value", "window"], "mappings": "AAAA,GAAI,AAAgB,KAAA,IAATA,MACV,IAAIA,MAAQ,CAAC,CAGdA,CAAAA,MAAMC,EAAE,CAAG,SAASC,CAAQ,CAAEC,CAAI,EACjC,OAAO,OAAOD,GAAYC,CAC3B,EAEAH,MAAMI,WAAW,CAAG,SAASF,CAAQ,EACpC,OAAOF,MAAMC,EAAE,CAAEC,EAAU,YAC5B,EAEAF,MAAMK,KAAK,CAAG,SAAUH,CAAQ,EAC/B,OAAOF,MAAMC,EAAE,CAAEC,EAAU,YAC5B,EAEAF,MAAMM,WAAW,CAAG,SAASC,CAAI,CAAEC,CAAQ,EAE1C,IAAIA,EAAW,AAAmB,KAAA,IAAZA,EAA0BR,MAAMS,YAAY,CAAG,sBAAwBD,EAe7F,OAbA,IAAI,CAACD,IAAI,CAAIA,EACb,IAAI,CAACG,KAAK,CAAG,aAAeF,EAAW,OAEvC,IAAI,CAACG,IAAI,CAAG,WAGX,OAFA,IAAI,CAACC,OAAO,CAAGC,OAAQ,IAAI,CAACH,KAAK,EACjCG,OAAQ,IAAI,CAACN,IAAI,EAAGO,KAAK,CAAE,IAAI,CAACF,OAAO,EAChC,IAAI,AACZ,EAEA,IAAI,CAACG,OAAO,CAAG,WACdF,OAAQ,IAAI,CAACD,OAAO,EAAGI,MAAM,EAC9B,EAEO,IAAI,CAACL,IAAI,EACjB,EAEAX,MAAMiB,WAAW,CAAG,SAASC,CAAU,CAAEC,CAAK,EAC7C,OAAO,AAA6B,KAAA,IAAtBC,MAAM,CAACF,EAAW,CAAkBE,MAAM,CAACF,EAAW,CAAEC,GAAUA,CACjF"}