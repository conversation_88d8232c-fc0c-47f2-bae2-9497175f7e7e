<?php

/**
 * Enqueue child styles.
 */
 
function child_enqueue_styles() {
	wp_enqueue_style( 'child-theme', get_stylesheet_directory_uri() . '/style.css', array(), null );
}
add_action( 'wp_enqueue_scripts', 'child_enqueue_styles', 15 );

function remove_global_css() {
	remove_action( 'wp_enqueue_scripts', 'wp_enqueue_global_styles' );
	remove_action( 'wp_body_open', 'wp_global_styles_render_svg_filters' );
	remove_action( 'wp_footer', 'wp_enqueue_global_styles', 1 );
}
add_action('init', 'remove_global_css');

// Shortcode for dynamic year - use [year] in footer or as needed
function year_shortcode() {
  $year = date('Y');
  return $year;
}
add_shortcode('year', 'year_shortcode');

/* Update default GF error message gform_validation_message_FORMNUMBER */
add_filter( 'gform_validation_message_1', function ( $message, $form ) {
    if ( gf_upgrade()->get_submissions_block() ) {
        return $message;
    }

    $message = "<h2 class='gform_submission_error hide_summary'>Email address is required. Please <NAME_EMAIL> format</h2>";


    return $message;
}, 10, 2 );

// Include Google Maps functionality
require_once get_stylesheet_directory() . '/inc/acf-google-maps.php';

/**
 * Shortcode for displaying Google Maps for service locations
 * Usage: [midway_service_location_map height=400 width=400]
 */
function midway_service_location_map_shortcode( $atts ) {
    // Set default attributes
    $atts = shortcode_atts( array(
        'height' => 400,
        'width' => 400,
    ), $atts, 'midway_service_location_map' );

    // Validate and sanitize dimensions
    $height = absint( $atts['height'] );
    $width = absint( $atts['width'] );

    // Set minimum dimensions for usability
    $height = max( $height, 200 );
    $width = max( $width, 200 );

    // Get the current post
    global $post;

    // Check if we're on a service-location post type
    if ( ! $post || get_post_type( $post ) !== 'service-location' ) {
        return '<p><em>Google Map is only available on service location pages.</em></p>';
    }

    // Get the ACF address field
    $address_field = get_field( 'address', $post->ID );

    // Check if address field exists and has data
    if ( empty( $address_field ) || empty( $address_field['address'] ) ) {
        return '<p><em>No address available for this location.</em></p>';
    }

    // Get Google Maps API key from ACF
    $embed_api_key = get_field('google_map_embed_api_key', 'option');

    // Check if Google Maps API key is configured
    if ( empty( $embed_api_key ) || $embed_api_key === 'MAPS-EMBED-API-KEY' ) {
        return '<p><em>Google Maps API key not configured.</em></p>';
    }

    // Encode the address for URL
    $encoded_address = urlencode( $address_field['address'] );

    // Generate the iframe HTML
    $iframe_html = sprintf(
        '<iframe width="%d" height="%d" frameborder="0" style="border:0" src="https://www.google.com/maps/embed/v1/place?key=%s&q=%s" allowfullscreen loading="lazy"></iframe>',
        $width,
        $height,
        $embed_api_key,
        $encoded_address
    );

    return $iframe_html;
}

// Register the shortcode
add_shortcode( 'midway_service_location_map', 'midway_service_location_map_shortcode' );

/**
 * ACF JSON Save & Load Functionality
 * This allows ACF field groups to be saved as JSON files in the theme
 * for version control and easier deployment between environments
 */

/**
 * Save ACF JSON files to theme directory
 */
function kadence_child_acf_json_save_point( $path ) {
    // Update path to save JSON files in theme's acf-json directory
    $path = get_stylesheet_directory() . '/acf-json';

    // Create directory if it doesn't exist
    if ( ! file_exists( $path ) ) {
        wp_mkdir_p( $path );
    }

    return $path;
}
add_filter( 'acf/settings/save_json', 'kadence_child_acf_json_save_point' );

/**
 * Load ACF JSON files from theme directory
 */
function kadence_child_acf_json_load_point( $paths ) {
    // Remove original path (optional)
    unset( $paths[0] );

    // Add new path to load JSON files from theme's acf-json directory
    $paths[] = get_stylesheet_directory() . '/acf-json';

    return $paths;
}
add_filter( 'acf/settings/load_json', 'kadence_child_acf_json_load_point' );

