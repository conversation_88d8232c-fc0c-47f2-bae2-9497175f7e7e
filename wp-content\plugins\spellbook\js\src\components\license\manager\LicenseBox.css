.license-box__content {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 1.5rem;
}

.license-box--empty .license-box__content {
	gap: 0 1.5rem;
}

.license-box--empty .gform-meta-box__content {
    align-items: center;
}

.license-box__icon {
    grid-row: span 2;
}

.license-box__details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, auto));
    gap: 1rem;
    align-items: baseline;
    justify-content: start;
}

.license-box__stat {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.license-box__key-value {
    font-family: monospace !important;
}

.license-box__key-dots-long {
    display: inline;
}

.license-box__key-dots-medium,
.license-box__key-dots-short {
    display: none;
}

@container spellbook (max-width: 1150px) {
    .license-box__key-dots-long {
        display: none;
    }
    .license-box__key-dots-medium {
        display: inline;
    }
}

@container spellbook (max-width: 1000px) {
    .license-box__key-dots-medium {
        display: none;
    }
    .license-box__key-dots-short {
        display: inline;
    }
}

.license-box__actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    grid-column: 2 / -1;
}

.spellbook-app__content .license-box__actions > button {
	height: 2.375rem !important;
}

/* Unactivated state */
.license-box__empty {
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    align-items: start;
    gap: 1.5rem;
}

.license-box__info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
}

.license-box__separator {
	width: 1px;
	height: 100%;
	background-color: #e4e4ee;
    display: none;
}

@container spellbook (min-width: 900px) {
    .license-box__empty {
        grid-template-columns: minmax(0, 1fr) auto minmax(0, 1fr);
    }

    .license-box__separator {
        display: block;
    }
}

.license-box__form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
}

.license-box__form:has(.license-box__error-message) {
    gap: 0;
}

.license-box__form-inputs {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: flex-start;
}

.license-box__input-wrapper {
    flex: 1;
    min-width: 250px;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.license-box__error-message {
    margin: 0.5rem 0;
    color: #d63638 !important; /* WordPress error red */
}

@container spellbook (min-width: 600px) {
    .license-box__form-inputs {
        flex-wrap: wrap;
    }

    .license-box__form-inputs > div:first-child {
        width: auto;
    }
}

@container spellbook (max-width: 600px) {
    .license-box__content {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .license-box__icon {
        grid-row: auto;
        margin-bottom: 1rem;
    }

    .license-box__actions {
        grid-column: 1;
    }
}
