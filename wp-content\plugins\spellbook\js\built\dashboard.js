/*! For license information please see dashboard.js.LICENSE.txt */
(()=>{var e={20:(e,t,r)=>{"use strict";var n=r(609),i=Symbol.for("react.element"),o=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,s=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,r){var n,o={},c=null,u=null;for(n in void 0!==r&&(c=""+r),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)a.call(t,n)&&!l.hasOwnProperty(n)&&(o[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps)void 0===o[n]&&(o[n]=t[n]);return{$$typeof:i,type:e,key:c,ref:u,props:o,_owner:s.current}}t.Fragment=o,t.jsx=c,t.jsxs=c},55:e=>{"use strict";e.exports=function(e,t){for(var r={},n=Object.keys(e),i=Array.isArray(t),o=0;o<n.length;o++){var a=n[o],s=e[a];(i?-1!==t.indexOf(a):t(a,s,e))&&(r[a]=s)}return r}},124:(e,t,r)=>{var n=r(325);e.exports=function(){return n.Date.now()}},128:(e,t,r)=>{var n=r(800),i=/^\s+/;e.exports=function(e){return e?e.slice(0,n(e)+1).replace(i,""):e}},221:(e,t,r)=>{var n=r(805),i=r(124),o=r(374),a=Math.max,s=Math.min;e.exports=function(e,t,r){var l,c,u,d,h,p,f=0,m=!1,g=!1,y=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function v(t){var r=l,n=c;return l=c=void 0,f=t,d=e.apply(n,r)}function b(e){var r=e-p;return void 0===p||r>=t||r<0||g&&e-f>=u}function w(){var e=i();if(b(e))return x(e);h=setTimeout(w,function(e){var r=t-(e-p);return g?s(r,u-(e-f)):r}(e))}function x(e){return h=void 0,y&&l?v(e):(l=c=void 0,d)}function k(){var e=i(),r=b(e);if(l=arguments,c=this,p=e,r){if(void 0===h)return function(e){return f=e,h=setTimeout(w,t),m?v(e):d}(p);if(g)return clearTimeout(h),h=setTimeout(w,t),v(p)}return void 0===h&&(h=setTimeout(w,t)),d}return t=o(t)||0,n(r)&&(m=!!r.leading,u=(g="maxWait"in r)?a(o(r.maxWait)||0,t):u,y="trailing"in r?!!r.trailing:y),k.cancel=function(){void 0!==h&&clearTimeout(h),f=0,l=p=c=h=void 0},k.flush=function(){return void 0===h?d:x(i())},k}},232:(e,t)=>{"use strict";Object.prototype.toString},280:e=>{"use strict";e.exports=e=>encodeURIComponent(e).replace(/[!'()*]/g,(e=>`%${e.charCodeAt(0).toString(16).toUpperCase()}`))},325:(e,t,r)=>{var n=r(840),i="object"==typeof self&&self&&self.Object===Object&&self,o=n||i||Function("return this")();e.exports=o},338:(e,t,r)=>{"use strict";var n=r(795);t.H=n.createRoot,n.hydrateRoot},346:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},350:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},374:(e,t,r)=>{var n=r(128),i=r(805),o=r(394),a=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,l=/^0o[0-7]+$/i,c=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(o(e))return NaN;if(i(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=i(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=n(e);var r=s.test(e);return r||l.test(e)?c(e.slice(2),r?2:8):a.test(e)?NaN:+e}},394:(e,t,r)=>{var n=r(552),i=r(346);e.exports=function(e){return"symbol"==typeof e||i(e)&&"[object Symbol]"==n(e)}},454:e=>{"use strict";var t="%[a-f0-9]{2}",r=new RegExp("("+t+")|([^%]+?)","gi"),n=new RegExp("("+t+")+","gi");function i(e,t){try{return[decodeURIComponent(e.join(""))]}catch(e){}if(1===e.length)return e;t=t||1;var r=e.slice(0,t),n=e.slice(t);return Array.prototype.concat.call([],i(r),i(n))}function o(e){try{return decodeURIComponent(e)}catch(o){for(var t=e.match(r)||[],n=1;n<t.length;n++)t=(e=i(t,n).join("")).match(r)||[];return e}}e.exports=function(e){if("string"!=typeof e)throw new TypeError("Expected `encodedURI` to be of type `string`, got `"+typeof e+"`");try{return e=e.replace(/\+/g," "),decodeURIComponent(e)}catch(t){return function(e){for(var t={"%FE%FF":"��","%FF%FE":"��"},r=n.exec(e);r;){try{t[r[0]]=decodeURIComponent(r[0])}catch(e){var i=o(r[0]);i!==r[0]&&(t[r[0]]=i)}r=n.exec(e)}t["%C2"]="�";for(var a=Object.keys(t),s=0;s<a.length;s++){var l=a[s];e=e.replace(new RegExp(l,"g"),t[l])}return e}(e)}}},491:e=>{e.exports={nbsp:" ",cent:"¢",pound:"£",yen:"¥",euro:"€",copy:"©",reg:"®",lt:"<",gt:">",quot:'"',amp:"&",apos:"'"}},520:(e,t,r)=>{var n=r(871);e.exports=function(e){return null==e?"\\s":e.source?e.source:"["+n(e)+"]"}},528:e=>{"use strict";e.exports=(e,t)=>{if("string"!=typeof e||"string"!=typeof t)throw new TypeError("Expected the arguments to be of type `string`");if(""===t)return[e];const r=e.indexOf(t);return-1===r?[e]:[e.slice(0,r),e.slice(r+t.length)]}},552:(e,t,r)=>{var n=r(873),i=r(659),o=r(350),a=n?n.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":a&&a in Object(e)?i(e):o(e)}},556:(e,t,r)=>{e.exports=r(694)()},609:e=>{"use strict";e.exports=window.React},622:function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},n(e,t)},function(e,t){function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var a=o(r(556)),s=o(r(609)),l=function(e){function t(r){var n=e.call(this,r)||this;return n.resetDragging=function(){n.frameDragCounter=0,n.setState({draggingOverFrame:!1,draggingOverTarget:!1})},n.handleWindowDragOverOrDrop=function(e){e.preventDefault()},n.handleFrameDrag=function(e){if(t.eventHasFiles(e))return n.frameDragCounter+="dragenter"===e.type?1:-1,1===n.frameDragCounter?(n.setState({draggingOverFrame:!0}),void(n.props.onFrameDragEnter&&n.props.onFrameDragEnter(e))):0===n.frameDragCounter?(n.setState({draggingOverFrame:!1}),void(n.props.onFrameDragLeave&&n.props.onFrameDragLeave(e))):void 0},n.handleFrameDrop=function(e){n.state.draggingOverTarget||(n.resetDragging(),n.props.onFrameDrop&&n.props.onFrameDrop(e))},n.handleDragOver=function(e){t.eventHasFiles(e)&&(n.setState({draggingOverTarget:!0}),!t.isIE()&&n.props.dropEffect&&(e.dataTransfer.dropEffect=n.props.dropEffect),n.props.onDragOver&&n.props.onDragOver(e))},n.handleDragLeave=function(e){n.setState({draggingOverTarget:!1}),n.props.onDragLeave&&n.props.onDragLeave(e)},n.handleDrop=function(e){if(n.props.onDrop&&t.eventHasFiles(e)){var r=e.dataTransfer?e.dataTransfer.files:null;n.props.onDrop(r,e)}n.resetDragging()},n.handleTargetClick=function(e){n.props.onTargetClick&&n.props.onTargetClick(e),n.resetDragging()},n.stopFrameListeners=function(e){e&&(e.removeEventListener("dragenter",n.handleFrameDrag),e.removeEventListener("dragleave",n.handleFrameDrag),e.removeEventListener("drop",n.handleFrameDrop))},n.startFrameListeners=function(e){e&&(e.addEventListener("dragenter",n.handleFrameDrag),e.addEventListener("dragleave",n.handleFrameDrag),e.addEventListener("drop",n.handleFrameDrop))},n.frameDragCounter=0,n.state={draggingOverFrame:!1,draggingOverTarget:!1},n}return i(t,e),t.prototype.componentDidMount=function(){this.startFrameListeners(this.props.frame),this.resetDragging(),window.addEventListener("dragover",this.handleWindowDragOverOrDrop),window.addEventListener("drop",this.handleWindowDragOverOrDrop)},t.prototype.componentDidUpdate=function(e){e.frame!==this.props.frame&&(this.resetDragging(),this.stopFrameListeners(e.frame),this.startFrameListeners(this.props.frame))},t.prototype.componentWillUnmount=function(){this.stopFrameListeners(this.props.frame),window.removeEventListener("dragover",this.handleWindowDragOverOrDrop),window.removeEventListener("drop",this.handleWindowDragOverOrDrop)},t.prototype.render=function(){var e=this.props,t=e.children,r=e.className,n=e.targetClassName,i=e.draggingOverFrameClassName,o=e.draggingOverTargetClassName,a=this.state,l=a.draggingOverTarget,c=n;return a.draggingOverFrame&&(c+=" "+i),l&&(c+=" "+o),s.default.createElement("div",{className:r,onDragOver:this.handleDragOver,onDragLeave:this.handleDragLeave,onDrop:this.handleDrop},s.default.createElement("div",{className:c,onClick:this.handleTargetClick},t))},t.isIE=function(){return"undefined"!=typeof window&&(-1!==window.navigator.userAgent.indexOf("MSIE")||window.navigator.appVersion.indexOf("Trident/")>0)},t.eventHasFiles=function(e){var t=!1;if(e.dataTransfer){var r=e.dataTransfer.types;for(var n in r)if("Files"===r[n]){t=!0;break}}return t},t.propTypes={className:a.default.string,targetClassName:a.default.string,draggingOverFrameClassName:a.default.string,draggingOverTargetClassName:a.default.string,onDragOver:a.default.func,onDragLeave:a.default.func,onDrop:a.default.func,onTargetClick:a.default.func,dropEffect:a.default.oneOf(["copy","move","link","none"]),frame:function(e,t,r){var n=e[t];return null==n?new Error("Warning: Required prop `"+t+"` was not specified in `"+r+"`"):n===document||n instanceof HTMLElement?void 0:new Error("Warning: Prop `"+t+"` must be one of the following: document, HTMLElement!")},onFrameDragEnter:a.default.func,onFrameDragLeave:a.default.func,onFrameDrop:a.default.func},t.defaultProps={dropEffect:"copy",frame:"undefined"==typeof window?void 0:window.document,className:"file-drop",targetClassName:"file-drop-target",draggingOverFrameClassName:"file-drop-dragging-over-frame",draggingOverTargetClassName:"file-drop-dragging-over-target"},t}(s.default.PureComponent);t.FileDrop=l},655:(e,t,r)=>{var n=r(730);e.exports=function(e){return n(e).replace(/<\/?[^>]+>/g,"")}},659:(e,t,r)=>{var n=r(873),i=Object.prototype,o=i.hasOwnProperty,a=i.toString,s=n?n.toStringTag:void 0;e.exports=function(e){var t=o.call(e,s),r=e[s];try{e[s]=void 0;var n=!0}catch(e){}var i=a.call(e);return n&&(t?e[s]=r:delete e[s]),i}},663:(e,t,r)=>{"use strict";const n=r(280),i=r(454),o=r(528),a=r(55),s=Symbol("encodeFragmentIdentifier");function l(e){if("string"!=typeof e||1!==e.length)throw new TypeError("arrayFormatSeparator must be single character string")}function c(e,t){return t.encode?t.strict?n(e):encodeURIComponent(e):e}function u(e,t){return t.decode?i(e):e}function d(e){return Array.isArray(e)?e.sort():"object"==typeof e?d(Object.keys(e)).sort(((e,t)=>Number(e)-Number(t))).map((t=>e[t])):e}function h(e){const t=e.indexOf("#");return-1!==t&&(e=e.slice(0,t)),e}function p(e){const t=(e=h(e)).indexOf("?");return-1===t?"":e.slice(t+1)}function f(e,t){return t.parseNumbers&&!Number.isNaN(Number(e))&&"string"==typeof e&&""!==e.trim()?e=Number(e):!t.parseBooleans||null===e||"true"!==e.toLowerCase()&&"false"!==e.toLowerCase()||(e="true"===e.toLowerCase()),e}function m(e,t){l((t=Object.assign({decode:!0,sort:!0,arrayFormat:"none",arrayFormatSeparator:",",parseNumbers:!1,parseBooleans:!1},t)).arrayFormatSeparator);const r=function(e){let t;switch(e.arrayFormat){case"index":return(e,r,n)=>{t=/\[(\d*)\]$/.exec(e),e=e.replace(/\[\d*\]$/,""),t?(void 0===n[e]&&(n[e]={}),n[e][t[1]]=r):n[e]=r};case"bracket":return(e,r,n)=>{t=/(\[\])$/.exec(e),e=e.replace(/\[\]$/,""),t?void 0!==n[e]?n[e]=[].concat(n[e],r):n[e]=[r]:n[e]=r};case"comma":case"separator":return(t,r,n)=>{const i="string"==typeof r&&r.includes(e.arrayFormatSeparator),o="string"==typeof r&&!i&&u(r,e).includes(e.arrayFormatSeparator);r=o?u(r,e):r;const a=i||o?r.split(e.arrayFormatSeparator).map((t=>u(t,e))):null===r?r:u(r,e);n[t]=a};case"bracket-separator":return(t,r,n)=>{const i=/(\[\])$/.test(t);if(t=t.replace(/\[\]$/,""),!i)return void(n[t]=r?u(r,e):r);const o=null===r?[]:r.split(e.arrayFormatSeparator).map((t=>u(t,e)));void 0!==n[t]?n[t]=[].concat(n[t],o):n[t]=o};default:return(e,t,r)=>{void 0!==r[e]?r[e]=[].concat(r[e],t):r[e]=t}}}(t),n=Object.create(null);if("string"!=typeof e)return n;if(!(e=e.trim().replace(/^[?#&]/,"")))return n;for(const i of e.split("&")){if(""===i)continue;let[e,a]=o(t.decode?i.replace(/\+/g," "):i,"=");a=void 0===a?null:["comma","separator","bracket-separator"].includes(t.arrayFormat)?a:u(a,t),r(u(e,t),a,n)}for(const e of Object.keys(n)){const r=n[e];if("object"==typeof r&&null!==r)for(const e of Object.keys(r))r[e]=f(r[e],t);else n[e]=f(r,t)}return!1===t.sort?n:(!0===t.sort?Object.keys(n).sort():Object.keys(n).sort(t.sort)).reduce(((e,t)=>{const r=n[t];return Boolean(r)&&"object"==typeof r&&!Array.isArray(r)?e[t]=d(r):e[t]=r,e}),Object.create(null))}t.extract=p,t.parse=m,t.stringify=(e,t)=>{if(!e)return"";l((t=Object.assign({encode:!0,strict:!0,arrayFormat:"none",arrayFormatSeparator:","},t)).arrayFormatSeparator);const r=r=>t.skipNull&&null==e[r]||t.skipEmptyString&&""===e[r],n=function(e){switch(e.arrayFormat){case"index":return t=>(r,n)=>{const i=r.length;return void 0===n||e.skipNull&&null===n||e.skipEmptyString&&""===n?r:null===n?[...r,[c(t,e),"[",i,"]"].join("")]:[...r,[c(t,e),"[",c(i,e),"]=",c(n,e)].join("")]};case"bracket":return t=>(r,n)=>void 0===n||e.skipNull&&null===n||e.skipEmptyString&&""===n?r:null===n?[...r,[c(t,e),"[]"].join("")]:[...r,[c(t,e),"[]=",c(n,e)].join("")];case"comma":case"separator":case"bracket-separator":{const t="bracket-separator"===e.arrayFormat?"[]=":"=";return r=>(n,i)=>void 0===i||e.skipNull&&null===i||e.skipEmptyString&&""===i?n:(i=null===i?"":i,0===n.length?[[c(r,e),t,c(i,e)].join("")]:[[n,c(i,e)].join(e.arrayFormatSeparator)])}default:return t=>(r,n)=>void 0===n||e.skipNull&&null===n||e.skipEmptyString&&""===n?r:null===n?[...r,c(t,e)]:[...r,[c(t,e),"=",c(n,e)].join("")]}}(t),i={};for(const t of Object.keys(e))r(t)||(i[t]=e[t]);const o=Object.keys(i);return!1!==t.sort&&o.sort(t.sort),o.map((r=>{const i=e[r];return void 0===i?"":null===i?c(r,t):Array.isArray(i)?0===i.length&&"bracket-separator"===t.arrayFormat?c(r,t)+"[]":i.reduce(n(r),[]).join("&"):c(r,t)+"="+c(i,t)})).filter((e=>e.length>0)).join("&")},t.parseUrl=(e,t)=>{t=Object.assign({decode:!0},t);const[r,n]=o(e,"#");return Object.assign({url:r.split("?")[0]||"",query:m(p(e),t)},t&&t.parseFragmentIdentifier&&n?{fragmentIdentifier:u(n,t)}:{})},t.stringifyUrl=(e,r)=>{r=Object.assign({encode:!0,strict:!0,[s]:!0},r);const n=h(e.url).split("?")[0]||"",i=t.extract(e.url),o=t.parse(i,{sort:!1}),a=Object.assign(o,e.query);let l=t.stringify(a,r);l&&(l=`?${l}`);let u=function(e){let t="";const r=e.indexOf("#");return-1!==r&&(t=e.slice(r)),t}(e.url);return e.fragmentIdentifier&&(u=`#${r[s]?c(e.fragmentIdentifier,r):e.fragmentIdentifier}`),`${n}${l}${u}`},t.pick=(e,r,n)=>{n=Object.assign({parseFragmentIdentifier:!0,[s]:!1},n);const{url:i,query:o,fragmentIdentifier:l}=t.parseUrl(e,n);return t.stringifyUrl({url:i,query:a(o,r),fragmentIdentifier:l},n)},t.exclude=(e,r,n)=>{const i=Array.isArray(r)?e=>!r.includes(e):(e,t)=>!r(e,t);return t.pick(e,i,n)}},694:(e,t,r)=>{"use strict";var n=r(925);function i(){}function o(){}o.resetWarningCache=i,e.exports=function(){function e(e,t,r,i,o,a){if(a!==n){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:i};return r.PropTypes=r,r}},710:(e,t,r)=>{var n=r(730),i=r(520),o=String.prototype.trim;e.exports=function(e,t){return e=n(e),!t&&o?o.call(e):(t=i(t),e.replace(new RegExp("^"+t+"+|"+t+"+$","g"),""))}},730:e=>{e.exports=function(e){return null==e?"":""+e}},764:function(e,t,r){var n;n=e=>(()=>{var t={703:(e,t,r)=>{"use strict";var n=r(414);function i(){}function o(){}o.resetWarningCache=i,e.exports=function(){function e(e,t,r,i,o,a){if(a!==n){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:i};return r.PropTypes=r,r}},697:(e,t,r)=>{e.exports=r(703)()},414:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},98:t=>{"use strict";t.exports=e}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var o=r[e]={exports:{}};return t[e](o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};return(()=>{"use strict";n.r(i),n.d(i,{default:()=>w});var e=n(98),t=n.n(e),r=n(697),o=n.n(r);function a(){return a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},a.apply(this,arguments)}var s=function(e){var r=e.pageClassName,n=e.pageLinkClassName,i=e.page,o=e.selected,s=e.activeClassName,l=e.activeLinkClassName,c=e.getEventListener,u=e.pageSelectedHandler,d=e.href,h=e.extraAriaContext,p=e.pageLabelBuilder,f=e.rel,m=e.ariaLabel||"Page "+i+(h?" "+h:""),g=null;return o&&(g="page",m=e.ariaLabel||"Page "+i+" is your current page",r=void 0!==r?r+" "+s:s,void 0!==n?void 0!==l&&(n=n+" "+l):n=l),t().createElement("li",{className:r},t().createElement("a",a({rel:f,role:d?void 0:"button",className:n,href:d,tabIndex:o?"-1":"0","aria-label":m,"aria-current":g,onKeyPress:u},c(u)),p(i)))};s.propTypes={pageSelectedHandler:o().func.isRequired,selected:o().bool.isRequired,pageClassName:o().string,pageLinkClassName:o().string,activeClassName:o().string,activeLinkClassName:o().string,extraAriaContext:o().string,href:o().string,ariaLabel:o().string,page:o().number.isRequired,getEventListener:o().func.isRequired,pageLabelBuilder:o().func.isRequired,rel:o().string};const l=s;function c(){return c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},c.apply(this,arguments)}var u=function(e){var r=e.breakLabel,n=e.breakAriaLabel,i=e.breakClassName,o=e.breakLinkClassName,a=e.breakHandler,s=e.getEventListener,l=i||"break";return t().createElement("li",{className:l},t().createElement("a",c({className:o,role:"button",tabIndex:"0","aria-label":n,onKeyPress:a},s(a)),r))};u.propTypes={breakLabel:o().oneOfType([o().string,o().node]),breakAriaLabel:o().string,breakClassName:o().string,breakLinkClassName:o().string,breakHandler:o().func.isRequired,getEventListener:o().func.isRequired};const d=u;function h(e){return null!=e?e:arguments.length>1&&void 0!==arguments[1]?arguments[1]:""}function p(e){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}function f(){return f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},f.apply(this,arguments)}function m(e,t){return m=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},m(e,t)}function g(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function y(e){return y=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},y(e)}function v(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var b=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&m(e,t)}(s,e);var r,n,i,o,a=(i=s,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=y(i);if(o){var r=y(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return function(e,t){if(t&&("object"===p(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return g(e)}(this,e)});function s(e){var r,n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),v(g(r=a.call(this,e)),"handlePreviousPage",(function(e){var t=r.state.selected;r.handleClick(e,null,t>0?t-1:void 0,{isPrevious:!0})})),v(g(r),"handleNextPage",(function(e){var t=r.state.selected,n=r.props.pageCount;r.handleClick(e,null,t<n-1?t+1:void 0,{isNext:!0})})),v(g(r),"handlePageSelected",(function(e,t){if(r.state.selected===e)return r.callActiveCallback(e),void r.handleClick(t,null,void 0,{isActive:!0});r.handleClick(t,null,e)})),v(g(r),"handlePageChange",(function(e){r.state.selected!==e&&(r.setState({selected:e}),r.callCallback(e))})),v(g(r),"getEventListener",(function(e){return v({},r.props.eventListener,e)})),v(g(r),"handleClick",(function(e,t,n){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=i.isPrevious,a=void 0!==o&&o,s=i.isNext,l=void 0!==s&&s,c=i.isBreak,u=void 0!==c&&c,d=i.isActive,h=void 0!==d&&d;e.preventDefault?e.preventDefault():e.returnValue=!1;var p=r.state.selected,f=r.props.onClick,m=n;if(f){var g=f({index:t,selected:p,nextSelectedPage:n,event:e,isPrevious:a,isNext:l,isBreak:u,isActive:h});if(!1===g)return;Number.isInteger(g)&&(m=g)}void 0!==m&&r.handlePageChange(m)})),v(g(r),"handleBreakClick",(function(e,t){var n=r.state.selected;r.handleClick(t,e,n<e?r.getForwardJump():r.getBackwardJump(),{isBreak:!0})})),v(g(r),"callCallback",(function(e){void 0!==r.props.onPageChange&&"function"==typeof r.props.onPageChange&&r.props.onPageChange({selected:e})})),v(g(r),"callActiveCallback",(function(e){void 0!==r.props.onPageActive&&"function"==typeof r.props.onPageActive&&r.props.onPageActive({selected:e})})),v(g(r),"getElementPageRel",(function(e){var t=r.state.selected,n=r.props,i=n.nextPageRel,o=n.prevPageRel,a=n.selectedPageRel;return t-1===e?o:t===e?a:t+1===e?i:void 0})),v(g(r),"pagination",(function(){var e=[],n=r.props,i=n.pageRangeDisplayed,o=n.pageCount,a=n.marginPagesDisplayed,s=n.breakLabel,l=n.breakClassName,c=n.breakLinkClassName,u=n.breakAriaLabels,h=r.state.selected;if(o<=i)for(var p=0;p<o;p++)e.push(r.getPageElement(p));else{var f=i/2,m=i-f;h>o-i/2?f=i-(m=o-h):h<i/2&&(m=i-(f=h));var g,y,v=function(e){return r.getPageElement(e)},b=[];for(g=0;g<o;g++){var w=g+1;if(w<=a)b.push({type:"page",index:g,display:v(g)});else if(w>o-a)b.push({type:"page",index:g,display:v(g)});else if(g>=h-f&&g<=h+(0===h&&i>1?m-1:m))b.push({type:"page",index:g,display:v(g)});else if(s&&b.length>0&&b[b.length-1].display!==y&&(i>0||a>0)){var x=g<h?u.backward:u.forward;y=t().createElement(d,{key:g,breakAriaLabel:x,breakLabel:s,breakClassName:l,breakLinkClassName:c,breakHandler:r.handleBreakClick.bind(null,g),getEventListener:r.getEventListener}),b.push({type:"break",index:g,display:y})}}b.forEach((function(t,r){var n=t;"break"===t.type&&b[r-1]&&"page"===b[r-1].type&&b[r+1]&&"page"===b[r+1].type&&b[r+1].index-b[r-1].index<=2&&(n={type:"page",index:t.index,display:v(t.index)}),e.push(n.display)}))}return e})),void 0!==e.initialPage&&void 0!==e.forcePage&&console.warn("(react-paginate): Both initialPage (".concat(e.initialPage,") and forcePage (").concat(e.forcePage,") props are provided, which is discouraged.")+" Use exclusively forcePage prop for a controlled component.\nSee https://reactjs.org/docs/forms.html#controlled-components"),n=e.initialPage?e.initialPage:e.forcePage?e.forcePage:0,r.state={selected:n},r}return r=s,(n=[{key:"componentDidMount",value:function(){var e=this.props,t=e.initialPage,r=e.disableInitialCallback,n=e.extraAriaContext,i=e.pageCount,o=e.forcePage;void 0===t||r||this.callCallback(t),n&&console.warn("DEPRECATED (react-paginate): The extraAriaContext prop is deprecated. You should now use the ariaLabelBuilder instead."),Number.isInteger(i)||console.warn("(react-paginate): The pageCount prop value provided is not an integer (".concat(i,"). Did you forget a Math.ceil()?")),void 0!==t&&t>i-1&&console.warn("(react-paginate): The initialPage prop provided is greater than the maximum page index from pageCount prop (".concat(t," > ").concat(i-1,").")),void 0!==o&&o>i-1&&console.warn("(react-paginate): The forcePage prop provided is greater than the maximum page index from pageCount prop (".concat(o," > ").concat(i-1,")."))}},{key:"componentDidUpdate",value:function(e){void 0!==this.props.forcePage&&this.props.forcePage!==e.forcePage&&(this.props.forcePage>this.props.pageCount-1&&console.warn("(react-paginate): The forcePage prop provided is greater than the maximum page index from pageCount prop (".concat(this.props.forcePage," > ").concat(this.props.pageCount-1,").")),this.setState({selected:this.props.forcePage})),Number.isInteger(e.pageCount)&&!Number.isInteger(this.props.pageCount)&&console.warn("(react-paginate): The pageCount prop value provided is not an integer (".concat(this.props.pageCount,"). Did you forget a Math.ceil()?"))}},{key:"getForwardJump",value:function(){var e=this.state.selected,t=this.props,r=t.pageCount,n=e+t.pageRangeDisplayed;return n>=r?r-1:n}},{key:"getBackwardJump",value:function(){var e=this.state.selected-this.props.pageRangeDisplayed;return e<0?0:e}},{key:"getElementHref",value:function(e){var t=this.props,r=t.hrefBuilder,n=t.pageCount,i=t.hrefAllControls;if(r)return i||e>=0&&e<n?r(e+1,n,this.state.selected):void 0}},{key:"ariaLabelBuilder",value:function(e){var t=e===this.state.selected;if(this.props.ariaLabelBuilder&&e>=0&&e<this.props.pageCount){var r=this.props.ariaLabelBuilder(e+1,t);return this.props.extraAriaContext&&!t&&(r=r+" "+this.props.extraAriaContext),r}}},{key:"getPageElement",value:function(e){var r=this.state.selected,n=this.props,i=n.pageClassName,o=n.pageLinkClassName,a=n.activeClassName,s=n.activeLinkClassName,c=n.extraAriaContext,u=n.pageLabelBuilder;return t().createElement(l,{key:e,pageSelectedHandler:this.handlePageSelected.bind(null,e),selected:r===e,rel:this.getElementPageRel(e),pageClassName:i,pageLinkClassName:o,activeClassName:a,activeLinkClassName:s,extraAriaContext:c,href:this.getElementHref(e),ariaLabel:this.ariaLabelBuilder(e),page:e+1,pageLabelBuilder:u,getEventListener:this.getEventListener})}},{key:"render",value:function(){var e=this.props.renderOnZeroPageCount;if(0===this.props.pageCount&&void 0!==e)return e?e(this.props):e;var r=this.props,n=r.disabledClassName,i=r.disabledLinkClassName,o=r.pageCount,a=r.className,s=r.containerClassName,l=r.previousLabel,c=r.previousClassName,u=r.previousLinkClassName,d=r.previousAriaLabel,p=r.prevRel,m=r.nextLabel,g=r.nextClassName,y=r.nextLinkClassName,v=r.nextAriaLabel,b=r.nextRel,w=this.state.selected,x=0===w,k=w===o-1,E="".concat(h(c)).concat(x?" ".concat(h(n)):""),C="".concat(h(g)).concat(k?" ".concat(h(n)):""),j="".concat(h(u)).concat(x?" ".concat(h(i)):""),O="".concat(h(y)).concat(k?" ".concat(h(i)):""),_=x?"true":"false",S=k?"true":"false";return t().createElement("ul",{className:a||s,role:"navigation","aria-label":"Pagination"},t().createElement("li",{className:E},t().createElement("a",f({className:j,href:this.getElementHref(w-1),tabIndex:x?"-1":"0",role:"button",onKeyPress:this.handlePreviousPage,"aria-disabled":_,"aria-label":d,rel:p},this.getEventListener(this.handlePreviousPage)),l)),this.pagination(),t().createElement("li",{className:C},t().createElement("a",f({className:O,href:this.getElementHref(w+1),tabIndex:k?"-1":"0",role:"button",onKeyPress:this.handleNextPage,"aria-disabled":S,"aria-label":v,rel:b},this.getEventListener(this.handleNextPage)),m)))}}])&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),s}(e.Component);v(b,"propTypes",{pageCount:o().number.isRequired,pageRangeDisplayed:o().number,marginPagesDisplayed:o().number,previousLabel:o().node,previousAriaLabel:o().string,prevPageRel:o().string,prevRel:o().string,nextLabel:o().node,nextAriaLabel:o().string,nextPageRel:o().string,nextRel:o().string,breakLabel:o().oneOfType([o().string,o().node]),breakAriaLabels:o().shape({forward:o().string,backward:o().string}),hrefBuilder:o().func,hrefAllControls:o().bool,onPageChange:o().func,onPageActive:o().func,onClick:o().func,initialPage:o().number,forcePage:o().number,disableInitialCallback:o().bool,containerClassName:o().string,className:o().string,pageClassName:o().string,pageLinkClassName:o().string,pageLabelBuilder:o().func,activeClassName:o().string,activeLinkClassName:o().string,previousClassName:o().string,nextClassName:o().string,previousLinkClassName:o().string,nextLinkClassName:o().string,disabledClassName:o().string,disabledLinkClassName:o().string,breakClassName:o().string,breakLinkClassName:o().string,extraAriaContext:o().string,ariaLabelBuilder:o().func,eventListener:o().string,renderOnZeroPageCount:o().func,selectedPageRel:o().string}),v(b,"defaultProps",{pageRangeDisplayed:2,marginPagesDisplayed:3,activeClassName:"selected",previousLabel:"Previous",previousClassName:"previous",previousAriaLabel:"Previous page",prevPageRel:"prev",prevRel:"prev",nextLabel:"Next",nextClassName:"next",nextAriaLabel:"Next page",nextPageRel:"next",nextRel:"next",breakLabel:"...",breakAriaLabels:{forward:"Jump forward",backward:"Jump backward"},disabledClassName:"disabled",disableInitialCallback:!1,pageLabelBuilder:function(e){return e},eventListener:"onClick",renderOnZeroPageCount:void 0,selectedPageRel:"canonical",hrefAllControls:!1});const w=b})(),i})(),e.exports=n(r(609))},795:e=>{"use strict";e.exports=window.ReactDOM},800:e=>{var t=/\s/;e.exports=function(e){for(var r=e.length;r--&&t.test(e.charAt(r)););return r}},805:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},840:(e,t,r)=>{var n="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g;e.exports=n},848:(e,t,r)=>{"use strict";e.exports=r(20)},871:(e,t,r)=>{var n=r(730);e.exports=function(e){return n(e).replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}},873:(e,t,r)=>{var n=r(325).Symbol;e.exports=n},891:(e,t,r)=>{var n=r(730),i=r(491);e.exports=function(e){return n(e).replace(/\&([^;]{1,10});/g,(function(e,t){var r;return t in i?i[t]:(r=t.match(/^#x([\da-fA-F]+)$/))?String.fromCharCode(parseInt(r[1],16)):(r=t.match(/^#(\d+)$/))?String.fromCharCode(~~r[1]):e}))}},925:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},942:(e,t)=>{var r;!function(){"use strict";var n={}.hasOwnProperty;function i(){for(var e="",t=0;t<arguments.length;t++){var r=arguments[t];r&&(e=a(e,o(r)))}return e}function o(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return i.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var r in e)n.call(e,r)&&e[r]&&(t=a(t,r));return t}function a(e,t){return t?e?e+" "+t:e+t:e}e.exports?(i.default=i,e.exports=i):void 0===(r=function(){return i}.apply(t,[]))||(e.exports=r)}()},969:(e,t,r)=>{var n=r(221),i=r(805);e.exports=function(e,t,r){var o=!0,a=!0;if("function"!=typeof e)throw new TypeError("Expected a function");return i(r)&&(o="leading"in r?!!r.leading:o,a="trailing"in r?!!r.trailing:a),n(e,t,{leading:o,maxWait:t,trailing:a})}}},t={};function r(n){var i=t[n];if(void 0!==i)return i.exports;var o=t[n]={exports:{}};return e[n].call(o.exports,o,o.exports,r),o.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";var e={};r.r(e),r.d(e,{Zp:()=>St});var t=r(848),n=r(609),i=r.n(n),o=r(338),a=(r(622),r(556)),s=r.n(a),l=r(942),c=r.n(l),u=r(221),d=r(969),h=function(){return h=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},h.apply(this,arguments)};function p(e){return e&&e.ownerDocument&&e.ownerDocument.defaultView?e.ownerDocument.defaultView:window}function f(e){return e&&e.ownerDocument?e.ownerDocument:document}var m=function(e){return Array.prototype.reduce.call(e,(function(e,t){var r=t.name.match(/data-simplebar-(.+)/);if(r){var n=r[1].replace(/\W+(.)/g,(function(e,t){return t.toUpperCase()}));switch(t.value){case"true":e[n]=!0;break;case"false":e[n]=!1;break;case void 0:e[n]=!0;break;default:e[n]=t.value}}return e}),{})};function g(e,t){var r;e&&(r=e.classList).add.apply(r,t.split(" "))}function y(e,t){e&&t.split(" ").forEach((function(t){e.classList.remove(t)}))}function v(e){return".".concat(e.split(" ").join("."))}var b=!("undefined"==typeof window||!window.document||!window.document.createElement),w=Object.freeze({__proto__:null,addClasses:g,canUseDOM:b,classNamesToQuery:v,getElementDocument:f,getElementWindow:p,getOptions:m,removeClasses:y}),x=null,k=null;function E(){if(null===x){if("undefined"==typeof document)return x=0;var e=document.body,t=document.createElement("div");t.classList.add("simplebar-hide-scrollbar"),e.appendChild(t);var r=t.getBoundingClientRect().right;e.removeChild(t),x=r}return x}b&&window.addEventListener("resize",(function(){k!==window.devicePixelRatio&&(k=window.devicePixelRatio,x=null)}));var C=p,j=f,O=m,_=g,S=y,A=v,P=function(){function e(t,r){void 0===r&&(r={});var n=this;if(this.removePreventClickId=null,this.minScrollbarWidth=20,this.stopScrollDelay=175,this.isScrolling=!1,this.isMouseEntering=!1,this.isDragging=!1,this.scrollXTicking=!1,this.scrollYTicking=!1,this.wrapperEl=null,this.contentWrapperEl=null,this.contentEl=null,this.offsetEl=null,this.maskEl=null,this.placeholderEl=null,this.heightAutoObserverWrapperEl=null,this.heightAutoObserverEl=null,this.rtlHelpers=null,this.scrollbarWidth=0,this.resizeObserver=null,this.mutationObserver=null,this.elStyles=null,this.isRtl=null,this.mouseX=0,this.mouseY=0,this.onMouseMove=function(){},this.onWindowResize=function(){},this.onStopScrolling=function(){},this.onMouseEntered=function(){},this.onScroll=function(){var e=C(n.el);n.scrollXTicking||(e.requestAnimationFrame(n.scrollX),n.scrollXTicking=!0),n.scrollYTicking||(e.requestAnimationFrame(n.scrollY),n.scrollYTicking=!0),n.isScrolling||(n.isScrolling=!0,_(n.el,n.classNames.scrolling)),n.showScrollbar("x"),n.showScrollbar("y"),n.onStopScrolling()},this.scrollX=function(){n.axis.x.isOverflowing&&n.positionScrollbar("x"),n.scrollXTicking=!1},this.scrollY=function(){n.axis.y.isOverflowing&&n.positionScrollbar("y"),n.scrollYTicking=!1},this._onStopScrolling=function(){S(n.el,n.classNames.scrolling),n.options.autoHide&&(n.hideScrollbar("x"),n.hideScrollbar("y")),n.isScrolling=!1},this.onMouseEnter=function(){n.isMouseEntering||(_(n.el,n.classNames.mouseEntered),n.showScrollbar("x"),n.showScrollbar("y"),n.isMouseEntering=!0),n.onMouseEntered()},this._onMouseEntered=function(){S(n.el,n.classNames.mouseEntered),n.options.autoHide&&(n.hideScrollbar("x"),n.hideScrollbar("y")),n.isMouseEntering=!1},this._onMouseMove=function(e){n.mouseX=e.clientX,n.mouseY=e.clientY,(n.axis.x.isOverflowing||n.axis.x.forceVisible)&&n.onMouseMoveForAxis("x"),(n.axis.y.isOverflowing||n.axis.y.forceVisible)&&n.onMouseMoveForAxis("y")},this.onMouseLeave=function(){n.onMouseMove.cancel(),(n.axis.x.isOverflowing||n.axis.x.forceVisible)&&n.onMouseLeaveForAxis("x"),(n.axis.y.isOverflowing||n.axis.y.forceVisible)&&n.onMouseLeaveForAxis("y"),n.mouseX=-1,n.mouseY=-1},this._onWindowResize=function(){n.scrollbarWidth=n.getScrollbarWidth(),n.hideNativeScrollbar()},this.onPointerEvent=function(e){var t,r;n.axis.x.track.el&&n.axis.y.track.el&&n.axis.x.scrollbar.el&&n.axis.y.scrollbar.el&&(n.axis.x.track.rect=n.axis.x.track.el.getBoundingClientRect(),n.axis.y.track.rect=n.axis.y.track.el.getBoundingClientRect(),(n.axis.x.isOverflowing||n.axis.x.forceVisible)&&(t=n.isWithinBounds(n.axis.x.track.rect)),(n.axis.y.isOverflowing||n.axis.y.forceVisible)&&(r=n.isWithinBounds(n.axis.y.track.rect)),(t||r)&&(e.stopPropagation(),"pointerdown"===e.type&&"touch"!==e.pointerType&&(t&&(n.axis.x.scrollbar.rect=n.axis.x.scrollbar.el.getBoundingClientRect(),n.isWithinBounds(n.axis.x.scrollbar.rect)?n.onDragStart(e,"x"):n.onTrackClick(e,"x")),r&&(n.axis.y.scrollbar.rect=n.axis.y.scrollbar.el.getBoundingClientRect(),n.isWithinBounds(n.axis.y.scrollbar.rect)?n.onDragStart(e,"y"):n.onTrackClick(e,"y")))))},this.drag=function(t){var r,i,o,a,s,l,c,u,d,h,p;if(n.draggedAxis&&n.contentWrapperEl){var f=n.axis[n.draggedAxis].track,m=null!==(i=null===(r=f.rect)||void 0===r?void 0:r[n.axis[n.draggedAxis].sizeAttr])&&void 0!==i?i:0,g=n.axis[n.draggedAxis].scrollbar,y=null!==(a=null===(o=n.contentWrapperEl)||void 0===o?void 0:o[n.axis[n.draggedAxis].scrollSizeAttr])&&void 0!==a?a:0,v=parseInt(null!==(l=null===(s=n.elStyles)||void 0===s?void 0:s[n.axis[n.draggedAxis].sizeAttr])&&void 0!==l?l:"0px",10);t.preventDefault(),t.stopPropagation();var b=("y"===n.draggedAxis?t.pageY:t.pageX)-(null!==(u=null===(c=f.rect)||void 0===c?void 0:c[n.axis[n.draggedAxis].offsetAttr])&&void 0!==u?u:0)-n.axis[n.draggedAxis].dragOffset,w=(b="x"===n.draggedAxis&&n.isRtl?(null!==(h=null===(d=f.rect)||void 0===d?void 0:d[n.axis[n.draggedAxis].sizeAttr])&&void 0!==h?h:0)-g.size-b:b)/(m-g.size)*(y-v);"x"===n.draggedAxis&&n.isRtl&&(w=(null===(p=e.getRtlHelpers())||void 0===p?void 0:p.isScrollingToNegative)?-w:w),n.contentWrapperEl[n.axis[n.draggedAxis].scrollOffsetAttr]=w}},this.onEndDrag=function(e){n.isDragging=!1;var t=j(n.el),r=C(n.el);e.preventDefault(),e.stopPropagation(),S(n.el,n.classNames.dragging),n.onStopScrolling(),t.removeEventListener("mousemove",n.drag,!0),t.removeEventListener("mouseup",n.onEndDrag,!0),n.removePreventClickId=r.setTimeout((function(){t.removeEventListener("click",n.preventClick,!0),t.removeEventListener("dblclick",n.preventClick,!0),n.removePreventClickId=null}))},this.preventClick=function(e){e.preventDefault(),e.stopPropagation()},this.el=t,this.options=h(h({},e.defaultOptions),r),this.classNames=h(h({},e.defaultOptions.classNames),r.classNames),this.axis={x:{scrollOffsetAttr:"scrollLeft",sizeAttr:"width",scrollSizeAttr:"scrollWidth",offsetSizeAttr:"offsetWidth",offsetAttr:"left",overflowAttr:"overflowX",dragOffset:0,isOverflowing:!0,forceVisible:!1,track:{size:null,el:null,rect:null,isVisible:!1},scrollbar:{size:null,el:null,rect:null,isVisible:!1}},y:{scrollOffsetAttr:"scrollTop",sizeAttr:"height",scrollSizeAttr:"scrollHeight",offsetSizeAttr:"offsetHeight",offsetAttr:"top",overflowAttr:"overflowY",dragOffset:0,isOverflowing:!0,forceVisible:!1,track:{size:null,el:null,rect:null,isVisible:!1},scrollbar:{size:null,el:null,rect:null,isVisible:!1}}},"object"!=typeof this.el||!this.el.nodeName)throw new Error("Argument passed to SimpleBar must be an HTML element instead of ".concat(this.el));this.onMouseMove=d(this._onMouseMove,64),this.onWindowResize=u(this._onWindowResize,64,{leading:!0}),this.onStopScrolling=u(this._onStopScrolling,this.stopScrollDelay),this.onMouseEntered=u(this._onMouseEntered,this.stopScrollDelay),this.init()}return e.getRtlHelpers=function(){if(e.rtlHelpers)return e.rtlHelpers;var t=document.createElement("div");t.innerHTML='<div class="simplebar-dummy-scrollbar-size"><div></div></div>';var r=t.firstElementChild,n=null==r?void 0:r.firstElementChild;if(!n)return null;document.body.appendChild(r),r.scrollLeft=0;var i=e.getOffset(r),o=e.getOffset(n);r.scrollLeft=-999;var a=e.getOffset(n);return document.body.removeChild(r),e.rtlHelpers={isScrollOriginAtZero:i.left!==o.left,isScrollingToNegative:o.left!==a.left},e.rtlHelpers},e.prototype.getScrollbarWidth=function(){try{return this.contentWrapperEl&&"none"===getComputedStyle(this.contentWrapperEl,"::-webkit-scrollbar").display||"scrollbarWidth"in document.documentElement.style||"-ms-overflow-style"in document.documentElement.style?0:E()}catch(e){return E()}},e.getOffset=function(e){var t=e.getBoundingClientRect(),r=j(e),n=C(e);return{top:t.top+(n.pageYOffset||r.documentElement.scrollTop),left:t.left+(n.pageXOffset||r.documentElement.scrollLeft)}},e.prototype.init=function(){b&&(this.initDOM(),this.rtlHelpers=e.getRtlHelpers(),this.scrollbarWidth=this.getScrollbarWidth(),this.recalculate(),this.initListeners())},e.prototype.initDOM=function(){var e,t;this.wrapperEl=this.el.querySelector(A(this.classNames.wrapper)),this.contentWrapperEl=this.options.scrollableNode||this.el.querySelector(A(this.classNames.contentWrapper)),this.contentEl=this.options.contentNode||this.el.querySelector(A(this.classNames.contentEl)),this.offsetEl=this.el.querySelector(A(this.classNames.offset)),this.maskEl=this.el.querySelector(A(this.classNames.mask)),this.placeholderEl=this.findChild(this.wrapperEl,A(this.classNames.placeholder)),this.heightAutoObserverWrapperEl=this.el.querySelector(A(this.classNames.heightAutoObserverWrapperEl)),this.heightAutoObserverEl=this.el.querySelector(A(this.classNames.heightAutoObserverEl)),this.axis.x.track.el=this.findChild(this.el,"".concat(A(this.classNames.track)).concat(A(this.classNames.horizontal))),this.axis.y.track.el=this.findChild(this.el,"".concat(A(this.classNames.track)).concat(A(this.classNames.vertical))),this.axis.x.scrollbar.el=(null===(e=this.axis.x.track.el)||void 0===e?void 0:e.querySelector(A(this.classNames.scrollbar)))||null,this.axis.y.scrollbar.el=(null===(t=this.axis.y.track.el)||void 0===t?void 0:t.querySelector(A(this.classNames.scrollbar)))||null,this.options.autoHide||(_(this.axis.x.scrollbar.el,this.classNames.visible),_(this.axis.y.scrollbar.el,this.classNames.visible))},e.prototype.initListeners=function(){var e,t=this,r=C(this.el);if(this.el.addEventListener("mouseenter",this.onMouseEnter),this.el.addEventListener("pointerdown",this.onPointerEvent,!0),this.el.addEventListener("mousemove",this.onMouseMove),this.el.addEventListener("mouseleave",this.onMouseLeave),null===(e=this.contentWrapperEl)||void 0===e||e.addEventListener("scroll",this.onScroll),r.addEventListener("resize",this.onWindowResize),this.contentEl){if(window.ResizeObserver){var n=!1,i=r.ResizeObserver||ResizeObserver;this.resizeObserver=new i((function(){n&&r.requestAnimationFrame((function(){t.recalculate()}))})),this.resizeObserver.observe(this.el),this.resizeObserver.observe(this.contentEl),r.requestAnimationFrame((function(){n=!0}))}this.mutationObserver=new r.MutationObserver((function(){r.requestAnimationFrame((function(){t.recalculate()}))})),this.mutationObserver.observe(this.contentEl,{childList:!0,subtree:!0,characterData:!0})}},e.prototype.recalculate=function(){if(this.heightAutoObserverEl&&this.contentEl&&this.contentWrapperEl&&this.wrapperEl&&this.placeholderEl){var e=C(this.el);this.elStyles=e.getComputedStyle(this.el),this.isRtl="rtl"===this.elStyles.direction;var t=this.contentEl.offsetWidth,r=this.heightAutoObserverEl.offsetHeight<=1,n=this.heightAutoObserverEl.offsetWidth<=1||t>0,i=this.contentWrapperEl.offsetWidth,o=this.elStyles.overflowX,a=this.elStyles.overflowY;this.contentEl.style.padding="".concat(this.elStyles.paddingTop," ").concat(this.elStyles.paddingRight," ").concat(this.elStyles.paddingBottom," ").concat(this.elStyles.paddingLeft),this.wrapperEl.style.margin="-".concat(this.elStyles.paddingTop," -").concat(this.elStyles.paddingRight," -").concat(this.elStyles.paddingBottom," -").concat(this.elStyles.paddingLeft);var s=this.contentEl.scrollHeight,l=this.contentEl.scrollWidth;this.contentWrapperEl.style.height=r?"auto":"100%",this.placeholderEl.style.width=n?"".concat(t||l,"px"):"auto",this.placeholderEl.style.height="".concat(s,"px");var c=this.contentWrapperEl.offsetHeight;this.axis.x.isOverflowing=0!==t&&l>t,this.axis.y.isOverflowing=s>c,this.axis.x.isOverflowing="hidden"!==o&&this.axis.x.isOverflowing,this.axis.y.isOverflowing="hidden"!==a&&this.axis.y.isOverflowing,this.axis.x.forceVisible="x"===this.options.forceVisible||!0===this.options.forceVisible,this.axis.y.forceVisible="y"===this.options.forceVisible||!0===this.options.forceVisible,this.hideNativeScrollbar();var u=this.axis.x.isOverflowing?this.scrollbarWidth:0,d=this.axis.y.isOverflowing?this.scrollbarWidth:0;this.axis.x.isOverflowing=this.axis.x.isOverflowing&&l>i-d,this.axis.y.isOverflowing=this.axis.y.isOverflowing&&s>c-u,this.axis.x.scrollbar.size=this.getScrollbarSize("x"),this.axis.y.scrollbar.size=this.getScrollbarSize("y"),this.axis.x.scrollbar.el&&(this.axis.x.scrollbar.el.style.width="".concat(this.axis.x.scrollbar.size,"px")),this.axis.y.scrollbar.el&&(this.axis.y.scrollbar.el.style.height="".concat(this.axis.y.scrollbar.size,"px")),this.positionScrollbar("x"),this.positionScrollbar("y"),this.toggleTrackVisibility("x"),this.toggleTrackVisibility("y")}},e.prototype.getScrollbarSize=function(e){var t,r;if(void 0===e&&(e="y"),!this.axis[e].isOverflowing||!this.contentEl)return 0;var n,i=this.contentEl[this.axis[e].scrollSizeAttr],o=null!==(r=null===(t=this.axis[e].track.el)||void 0===t?void 0:t[this.axis[e].offsetSizeAttr])&&void 0!==r?r:0,a=o/i;return n=Math.max(~~(a*o),this.options.scrollbarMinSize),this.options.scrollbarMaxSize&&(n=Math.min(n,this.options.scrollbarMaxSize)),n},e.prototype.positionScrollbar=function(t){var r,n,i;void 0===t&&(t="y");var o=this.axis[t].scrollbar;if(this.axis[t].isOverflowing&&this.contentWrapperEl&&o.el&&this.elStyles){var a=this.contentWrapperEl[this.axis[t].scrollSizeAttr],s=(null===(r=this.axis[t].track.el)||void 0===r?void 0:r[this.axis[t].offsetSizeAttr])||0,l=parseInt(this.elStyles[this.axis[t].sizeAttr],10),c=this.contentWrapperEl[this.axis[t].scrollOffsetAttr];c="x"===t&&this.isRtl&&(null===(n=e.getRtlHelpers())||void 0===n?void 0:n.isScrollOriginAtZero)?-c:c,"x"===t&&this.isRtl&&(c=(null===(i=e.getRtlHelpers())||void 0===i?void 0:i.isScrollingToNegative)?c:-c);var u=c/(a-l),d=~~((s-o.size)*u);d="x"===t&&this.isRtl?-d+(s-o.size):d,o.el.style.transform="x"===t?"translate3d(".concat(d,"px, 0, 0)"):"translate3d(0, ".concat(d,"px, 0)")}},e.prototype.toggleTrackVisibility=function(e){void 0===e&&(e="y");var t=this.axis[e].track.el,r=this.axis[e].scrollbar.el;t&&r&&this.contentWrapperEl&&(this.axis[e].isOverflowing||this.axis[e].forceVisible?(t.style.visibility="visible",this.contentWrapperEl.style[this.axis[e].overflowAttr]="scroll",this.el.classList.add("".concat(this.classNames.scrollable,"-").concat(e))):(t.style.visibility="hidden",this.contentWrapperEl.style[this.axis[e].overflowAttr]="hidden",this.el.classList.remove("".concat(this.classNames.scrollable,"-").concat(e))),this.axis[e].isOverflowing?r.style.display="block":r.style.display="none")},e.prototype.showScrollbar=function(e){void 0===e&&(e="y"),this.axis[e].isOverflowing&&!this.axis[e].scrollbar.isVisible&&(_(this.axis[e].scrollbar.el,this.classNames.visible),this.axis[e].scrollbar.isVisible=!0)},e.prototype.hideScrollbar=function(e){void 0===e&&(e="y"),this.isDragging||this.axis[e].isOverflowing&&this.axis[e].scrollbar.isVisible&&(S(this.axis[e].scrollbar.el,this.classNames.visible),this.axis[e].scrollbar.isVisible=!1)},e.prototype.hideNativeScrollbar=function(){this.offsetEl&&(this.offsetEl.style[this.isRtl?"left":"right"]=this.axis.y.isOverflowing||this.axis.y.forceVisible?"-".concat(this.scrollbarWidth,"px"):"0px",this.offsetEl.style.bottom=this.axis.x.isOverflowing||this.axis.x.forceVisible?"-".concat(this.scrollbarWidth,"px"):"0px")},e.prototype.onMouseMoveForAxis=function(e){void 0===e&&(e="y");var t=this.axis[e];t.track.el&&t.scrollbar.el&&(t.track.rect=t.track.el.getBoundingClientRect(),t.scrollbar.rect=t.scrollbar.el.getBoundingClientRect(),this.isWithinBounds(t.track.rect)?(this.showScrollbar(e),_(t.track.el,this.classNames.hover),this.isWithinBounds(t.scrollbar.rect)?_(t.scrollbar.el,this.classNames.hover):S(t.scrollbar.el,this.classNames.hover)):(S(t.track.el,this.classNames.hover),this.options.autoHide&&this.hideScrollbar(e)))},e.prototype.onMouseLeaveForAxis=function(e){void 0===e&&(e="y"),S(this.axis[e].track.el,this.classNames.hover),S(this.axis[e].scrollbar.el,this.classNames.hover),this.options.autoHide&&this.hideScrollbar(e)},e.prototype.onDragStart=function(e,t){var r;void 0===t&&(t="y"),this.isDragging=!0;var n=j(this.el),i=C(this.el),o=this.axis[t].scrollbar,a="y"===t?e.pageY:e.pageX;this.axis[t].dragOffset=a-((null===(r=o.rect)||void 0===r?void 0:r[this.axis[t].offsetAttr])||0),this.draggedAxis=t,_(this.el,this.classNames.dragging),n.addEventListener("mousemove",this.drag,!0),n.addEventListener("mouseup",this.onEndDrag,!0),null===this.removePreventClickId?(n.addEventListener("click",this.preventClick,!0),n.addEventListener("dblclick",this.preventClick,!0)):(i.clearTimeout(this.removePreventClickId),this.removePreventClickId=null)},e.prototype.onTrackClick=function(e,t){var r,n,i,o,a=this;void 0===t&&(t="y");var s=this.axis[t];if(this.options.clickOnTrack&&s.scrollbar.el&&this.contentWrapperEl){e.preventDefault();var l=C(this.el);this.axis[t].scrollbar.rect=s.scrollbar.el.getBoundingClientRect();var c=null!==(n=null===(r=this.axis[t].scrollbar.rect)||void 0===r?void 0:r[this.axis[t].offsetAttr])&&void 0!==n?n:0,u=parseInt(null!==(o=null===(i=this.elStyles)||void 0===i?void 0:i[this.axis[t].sizeAttr])&&void 0!==o?o:"0px",10),d=this.contentWrapperEl[this.axis[t].scrollOffsetAttr],h=("y"===t?this.mouseY-c:this.mouseX-c)<0?-1:1,p=-1===h?d-u:d+u,f=function(){a.contentWrapperEl&&(-1===h?d>p&&(d-=40,a.contentWrapperEl[a.axis[t].scrollOffsetAttr]=d,l.requestAnimationFrame(f)):d<p&&(d+=40,a.contentWrapperEl[a.axis[t].scrollOffsetAttr]=d,l.requestAnimationFrame(f)))};f()}},e.prototype.getContentElement=function(){return this.contentEl},e.prototype.getScrollElement=function(){return this.contentWrapperEl},e.prototype.removeListeners=function(){var e=C(this.el);this.el.removeEventListener("mouseenter",this.onMouseEnter),this.el.removeEventListener("pointerdown",this.onPointerEvent,!0),this.el.removeEventListener("mousemove",this.onMouseMove),this.el.removeEventListener("mouseleave",this.onMouseLeave),this.contentWrapperEl&&this.contentWrapperEl.removeEventListener("scroll",this.onScroll),e.removeEventListener("resize",this.onWindowResize),this.mutationObserver&&this.mutationObserver.disconnect(),this.resizeObserver&&this.resizeObserver.disconnect(),this.onMouseMove.cancel(),this.onWindowResize.cancel(),this.onStopScrolling.cancel(),this.onMouseEntered.cancel()},e.prototype.unMount=function(){this.removeListeners()},e.prototype.isWithinBounds=function(e){return this.mouseX>=e.left&&this.mouseX<=e.left+e.width&&this.mouseY>=e.top&&this.mouseY<=e.top+e.height},e.prototype.findChild=function(e,t){var r=e.matches||e.webkitMatchesSelector||e.mozMatchesSelector||e.msMatchesSelector;return Array.prototype.filter.call(e.children,(function(e){return r.call(e,t)}))[0]},e.rtlHelpers=null,e.defaultOptions={forceVisible:!1,clickOnTrack:!0,scrollbarMinSize:25,scrollbarMaxSize:0,ariaLabel:"scrollable content",tabIndex:0,classNames:{contentEl:"simplebar-content",contentWrapper:"simplebar-content-wrapper",offset:"simplebar-offset",mask:"simplebar-mask",wrapper:"simplebar-wrapper",placeholder:"simplebar-placeholder",scrollbar:"simplebar-scrollbar",track:"simplebar-track",heightAutoObserverWrapperEl:"simplebar-height-auto-observer-wrapper",heightAutoObserverEl:"simplebar-height-auto-observer",visible:"simplebar-visible",horizontal:"simplebar-horizontal",vertical:"simplebar-vertical",hover:"simplebar-hover",dragging:"simplebar-dragging",scrolling:"simplebar-scrolling",scrollable:"simplebar-scrollable",mouseEntered:"simplebar-mouse-entered"},scrollableNode:null,contentNode:null,autoHide:!0},e.getOptions=O,e.helpers=w,e}(),M=function(){return M=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},M.apply(this,arguments)},R=n.forwardRef((function(e,t){var r=e.children,i=e.scrollableNodeProps,o=void 0===i?{}:i,a=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(n=Object.getOwnPropertySymbols(e);i<n.length;i++)t.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]])}return r}(e,["children","scrollableNodeProps"]),s=n.useRef(),l=n.useRef(),c=n.useRef(),u={},d={};Object.keys(a).forEach((function(e){Object.prototype.hasOwnProperty.call(P.defaultOptions,e)?u[e]=a[e]:d[e]=a[e]}));var h=M(M({},P.defaultOptions.classNames),u.classNames),p=M(M({},o),{className:"".concat(h.contentWrapper).concat(o.className?" ".concat(o.className):""),tabIndex:u.tabIndex||P.defaultOptions.tabIndex,role:"region","aria-label":u.ariaLabel||P.defaultOptions.ariaLabel});return n.useEffect((function(){var e;return l.current=p.ref?p.ref.current:l.current,s.current&&(e=new P(s.current,M(M(M({},u),l.current&&{scrollableNode:l.current}),c.current&&{contentNode:c.current})),"function"==typeof t?t(e):t&&(t.current=e)),function(){null==e||e.unMount(),e=null,"function"==typeof t&&t(null)}}),[]),n.createElement("div",M({"data-simplebar":"init",ref:s},d),n.createElement("div",{className:h.wrapper},n.createElement("div",{className:h.heightAutoObserverWrapperEl},n.createElement("div",{className:h.heightAutoObserverEl})),n.createElement("div",{className:h.mask},n.createElement("div",{className:h.offset},"function"==typeof r?r({scrollableNodeRef:l,scrollableNodeProps:M(M({},p),{ref:l}),contentNodeRef:c,contentNodeProps:{className:h.contentEl,ref:c}}):n.createElement("div",M({},p),n.createElement("div",{className:h.contentEl},r)))),n.createElement("div",{className:h.placeholder})),n.createElement("div",{className:"".concat(h.track," simplebar-horizontal")},n.createElement("div",{className:h.scrollbar})),n.createElement("div",{className:"".concat(h.track," simplebar-vertical")},n.createElement("div",{className:h.scrollbar})))}));R.displayName="SimpleBar",r(764),r(232);var N,L=e=>{throw TypeError(e)},T=(e,t,r)=>(((e,t)=>{t.has(e)||L("Cannot read from private field")})(e,t),r?r.call(e):t.get(e)),D="popstate";function F(e,t){if(!1===e||null==e)throw new Error(t)}function z(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw new Error(t)}catch(e){}}}function I(e,t){return{usr:e.state,key:e.key,idx:t}}function $(e,t,r=null,n){return{pathname:"string"==typeof e?e:e.pathname,search:"",hash:"",..."string"==typeof t?W(t):t,state:r,key:t&&t.key||n||Math.random().toString(36).substring(2,10)}}function B({pathname:e="/",search:t="",hash:r=""}){return t&&"?"!==t&&(e+="?"===t.charAt(0)?t:"?"+t),r&&"#"!==r&&(e+="#"===r.charAt(0)?r:"#"+r),e}function W(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substring(r),e=e.substring(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substring(n),e=e.substring(0,n)),e&&(t.pathname=e)}return t}var q=class{constructor(e){var t,r,n;if(t=this,r=N,n=new Map,r.has(t)?L("Cannot add the same private member more than once"):r instanceof WeakSet?r.add(t):r.set(t,n),e)for(let[t,r]of e)this.set(t,r)}get(e){if(T(this,N).has(e))return T(this,N).get(e);if(void 0!==e.defaultValue)return e.defaultValue;throw new Error("No value found for context")}set(e,t){T(this,N).set(e,t)}};N=new WeakMap;var H=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","unstable_lazyMiddleware","children"]);function U(e,t,r=[],n={}){return e.map(((e,i)=>{let o=[...r,String(i)],a="string"==typeof e.id?e.id:o.join("-");if(F(!0!==e.index||!e.children,"Cannot specify children on an index route"),F(!n[a],`Found a route id collision on id "${a}".  Route id's must be globally unique within Data Router usages`),function(e){return!0===e.index}(e)){let r={...e,...t(e),id:a};return n[a]=r,r}{let r={...e,...t(e),id:a,children:void 0};return n[a]=r,e.children&&(r.children=U(e.children,t,o,n)),r}}))}function Q(e,t,r="/"){return V(e,t,r,!1)}function V(e,t,r,n){let i=se(("string"==typeof t?W(t):t).pathname||"/",r);if(null==i)return null;let o=Y(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){return e.length===t.length&&e.slice(0,-1).every(((e,r)=>e===t[r]))?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(o);let a=null;for(let e=0;null==a&&e<o.length;++e){let t=ae(i);a=ie(o[e],t,n)}return a}function Y(e,t=[],r=[],n=""){let i=(e,i,o)=>{let a={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:i,route:e};a.relativePath.startsWith("/")&&(F(a.relativePath.startsWith(n),`Absolute route path "${a.relativePath}" nested under path "${n}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),a.relativePath=a.relativePath.slice(n.length));let s=he([n,a.relativePath]),l=r.concat(a);e.children&&e.children.length>0&&(F(!0!==e.index,`Index routes must not have child routes. Please remove all child routes from route path "${s}".`),Y(e.children,t,l,s)),(null!=e.path||e.index)&&t.push({path:s,score:ne(s,e.index),routesMeta:l})};return e.forEach(((e,t)=>{if(""!==e.path&&e.path?.includes("?"))for(let r of K(e.path))i(e,t,r);else i(e,t)})),t}function K(e){let t=e.split("/");if(0===t.length)return[];let[r,...n]=t,i=r.endsWith("?"),o=r.replace(/\?$/,"");if(0===n.length)return i?[o,""]:[o];let a=K(n.join("/")),s=[];return s.push(...a.map((e=>""===e?o:[o,e].join("/")))),i&&s.push(...a),s.map((t=>e.startsWith("/")&&""===t?"/":t))}var G=/^:[\w-]+$/,X=3,J=2,Z=1,ee=10,te=-2,re=e=>"*"===e;function ne(e,t){let r=e.split("/"),n=r.length;return r.some(re)&&(n+=te),t&&(n+=J),r.filter((e=>!re(e))).reduce(((e,t)=>e+(G.test(t)?X:""===t?Z:ee)),n)}function ie(e,t,r=!1){let{routesMeta:n}=e,i={},o="/",a=[];for(let e=0;e<n.length;++e){let s=n[e],l=e===n.length-1,c="/"===o?t:t.slice(o.length)||"/",u=oe({path:s.relativePath,caseSensitive:s.caseSensitive,end:l},c),d=s.route;if(!u&&l&&r&&!n[n.length-1].route.index&&(u=oe({path:s.relativePath,caseSensitive:s.caseSensitive,end:!1},c)),!u)return null;Object.assign(i,u.params),a.push({params:i,pathname:he([o,u.pathname]),pathnameBase:pe(he([o,u.pathnameBase])),route:d}),"/"!==u.pathnameBase&&(o=he([o,u.pathnameBase]))}return a}function oe(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=function(e,t=!1,r=!0){z("*"===e||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let n=[],i="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,r)=>(n.push({paramName:t,isOptional:null!=r}),r?"/?([^\\/]+)?":"/([^\\/]+)")));return e.endsWith("*")?(n.push({paramName:"*"}),i+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?i+="\\/*$":""!==e&&"/"!==e&&(i+="(?:(?=\\/|$))"),[new RegExp(i,t?void 0:"i"),n]}(e.path,e.caseSensitive,e.end),i=t.match(r);if(!i)return null;let o=i[0],a=o.replace(/(.)\/+$/,"$1"),s=i.slice(1);return{params:n.reduce(((e,{paramName:t,isOptional:r},n)=>{if("*"===t){let e=s[n]||"";a=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const i=s[n];return e[t]=r&&!i?void 0:(i||"").replace(/%2F/g,"/"),e}),{}),pathname:o,pathnameBase:a,pattern:e}}function ae(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return z(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function se(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&"/"!==n?null:e.slice(r)||"/"}function le(e,t,r,n){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(n)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function ce(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function ue(e){let t=ce(e);return t.map(((e,r)=>r===t.length-1?e.pathname:e.pathnameBase))}function de(e,t,r,n=!1){let i;"string"==typeof e?i=W(e):(i={...e},F(!i.pathname||!i.pathname.includes("?"),le("?","pathname","search",i)),F(!i.pathname||!i.pathname.includes("#"),le("#","pathname","hash",i)),F(!i.search||!i.search.includes("#"),le("#","search","hash",i)));let o,a=""===e||""===i.pathname,s=a?"/":i.pathname;if(null==s)o=r;else{let e=t.length-1;if(!n&&s.startsWith("..")){let t=s.split("/");for(;".."===t[0];)t.shift(),e-=1;i.pathname=t.join("/")}o=e>=0?t[e]:"/"}let l=function(e,t="/"){let{pathname:r,search:n="",hash:i=""}="string"==typeof e?W(e):e,o=r?r.startsWith("/")?r:function(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?r.length>1&&r.pop():"."!==e&&r.push(e)})),r.length>1?r.join("/"):"/"}(r,t):t;return{pathname:o,search:fe(n),hash:me(i)}}(i,o),c=s&&"/"!==s&&s.endsWith("/"),u=(a||"."===s)&&r.endsWith("/");return l.pathname.endsWith("/")||!c&&!u||(l.pathname+="/"),l}var he=e=>e.join("/").replace(/\/\/+/g,"/"),pe=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),fe=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",me=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"",ge=class{constructor(e,t,r,n=!1){this.status=e,this.statusText=t||"",this.internal=n,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}};function ye(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}var ve=["POST","PUT","PATCH","DELETE"],be=new Set(ve),we=["GET",...ve],xe=new Set(we),ke=new Set([301,302,303,307,308]),Ee=new Set([307,308]),Ce={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},je={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Oe={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},_e=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Se=e=>({hasErrorBoundary:Boolean(e.hasErrorBoundary)}),Ae="remix-router-transitions",Pe=Symbol("ResetLoaderData");function Me(e,t,r,n,i,o){let a,s;if(i){a=[];for(let e of t)if(a.push(e),e.route.id===i){s=e;break}}else a=t,s=t[t.length-1];let l=de(n||".",ue(a),se(e.pathname,r)||e.pathname,"path"===o);if(null==n&&(l.search=e.search,l.hash=e.hash),(null==n||""===n||"."===n)&&s){let e=ut(l.search);if(s.route.index&&!e)l.search=l.search?l.search.replace(/^\?/,"?index&"):"?index";else if(!s.route.index&&e){let e=new URLSearchParams(l.search),t=e.getAll("index");e.delete("index"),t.filter((e=>e)).forEach((t=>e.append("index",t)));let r=e.toString();l.search=r?`?${r}`:""}}return"/"!==r&&(l.pathname="/"===l.pathname?r:he([r,l.pathname])),B(l)}function Re(e,t,r){if(!r||!function(e){return null!=e&&("formData"in e&&null!=e.formData||"body"in e&&void 0!==e.body)}(r))return{path:t};if(r.formMethod&&(n=r.formMethod,!xe.has(n.toUpperCase())))return{path:t,error:tt(405,{method:r.formMethod})};var n;let i,o,a=()=>({path:t,error:tt(400,{type:"invalid-body"})}),s=(r.formMethod||"get").toUpperCase(),l=nt(t);if(void 0!==r.body){if("text/plain"===r.formEncType){if(!ct(s))return a();let e="string"==typeof r.body?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce(((e,[t,r])=>`${e}${t}=${r}\n`),""):String(r.body);return{path:t,submission:{formMethod:s,formAction:l,formEncType:r.formEncType,formData:void 0,json:void 0,text:e}}}if("application/json"===r.formEncType){if(!ct(s))return a();try{let e="string"==typeof r.body?JSON.parse(r.body):r.body;return{path:t,submission:{formMethod:s,formAction:l,formEncType:r.formEncType,formData:void 0,json:e,text:void 0}}}catch(e){return a()}}}if(F("function"==typeof FormData,"FormData is not available in this environment"),r.formData)i=Ye(r.formData),o=r.formData;else if(r.body instanceof FormData)i=Ye(r.body),o=r.body;else if(r.body instanceof URLSearchParams)i=r.body,o=Ke(i);else if(null==r.body)i=new URLSearchParams,o=new FormData;else try{i=new URLSearchParams(r.body),o=Ke(i)}catch(e){return a()}let c={formMethod:s,formAction:l,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:o,json:void 0,text:void 0};if(ct(c.formMethod))return{path:t,submission:c};let u=W(t);return e&&u.search&&ut(u.search)&&i.append("index",""),u.search=`?${i}`,{path:B(u),submission:c}}function Ne(e,t,r=!1){let n=e.findIndex((e=>e.route.id===t));return n>=0?e.slice(0,r?n+1:n):e}function Le(e,t,r,n,i,o,a,s,l,c,u,d,h,p){let f=p?ot(p[1])?p[1].error:p[1].data:void 0,m=e.createURL(t.location),g=e.createURL(i),y=r;o&&t.errors?y=Ne(r,Object.keys(t.errors)[0],!0):p&&ot(p[1])&&(y=Ne(r,p[0]));let v=p?p[1].statusCode:void 0,b=v&&v>=400,w=y.filter(((e,r)=>{let{route:i}=e;if(i.lazy)return!0;if(null==i.loader)return!1;if(o)return Te(i,t.loaderData,t.errors);if(function(e,t,r){let n=!t||r.route.id!==t.route.id,i=!e.hasOwnProperty(r.route.id);return n||i}(t.loaderData,t.matches[r],e))return!0;let s=t.matches[r],l=e;return Fe(e,{currentUrl:m,currentParams:s.params,nextUrl:g,nextParams:l.params,...n,actionResult:f,actionStatus:v,defaultShouldRevalidate:!b&&(a||m.pathname+m.search===g.pathname+g.search||m.search!==g.search||De(s,l))})})),x=[];return c.forEach(((e,i)=>{if(o||!r.some((t=>t.route.id===e.routeId))||l.has(i))return;let c=Q(d,e.path,h);if(!c)return void x.push({key:i,routeId:e.routeId,path:e.path,matches:null,match:null,controller:null});let p=t.fetchers.get(i),y=dt(c,e.path),w=!1;u.has(i)?w=!1:s.has(i)?(s.delete(i),w=!0):w=p&&"idle"!==p.state&&void 0===p.data?a:Fe(y,{currentUrl:m,currentParams:t.matches[t.matches.length-1].params,nextUrl:g,nextParams:r[r.length-1].params,...n,actionResult:f,actionStatus:v,defaultShouldRevalidate:!b&&a}),w&&x.push({key:i,routeId:e.routeId,path:e.path,matches:c,match:y,controller:new AbortController})})),[w,x]}function Te(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let n=null!=t&&void 0!==t[e.id],i=null!=r&&void 0!==r[e.id];return!(!n&&i)&&("function"==typeof e.loader&&!0===e.loader.hydrate||!n&&!i)}function De(e,t){let r=e.route.path;return e.pathname!==t.pathname||null!=r&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function Fe(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if("boolean"==typeof r)return r}return t.defaultShouldRevalidate}function ze(e,t,r,n,i){let o;if(e){let t=n[e];F(t,`No route found to patch children into: routeId = ${e}`),t.children||(t.children=[]),o=t.children}else o=r;let a=U(t.filter((e=>!o.some((t=>Ie(e,t))))),i,[e||"_","patch",String(o?.length||"0")],n);o.push(...a)}function Ie(e,t){return"id"in e&&"id"in t&&e.id===t.id||e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive&&(!(e.children&&0!==e.children.length||t.children&&0!==t.children.length)||e.children.every(((e,r)=>t.children?.some((t=>Ie(e,t))))))}var $e=new WeakMap;async function Be(e){let t=e.matches.filter((e=>e.shouldLoad)),r={};return(await Promise.all(t.map((e=>e.resolve())))).forEach(((e,n)=>{r[t[n].route.id]=e})),r}async function We(e){return e.matches.some((e=>e.route.unstable_middleware))?async function(e,t,r,n){let{matches:i,request:o,params:a,context:s}=e,l={handlerResult:void 0};try{let e=i.flatMap((e=>e.route.unstable_middleware?e.route.unstable_middleware.map((t=>[e.route.id,t])):[])),n=await qe({request:o,params:a,context:s},e,t,l,r);return t?n:l.handlerResult}catch(e){if(!l.middlewareError)throw e;let r=await n(l.middlewareError.error,l.middlewareError.routeId);return t||!l.handlerResult?r:Object.assign(l.handlerResult,r)}}(e,!1,(()=>Be(e)),((e,t)=>({[t]:{type:"error",result:e}}))):Be(e)}async function qe(e,t,r,n,i,o=0){let{request:a}=e;if(a.signal.aborted){if(a.signal.reason)throw a.signal.reason;throw new Error(`Request aborted without an \`AbortSignal.reason\`: ${a.method} ${a.url}`)}let s=t[o];if(!s)return n.handlerResult=await i(),n.handlerResult;let l,[c,u]=s,d=!1,h=async()=>{if(d)throw new Error("You may only call `next()` once per middleware");d=!0;let a=await qe(e,t,r,n,i,o+1);if(r)return l=a,l};try{let t=await u({request:e.request,params:e.params,context:e.context},h);return d?void 0===t?l:t:h()}catch(e){throw n.middlewareError?n.middlewareError.error!==e&&(n.middlewareError={routeId:c,error:e}):n.middlewareError={routeId:c,error:e},e}}async function He(e){let{result:t,type:r}=e;if(lt(t)){let e;try{let r=t.headers.get("Content-Type");e=r&&/\bapplication\/json\b/.test(r)?null==t.body?null:await t.json():await t.text()}catch(e){return{type:"error",error:e}}return"error"===r?{type:"error",error:new ge(t.status,t.statusText,e),statusCode:t.status,headers:t.headers}:{type:"data",data:e,statusCode:t.status,headers:t.headers}}return"error"===r?st(t)?t.data instanceof Error?{type:"error",error:t.data,statusCode:t.init?.status,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"error",error:new ge(t.init?.status||500,void 0,t.data),statusCode:ye(t)?t.status:void 0,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"error",error:t,statusCode:ye(t)?t.status:void 0}:st(t)?{type:"data",data:t.data,statusCode:t.init?.status,headers:t.init?.headers?new Headers(t.init.headers):void 0}:{type:"data",data:t}}function Ue(e,t,r,n,i){let o=e.headers.get("Location");if(F(o,"Redirects returned/thrown from loaders/actions must have a Location header"),!_e.test(o)){let a=n.slice(0,n.findIndex((e=>e.route.id===r))+1);o=Me(new URL(t.url),a,i,o),e.headers.set("Location",o)}return e}function Qe(e,t,r){if(_e.test(e)){let n=e,i=n.startsWith("//")?new URL(t.protocol+n):new URL(n),o=null!=se(i.pathname,r);if(i.origin===t.origin&&o)return i.pathname+i.search+i.hash}return e}function Ve(e,t,r,n){let i=e.createURL(nt(t)).toString(),o={signal:r};if(n&&ct(n.formMethod)){let{formMethod:e,formEncType:t}=n;o.method=e.toUpperCase(),"application/json"===t?(o.headers=new Headers({"Content-Type":t}),o.body=JSON.stringify(n.json)):"text/plain"===t?o.body=n.text:"application/x-www-form-urlencoded"===t&&n.formData?o.body=Ye(n.formData):o.body=n.formData}return new Request(i,o)}function Ye(e){let t=new URLSearchParams;for(let[r,n]of e.entries())t.append(r,"string"==typeof n?n:n.name);return t}function Ke(e){let t=new FormData;for(let[r,n]of e.entries())t.append(r,n);return t}function Ge(e,t,r,n,i,o){let{loaderData:a,errors:s}=function(e,t,r,n=!1,i=!1){let o,a={},s=null,l=!1,c={},u=r&&ot(r[1])?r[1].error:void 0;return e.forEach((r=>{if(!(r.route.id in t))return;let d=r.route.id,h=t[d];if(F(!at(h),"Cannot handle redirect results in processLoaderData"),ot(h)){let t=h.error;if(void 0!==u&&(t=u,u=void 0),s=s||{},i)s[d]=t;else{let r=Ze(e,d);null==s[r.route.id]&&(s[r.route.id]=t)}n||(a[d]=Pe),l||(l=!0,o=ye(h.error)?h.error.status:500),h.headers&&(c[d]=h.headers)}else a[d]=h.data,h.statusCode&&200!==h.statusCode&&!l&&(o=h.statusCode),h.headers&&(c[d]=h.headers)})),void 0!==u&&r&&(s={[r[0]]:u},a[r[0]]=void 0),{loaderData:a,errors:s,statusCode:o||200,loaderHeaders:c}}(t,r,n);return i.forEach((t=>{let{key:r,match:n,controller:i}=t,a=o[r];if(F(a,"Did not find corresponding fetcher result"),!i||!i.signal.aborted)if(ot(a)){let t=Ze(e.matches,n?.route.id);s&&s[t.route.id]||(s={...s,[t.route.id]:a.error}),e.fetchers.delete(r)}else if(at(a))F(!1,"Unhandled fetcher revalidation redirect");else{let t=mt(a.data);e.fetchers.set(r,t)}})),{loaderData:a,errors:s}}function Xe(e,t,r,n){let i=Object.entries(t).filter((([,e])=>e!==Pe)).reduce(((e,[t,r])=>(e[t]=r,e)),{});for(let o of r){let r=o.route.id;if(!t.hasOwnProperty(r)&&e.hasOwnProperty(r)&&o.route.loader&&(i[r]=e[r]),n&&n.hasOwnProperty(r))break}return i}function Je(e){return e?ot(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function Ze(e,t){return(t?e.slice(0,e.findIndex((e=>e.route.id===t))+1):[...e]).reverse().find((e=>!0===e.route.hasErrorBoundary))||e[0]}function et(e){let t=1===e.length?e[0]:e.find((e=>e.index||!e.path||"/"===e.path))||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function tt(e,{pathname:t,routeId:r,method:n,type:i,message:o}={}){let a="Unknown Server Error",s="Unknown @remix-run/router error";return 400===e?(a="Bad Request",n&&t&&r?s=`You made a ${n} request to "${t}" but did not provide a \`loader\` for route "${r}", so there is no way to handle the request.`:"invalid-body"===i&&(s="Unable to encode submission body")):403===e?(a="Forbidden",s=`Route "${r}" does not match URL "${t}"`):404===e?(a="Not Found",s=`No route matches URL "${t}"`):405===e&&(a="Method Not Allowed",n&&t&&r?s=`You made a ${n.toUpperCase()} request to "${t}" but did not provide an \`action\` for route "${r}", so there is no way to handle the request.`:n&&(s=`Invalid request method "${n.toUpperCase()}"`)),new ge(e||500,a,new Error(s),!0)}function rt(e){let t=Object.entries(e);for(let e=t.length-1;e>=0;e--){let[r,n]=t[e];if(at(n))return{key:r,result:n}}}function nt(e){return B({..."string"==typeof e?W(e):e,hash:""})}function it(e){return lt(e.result)&&ke.has(e.result.status)}function ot(e){return"error"===e.type}function at(e){return"redirect"===(e&&e.type)}function st(e){return"object"==typeof e&&null!=e&&"type"in e&&"data"in e&&"init"in e&&"DataWithResponseInit"===e.type}function lt(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"object"==typeof e.headers&&void 0!==e.body}function ct(e){return be.has(e.toUpperCase())}function ut(e){return new URLSearchParams(e).getAll("index").some((e=>""===e))}function dt(e,t){let r="string"==typeof t?W(t).search:t.search;if(e[e.length-1].route.index&&ut(r||""))return e[e.length-1];let n=ce(e);return n[n.length-1]}function ht(e){let{formMethod:t,formAction:r,formEncType:n,text:i,formData:o,json:a}=e;if(t&&r&&n)return null!=i?{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:void 0,text:i}:null!=o?{formMethod:t,formAction:r,formEncType:n,formData:o,json:void 0,text:void 0}:void 0!==a?{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:a,text:void 0}:void 0}function pt(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function ft(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function mt(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}var gt=n.createContext(null);gt.displayName="DataRouter";var yt=n.createContext(null);yt.displayName="DataRouterState";var vt=n.createContext({isTransitioning:!1});vt.displayName="ViewTransition";var bt=n.createContext(new Map);bt.displayName="Fetchers",n.createContext(null).displayName="Await";var wt=n.createContext(null);wt.displayName="Navigation";var xt=n.createContext(null);xt.displayName="Location";var kt=n.createContext({outlet:null,matches:[],isDataRoute:!1});kt.displayName="Route";var Et=n.createContext(null);function Ct(){return null!=n.useContext(xt)}function jt(){return F(Ct(),"useLocation() may be used only in the context of a <Router> component."),n.useContext(xt).location}Et.displayName="RouteError";var Ot="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function _t(e){n.useContext(wt).static||n.useLayoutEffect(e)}function St(){let{isDataRoute:e}=n.useContext(kt);return e?function(){let{router:e}=function(e){let t=n.useContext(gt);return F(t,Tt(e)),t}("useNavigate"),t=Dt("useNavigate"),r=n.useRef(!1);return _t((()=>{r.current=!0})),n.useCallback((async(n,i={})=>{z(r.current,Ot),r.current&&("number"==typeof n?e.navigate(n):await e.navigate(n,{fromRouteId:t,...i}))}),[e,t])}():function(){F(Ct(),"useNavigate() may be used only in the context of a <Router> component.");let e=n.useContext(gt),{basename:t,navigator:r}=n.useContext(wt),{matches:i}=n.useContext(kt),{pathname:o}=jt(),a=JSON.stringify(ue(i)),s=n.useRef(!1);return _t((()=>{s.current=!0})),n.useCallback(((n,i={})=>{if(z(s.current,Ot),!s.current)return;if("number"==typeof n)return void r.go(n);let l=de(n,JSON.parse(a),o,"path"===i.relative);null==e&&"/"!==t&&(l.pathname="/"===l.pathname?t:he([t,l.pathname])),(i.replace?r.replace:r.push)(l,i.state,i)}),[t,r,a,o,e])}()}function At(e,{relative:t}={}){let{matches:r}=n.useContext(kt),{pathname:i}=jt(),o=JSON.stringify(ue(r));return n.useMemo((()=>de(e,JSON.parse(o),i,"path"===t)),[e,o,i,t])}function Pt(e,t,r,i){F(Ct(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:o,static:a}=n.useContext(wt),{matches:s}=n.useContext(kt),l=s[s.length-1],c=l?l.params:{},u=l?l.pathname:"/",d=l?l.pathnameBase:"/",h=l&&l.route;{let e=h&&h.path||"";zt(u,!h||e.endsWith("*")||e.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${u}" (under <Route path="${e}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="${e}"> to <Route path="${"/"===e?"*":`${e}/*`}">.`)}let p,f=jt();if(t){let e="string"==typeof t?W(t):t;F("/"===d||e.pathname?.startsWith(d),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${d}" but pathname "${e.pathname}" was given in the \`location\` prop.`),p=e}else p=f;let m=p.pathname||"/",g=m;if("/"!==d){let e=d.replace(/^\//,"").split("/");g="/"+m.replace(/^\//,"").split("/").slice(e.length).join("/")}let y=!a&&r&&r.matches&&r.matches.length>0?r.matches:Q(e,{pathname:g});z(h||null!=y,`No routes matched location "${p.pathname}${p.search}${p.hash}" `),z(null==y||void 0!==y[y.length-1].route.element||void 0!==y[y.length-1].route.Component||void 0!==y[y.length-1].route.lazy,`Matched leaf route at location "${p.pathname}${p.search}${p.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let v=function(e,t=[],r=null){if(null==e){if(!r)return null;if(r.errors)e=r.matches;else{if(0!==t.length||r.initialized||!(r.matches.length>0))return null;e=r.matches}}let i=e,o=r?.errors;if(null!=o){let e=i.findIndex((e=>e.route.id&&void 0!==o?.[e.route.id]));F(e>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),i=i.slice(0,Math.min(i.length,e+1))}let a=!1,s=-1;if(r)for(let e=0;e<i.length;e++){let t=i[e];if((t.route.HydrateFallback||t.route.hydrateFallbackElement)&&(s=e),t.route.id){let{loaderData:e,errors:n}=r,o=t.route.loader&&!e.hasOwnProperty(t.route.id)&&(!n||void 0===n[t.route.id]);if(t.route.lazy||o){a=!0,i=s>=0?i.slice(0,s+1):[i[0]];break}}}return i.reduceRight(((e,l,c)=>{let u,d=!1,h=null,p=null;r&&(u=o&&l.route.id?o[l.route.id]:void 0,h=l.route.errorElement||Rt,a&&(s<0&&0===c?(zt("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),d=!0,p=null):s===c&&(d=!0,p=l.route.hydrateFallbackElement||null)));let f=t.concat(i.slice(0,c+1)),m=()=>{let t;return t=u?h:d?p:l.route.Component?n.createElement(l.route.Component,null):l.route.element?l.route.element:e,n.createElement(Lt,{match:l,routeContext:{outlet:e,matches:f,isDataRoute:null!=r},children:t})};return r&&(l.route.ErrorBoundary||l.route.errorElement||0===c)?n.createElement(Nt,{location:r.location,revalidation:r.revalidation,component:h,error:u,children:m(),routeContext:{outlet:null,matches:f,isDataRoute:!0}}):m()}),null)}(y&&y.map((e=>Object.assign({},e,{params:Object.assign({},c,e.params),pathname:he([d,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?d:he([d,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),s,r,i);return t&&v?n.createElement(xt.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...p},navigationType:"POP"}},v):v}function Mt(){let e=function(){let e=n.useContext(Et),t=function(e){let t=n.useContext(yt);return F(t,Tt(e)),t}("useRouteError"),r=Dt("useRouteError");return void 0!==e?e:t.errors?.[r]}(),t=ye(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,i="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:i},a={padding:"2px 4px",backgroundColor:i},s=null;return console.error("Error handled by React Router default ErrorBoundary:",e),s=n.createElement(n.Fragment,null,n.createElement("p",null,"💿 Hey developer 👋"),n.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",n.createElement("code",{style:a},"ErrorBoundary")," or"," ",n.createElement("code",{style:a},"errorElement")," prop on your route.")),n.createElement(n.Fragment,null,n.createElement("h2",null,"Unexpected Application Error!"),n.createElement("h3",{style:{fontStyle:"italic"}},t),r?n.createElement("pre",{style:o},r):null,s)}n.createContext(null);var Rt=n.createElement(Mt,null),Nt=class extends n.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?n.createElement(kt.Provider,{value:this.props.routeContext},n.createElement(Et.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Lt({routeContext:e,match:t,children:r}){let i=n.useContext(gt);return i&&i.static&&i.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=t.route.id),n.createElement(kt.Provider,{value:e},r)}function Tt(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Dt(e){let t=function(e){let t=n.useContext(kt);return F(t,Tt(e)),t}(e),r=t.matches[t.matches.length-1];return F(r.route.id,`${e} can only be used on routes that contain a unique "id"`),r.route.id}var Ft={};function zt(e,t,r){t||Ft[e]||(Ft[e]=!0,z(!1,r))}var It={};function $t(e,t){e||It[t]||(It[t]=!0,console.warn(t))}var Bt=class{constructor(){this.status="pending",this.promise=new Promise(((e,t)=>{this.resolve=t=>{"pending"===this.status&&(this.status="resolved",e(t))},this.reject=e=>{"pending"===this.status&&(this.status="rejected",t(e))}}))}};function Wt({router:e,flushSync:t}){let[r,i]=n.useState(e.state),[o,a]=n.useState(),[s,l]=n.useState({isTransitioning:!1}),[c,u]=n.useState(),[d,h]=n.useState(),[p,f]=n.useState(),m=n.useRef(new Map),g=n.useCallback(((r,{deletedFetchers:o,flushSync:s,viewTransitionOpts:p})=>{r.fetchers.forEach(((e,t)=>{void 0!==e.data&&m.current.set(t,e.data)})),o.forEach((e=>m.current.delete(e))),$t(!1===s||null!=t,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let g=null!=e.window&&null!=e.window.document&&"function"==typeof e.window.document.startViewTransition;if($t(null==p||g,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),p&&g){if(t&&s){t((()=>{d&&(c&&c.resolve(),d.skipTransition()),l({isTransitioning:!0,flushSync:!0,currentLocation:p.currentLocation,nextLocation:p.nextLocation})}));let n=e.window.document.startViewTransition((()=>{t((()=>i(r)))}));return n.finished.finally((()=>{t((()=>{u(void 0),h(void 0),a(void 0),l({isTransitioning:!1})}))})),void t((()=>h(n)))}d?(c&&c.resolve(),d.skipTransition(),f({state:r,currentLocation:p.currentLocation,nextLocation:p.nextLocation})):(a(r),l({isTransitioning:!0,flushSync:!1,currentLocation:p.currentLocation,nextLocation:p.nextLocation}))}else t&&s?t((()=>i(r))):n.startTransition((()=>i(r)))}),[e.window,t,d,c]);n.useLayoutEffect((()=>e.subscribe(g)),[e,g]),n.useEffect((()=>{s.isTransitioning&&!s.flushSync&&u(new Bt)}),[s]),n.useEffect((()=>{if(c&&o&&e.window){let t=o,r=c.promise,s=e.window.document.startViewTransition((async()=>{n.startTransition((()=>i(t))),await r}));s.finished.finally((()=>{u(void 0),h(void 0),a(void 0),l({isTransitioning:!1})})),h(s)}}),[o,c,e.window]),n.useEffect((()=>{c&&o&&r.location.key===o.location.key&&c.resolve()}),[c,d,r.location,o]),n.useEffect((()=>{!s.isTransitioning&&p&&(a(p.state),l({isTransitioning:!0,flushSync:!1,currentLocation:p.currentLocation,nextLocation:p.nextLocation}),f(void 0))}),[s.isTransitioning,p]);let y=n.useMemo((()=>({createHref:e.createHref,encodeLocation:e.encodeLocation,go:t=>e.navigate(t),push:(t,r,n)=>e.navigate(t,{state:r,preventScrollReset:n?.preventScrollReset}),replace:(t,r,n)=>e.navigate(t,{replace:!0,state:r,preventScrollReset:n?.preventScrollReset})})),[e]),v=e.basename||"/",b=n.useMemo((()=>({router:e,navigator:y,static:!1,basename:v})),[e,y,v]);return n.createElement(n.Fragment,null,n.createElement(gt.Provider,{value:b},n.createElement(yt.Provider,{value:r},n.createElement(bt.Provider,{value:m.current},n.createElement(vt.Provider,{value:s},n.createElement(Ut,{basename:v,location:r.location,navigationType:r.historyAction,navigator:y},n.createElement(qt,{routes:e.routes,future:e.future,state:r})))))),null)}var qt=n.memo((function({routes:e,future:t,state:r}){return Pt(e,void 0,r,t)}));function Ht(e){F(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Ut({basename:e="/",children:t=null,location:r,navigationType:i="POP",navigator:o,static:a=!1}){F(!Ct(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let s=e.replace(/^\/*/,"/"),l=n.useMemo((()=>({basename:s,navigator:o,static:a,future:{}})),[s,o,a]);"string"==typeof r&&(r=W(r));let{pathname:c="/",search:u="",hash:d="",state:h=null,key:p="default"}=r,f=n.useMemo((()=>{let e=se(c,s);return null==e?null:{location:{pathname:e,search:u,hash:d,state:h,key:p},navigationType:i}}),[s,c,u,d,h,p,i]);return z(null!=f,`<Router basename="${s}"> is not able to match the URL "${c}${u}${d}" because it does not start with the basename, so the <Router> won't render anything.`),null==f?null:n.createElement(wt.Provider,{value:l},n.createElement(xt.Provider,{children:t,value:f}))}function Qt({children:e,location:t}){return Pt(Vt(e),t)}function Vt(e,t=[]){let r=[];return n.Children.forEach(e,((e,i)=>{if(!n.isValidElement(e))return;let o=[...t,i];if(e.type===n.Fragment)return void r.push.apply(r,Vt(e.props.children,o));F(e.type===Ht,`[${"string"==typeof e.type?e.type:e.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),F(!e.props.index||!e.props.children,"An index route cannot have child routes.");let a={id:e.props.id||o.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,hydrateFallbackElement:e.props.hydrateFallbackElement,HydrateFallback:e.props.HydrateFallback,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:!0===e.props.hasErrorBoundary||null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(a.children=Vt(e.props.children,o)),r.push(a)})),r}n.Component;var Yt="get",Kt="application/x-www-form-urlencoded";function Gt(e){return null!=e&&"string"==typeof e.tagName}var Xt=null,Jt=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Zt(e){return null==e||Jt.has(e)?e:(z(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Kt}"`),null)}function er(e,t){if(!1===e||null==e)throw new Error(t)}function tr(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"==typeof e.imageSrcSet&&"string"==typeof e.imageSizes:"string"==typeof e.rel&&"string"==typeof e.href)}function rr(e,t,r,n,i,o){let a=(e,t)=>!r[t]||e.route.id!==r[t].route.id,s=(e,t)=>r[t].pathname!==e.pathname||r[t].route.path?.endsWith("*")&&r[t].params["*"]!==e.params["*"];return"assets"===o?t.filter(((e,t)=>a(e,t)||s(e,t))):"data"===o?t.filter(((t,o)=>{let l=n.routes[t.route.id];if(!l||!l.hasLoader)return!1;if(a(t,o)||s(t,o))return!0;if(t.route.shouldRevalidate){let n=t.route.shouldRevalidate({currentUrl:new URL(i.pathname+i.search+i.hash,window.origin),currentParams:r[0]?.params||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"==typeof n)return n}return!0})):[]}function nr(e,t,{includeHydrateFallback:r}={}){return n=e.map((e=>{let n=t.routes[e.route.id];if(!n)return[];let i=[n.module];return n.clientActionModule&&(i=i.concat(n.clientActionModule)),n.clientLoaderModule&&(i=i.concat(n.clientLoaderModule)),r&&n.hydrateFallbackModule&&(i=i.concat(n.hydrateFallbackModule)),n.imports&&(i=i.concat(n.imports)),i})).flat(1),[...new Set(n)];var n}function ir(){let e=n.useContext(gt);return er(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function or(){let e=n.useContext(yt);return er(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}Symbol("SingleFetchRedirect"),n.Component;var ar=n.createContext(void 0);function sr(){let e=n.useContext(ar);return er(e,"You must render this element inside a <HydratedRouter> element"),e}function lr(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}function cr({page:e,...t}){let{router:r}=ir(),i=n.useMemo((()=>Q(r.routes,e,r.basename)),[r.routes,e,r.basename]);return i?n.createElement(dr,{page:e,matches:i,...t}):null}function ur(e){let{manifest:t,routeModules:r}=sr(),[i,o]=n.useState([]);return n.useEffect((()=>{let n=!1;return async function(e,t,r){let n=await Promise.all(e.map((async e=>{let n=t.routes[e.route.id];if(n){let e=await async function(e,t){if(e.id in t)return t[e.id];try{let r=await import(e.module);return t[e.id]=r,r}catch(t){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(t),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise((()=>{}))}}(n,r);return e.links?e.links():[]}return[]})));return function(e,t){let r=new Set,n=new Set(t);return e.reduce(((e,i)=>{if(t&&(null==(o=i)||"string"!=typeof o.page)&&"script"===i.as&&i.href&&n.has(i.href))return e;var o;let a=JSON.stringify(function(e){let t={},r=Object.keys(e).sort();for(let n of r)t[n]=e[n];return t}(i));return r.has(a)||(r.add(a),e.push({key:a,link:i})),e}),[])}(n.flat(1).filter(tr).filter((e=>"stylesheet"===e.rel||"preload"===e.rel)).map((e=>"stylesheet"===e.rel?{...e,rel:"prefetch",as:"style"}:{...e,rel:"prefetch"})))}(e,t,r).then((e=>{n||o(e)})),()=>{n=!0}}),[e,t,r]),i}function dr({page:e,matches:t,...r}){let i=jt(),{manifest:o,routeModules:a}=sr(),{basename:s}=ir(),{loaderData:l,matches:c}=or(),u=n.useMemo((()=>rr(e,t,c,o,i,"data")),[e,t,c,o,i]),d=n.useMemo((()=>rr(e,t,c,o,i,"assets")),[e,t,c,o,i]),h=n.useMemo((()=>{if(e===i.pathname+i.search+i.hash)return[];let r=new Set,n=!1;if(t.forEach((e=>{let t=o.routes[e.route.id];t&&t.hasLoader&&(!u.some((t=>t.route.id===e.route.id))&&e.route.id in l&&a[e.route.id]?.shouldRevalidate||t.hasClientLoader?n=!0:r.add(e.route.id))})),0===r.size)return[];let c=function(e,t){let r="string"==typeof e?new URL(e,"undefined"==typeof window?"server://singlefetch/":window.location.origin):e;return"/"===r.pathname?r.pathname="_root.data":t&&"/"===se(r.pathname,t)?r.pathname=`${t.replace(/\/$/,"")}/_root.data`:r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}(e,s);return n&&r.size>0&&c.searchParams.set("_routes",t.filter((e=>r.has(e.route.id))).map((e=>e.route.id)).join(",")),[c.pathname+c.search]}),[s,l,i,o,u,t,e,a]),p=n.useMemo((()=>nr(d,o)),[d,o]),f=ur(d);return n.createElement(n.Fragment,null,h.map((e=>n.createElement("link",{key:e,rel:"prefetch",as:"fetch",href:e,...r}))),p.map((e=>n.createElement("link",{key:e,rel:"modulepreload",href:e,...r}))),f.map((({key:e,link:t})=>n.createElement("link",{key:e,...t}))))}ar.displayName="FrameworkContext";function hr(...e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}var pr="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;try{pr&&(window.__reactRouterVersion="7.4.1")}catch(e){}function fr(e){if(!e)return null;let t=Object.entries(e),r={};for(let[e,n]of t)if(n&&"RouteErrorResponse"===n.__type)r[e]=new ge(n.status,n.statusText,n.data,!0===n.internal);else if(n&&"Error"===n.__type){if(n.__subType){let t=window[n.__subType];if("function"==typeof t)try{let i=new t(n.message);i.stack="",r[e]=i}catch(e){}}if(null==r[e]){let t=new Error(n.message);t.stack="",r[e]=t}}else r[e]=n;return r}var mr=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,gr=n.forwardRef((function({onClick:e,discover:t="render",prefetch:r="none",relative:i,reloadDocument:o,replace:a,state:s,target:l,to:c,preventScrollReset:u,viewTransition:d,...h},p){let f,{basename:m}=n.useContext(wt),g="string"==typeof c&&mr.test(c),y=!1;if("string"==typeof c&&g&&(f=c,pr))try{let e=new URL(window.location.href),t=c.startsWith("//")?new URL(e.protocol+c):new URL(c),r=se(t.pathname,m);t.origin===e.origin&&null!=r?c=r+t.search+t.hash:y=!0}catch(e){z(!1,`<Link to="${c}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let v=function(e,{relative:t}={}){F(Ct(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:i}=n.useContext(wt),{hash:o,pathname:a,search:s}=At(e,{relative:t}),l=a;return"/"!==r&&(l="/"===a?r:he([r,a])),i.createHref({pathname:l,search:s,hash:o})}(c,{relative:i}),[b,w,x]=function(e,t){let r=n.useContext(ar),[i,o]=n.useState(!1),[a,s]=n.useState(!1),{onFocus:l,onBlur:c,onMouseEnter:u,onMouseLeave:d,onTouchStart:h}=t,p=n.useRef(null);n.useEffect((()=>{if("render"===e&&s(!0),"viewport"===e){let e=new IntersectionObserver((e=>{e.forEach((e=>{s(e.isIntersecting)}))}),{threshold:.5});return p.current&&e.observe(p.current),()=>{e.disconnect()}}}),[e]),n.useEffect((()=>{if(i){let e=setTimeout((()=>{s(!0)}),100);return()=>{clearTimeout(e)}}}),[i]);let f=()=>{o(!0)},m=()=>{o(!1),s(!1)};return r?"intent"!==e?[a,p,{}]:[a,p,{onFocus:lr(l,f),onBlur:lr(c,m),onMouseEnter:lr(u,f),onMouseLeave:lr(d,m),onTouchStart:lr(h,f)}]:[!1,p,{}]}(r,h),k=function(e,{target:t,replace:r,state:i,preventScrollReset:o,relative:a,viewTransition:s}={}){let l=St(),c=jt(),u=At(e,{relative:a});return n.useCallback((n=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(n,t)){n.preventDefault();let t=void 0!==r?r:B(c)===B(u);l(e,{replace:t,state:i,preventScrollReset:o,relative:a,viewTransition:s})}}),[c,l,u,r,i,t,e,o,a,s])}(c,{replace:a,state:s,target:l,preventScrollReset:u,relative:i,viewTransition:d}),E=n.createElement("a",{...h,...x,href:f||v,onClick:y||o?e:function(t){e&&e(t),t.defaultPrevented||k(t)},ref:hr(p,w),target:l,"data-discover":g||"render"!==t?void 0:"true"});return b&&!g?n.createElement(n.Fragment,null,E,n.createElement(cr,{page:v})):E}));gr.displayName="Link";var yr=n.forwardRef((function({"aria-current":e="page",caseSensitive:t=!1,className:r="",end:i=!1,style:o,to:a,viewTransition:s,children:l,...c},u){let d=At(a,{relative:c.relative}),h=jt(),p=n.useContext(yt),{navigator:f,basename:m}=n.useContext(wt),g=null!=p&&function(e,t={}){let r=n.useContext(vt);F(null!=r,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:i}=br("useViewTransitionState"),o=At(e,{relative:t.relative});if(!r.isTransitioning)return!1;let a=se(r.currentLocation.pathname,i)||r.currentLocation.pathname,s=se(r.nextLocation.pathname,i)||r.nextLocation.pathname;return null!=oe(o.pathname,s)||null!=oe(o.pathname,a)}(d)&&!0===s,y=f.encodeLocation?f.encodeLocation(d).pathname:d.pathname,v=h.pathname,b=p&&p.navigation&&p.navigation.location?p.navigation.location.pathname:null;t||(v=v.toLowerCase(),b=b?b.toLowerCase():null,y=y.toLowerCase()),b&&m&&(b=se(b,m)||b);const w="/"!==y&&y.endsWith("/")?y.length-1:y.length;let x,k=v===y||!i&&v.startsWith(y)&&"/"===v.charAt(w),E=null!=b&&(b===y||!i&&b.startsWith(y)&&"/"===b.charAt(y.length)),C={isActive:k,isPending:E,isTransitioning:g},j=k?e:void 0;x="function"==typeof r?r(C):[r,k?"active":null,E?"pending":null,g?"transitioning":null].filter(Boolean).join(" ");let O="function"==typeof o?o(C):o;return n.createElement(gr,{...c,"aria-current":j,className:x,ref:u,style:O,to:a,viewTransition:s},"function"==typeof l?l(C):l)}));yr.displayName="NavLink";var vr=n.forwardRef((({discover:e="render",fetcherKey:t,navigate:r,reloadDocument:i,replace:o,state:a,method:s=Yt,action:l,onSubmit:c,relative:u,preventScrollReset:d,viewTransition:h,...p},f)=>{let m=function(){let{router:e}=br("useSubmit"),{basename:t}=n.useContext(wt),r=Dt("useRouteId");return n.useCallback((async(n,i={})=>{let{action:o,method:a,encType:s,formData:l,body:c}=function(e,t){let r,n,i,o,a;if(Gt(s=e)&&"form"===s.tagName.toLowerCase()){let a=e.getAttribute("action");n=a?se(a,t):null,r=e.getAttribute("method")||Yt,i=Zt(e.getAttribute("enctype"))||Kt,o=new FormData(e)}else if(function(e){return Gt(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return Gt(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let a=e.form;if(null==a)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let s=e.getAttribute("formaction")||a.getAttribute("action");if(n=s?se(s,t):null,r=e.getAttribute("formmethod")||a.getAttribute("method")||Yt,i=Zt(e.getAttribute("formenctype"))||Zt(a.getAttribute("enctype"))||Kt,o=new FormData(a,e),!function(){if(null===Xt)try{new FormData(document.createElement("form"),0),Xt=!1}catch(e){Xt=!0}return Xt}()){let{name:t,type:r,value:n}=e;if("image"===r){let e=t?`${t}.`:"";o.append(`${e}x`,"0"),o.append(`${e}y`,"0")}else t&&o.append(t,n)}}else{if(Gt(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=Yt,n=null,i=Kt,a=e}var s;return o&&"text/plain"===i&&(a=o,o=void 0),{action:n,method:r.toLowerCase(),encType:i,formData:o,body:a}}(n,t);if(!1===i.navigate){let t=i.fetcherKey||xr();await e.fetch(t,r,i.action||o,{preventScrollReset:i.preventScrollReset,formData:l,body:c,formMethod:i.method||a,formEncType:i.encType||s,flushSync:i.flushSync})}else await e.navigate(i.action||o,{preventScrollReset:i.preventScrollReset,formData:l,body:c,formMethod:i.method||a,formEncType:i.encType||s,replace:i.replace,state:i.state,fromRouteId:r,flushSync:i.flushSync,viewTransition:i.viewTransition})}),[e,t,r])}(),g=function(e,{relative:t}={}){let{basename:r}=n.useContext(wt),i=n.useContext(kt);F(i,"useFormAction must be used inside a RouteContext");let[o]=i.matches.slice(-1),a={...At(e||".",{relative:t})},s=jt();if(null==e){a.search=s.search;let e=new URLSearchParams(a.search),t=e.getAll("index");if(t.some((e=>""===e))){e.delete("index"),t.filter((e=>e)).forEach((t=>e.append("index",t)));let r=e.toString();a.search=r?`?${r}`:""}}return e&&"."!==e||!o.route.index||(a.search=a.search?a.search.replace(/^\?/,"?index&"):"?index"),"/"!==r&&(a.pathname="/"===a.pathname?r:he([r,a.pathname])),B(a)}(l,{relative:u}),y="get"===s.toLowerCase()?"get":"post",v="string"==typeof l&&mr.test(l);return n.createElement("form",{ref:f,method:y,action:g,onSubmit:i?c:e=>{if(c&&c(e),e.defaultPrevented)return;e.preventDefault();let n=e.nativeEvent.submitter,i=n?.getAttribute("formmethod")||s;m(n||e.currentTarget,{fetcherKey:t,method:i,navigate:r,replace:o,state:a,relative:u,preventScrollReset:d,viewTransition:h})},...p,"data-discover":v||"render"!==e?void 0:"true"})}));function br(e){let t=n.useContext(gt);return F(t,function(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}(e)),t}vr.displayName="Form";var wr=0,xr=()=>`__${String(++wr)}__`;new TextEncoder;const kr=({condition:e,wrapper:t,children:r})=>e?t(r):r,Er="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function Cr(e){return(e=>!!e&&"object"==typeof e)(e)&&!(e=>{const t=Object.prototype.toString.call(e);return"[object RegExp]"===t||"[object Date]"===t||(e=>e.$$typeof===Er)(e)})(e)}function jr(e,t){return!1!==t.clone&&t.isMergeableObject(e)?Pr((r=e,Array.isArray(r)?[]:{}),e,t):e;var r}function Or(e,t,r){return e.concat(t).map((function(e){return jr(e,r)}))}function _r(e,t,r){const n=e.slice();return t.forEach(((t,i)=>{void 0===n[i]?n[i]=r.cloneUnlessOtherwiseSpecified(t,r):r.isMergeableObject(t)?n[i]=Pr(e[i],t,r):-1===e.indexOf(t)&&n.push(t)})),n}function Sr(e){return Object.keys(e).concat(function(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter((function(t){return e.propertyIsEnumerable(t)})):[]}(e))}function Ar(e,t){try{return t in e}catch(e){return!1}}function Pr(e,t,r={}){r.arrayMerge=function(e){let t=Or;var r;return"combine"===e.arrayMerge?t=_r:(r=e.arrayMerge)&&"[object Function]"==={}.toString.call(r)&&(t=e.arrayMerge),t}(r),r.isMergeableObject=r.isMergeableObject||Cr,r.cloneUnlessOtherwiseSpecified=jr;const n=Array.isArray(t);return n===Array.isArray(e)?n?r.arrayMerge(e,t,r):function(e,t,r){const n={};return r.isMergeableObject(e)&&Sr(e).forEach((function(t){n[t]=jr(e[t],r)})),Sr(t).forEach((function(i){(function(e,t){return Ar(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))})(e,i)||(Ar(e,i)&&r.isMergeableObject(t[i])?n[i]=function(e,t){if(!t.customMerge)return Pr;const r=t.customMerge(e);return"function"==typeof r?r:Pr}(i,r)(e[i],t[i],r):n[i]=jr(t[i],r))})),n}(e,t,r):jr(t,r)}Pr.all=function(e,t){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce((function(e,r){return Pr(e,r,t)}),{})};const Mr=(e,t,r,n)=>{if("length"===r||"prototype"===r)return;if("arguments"===r||"caller"===r)return;const i=Object.getOwnPropertyDescriptor(e,r),o=Object.getOwnPropertyDescriptor(t,r);!Rr(i,o)&&n||Object.defineProperty(e,r,o)},Rr=function(e,t){return void 0===e||e.configurable||e.writable===t.writable&&e.enumerable===t.enumerable&&e.configurable===t.configurable&&(e.writable||e.value===t.value)},Nr=(e,t)=>`/* Wrapped ${e}*/\n${t}`,Lr=Object.getOwnPropertyDescriptor(Function.prototype,"toString"),Tr=Object.getOwnPropertyDescriptor(Function.prototype.toString,"name");function Dr(e=""){return e.toString().normalize("NFKD").toLowerCase().trim().replace(/\s+/g,"-").replace(/[^\w-]+/g,"").replace(/--+/g,"-").replace(/-$/g,"")}function Fr(e="id"){return`${e.length?`${e}-`:""}${Math.random().toString(36).substr(2,9)}`}Object.create(null),/(android)/i.test(window.navigator.userAgent);const zr=!!window.chrome,Ir=(!(document.documentMode||!1)&&window.StyleMedia,window.navigator.userAgent.match(/(iPod|iPhone|iPad)/i),window.navigator.userAgent.match(/(iPod|iPhone)/i),!!window.opera||window.navigator.userAgent.indexOf(" OPR/")>=0);Object.prototype.toString.call(window.HTMLElement).indexOf("Constructor")>0||!zr&&!Ir&&window.webkitAudioContext,window.navigator.platform;const $r=(e,t="",r="gform-spacing")=>{const n={};return!e||"string"!=typeof e&&"number"!=typeof e&&!Array.isArray(e)||Array.isArray(e)&&!e.length?n:"string"==typeof e||"number"==typeof e?(n[`${r}--${t}bottom-${e}`]=!0,n):1===e.length?(["top","right","bottom","left"].forEach((i=>{n[`${r}--${t}${i}-${e[0]}`]=!0})),n):2===e.length?(["top","bottom"].forEach((i=>{n[`${r}--${t}${i}-${e[0]}`]=!0})),["right","left"].forEach((i=>{n[`${r}--${t}${i}-${e[1]}`]=!0})),n):3===e.length?(n[`${r}--${t}top-${e[0]}`]=!0,["right","left"].forEach((i=>{n[`${r}--${t}${i}-${e[1]}`]=!0})),n[`gform-spacing--${t}bottom-${e[2]}`]=!0,n):4===e.length?(n[`${r}--${t}top-${e[0]}`]=!0,n[`${r}--${t}right-${e[1]}`]=!0,n[`${r}--${t}bottom-${e[2]}`]=!0,n[`${r}--${t}left-${e[3]}`]=!0,n):n};function Br(e="",t="gform-spacing"){const r={};return!e||"string"!=typeof e&&"number"!=typeof e&&!Array.isArray(e)&&("object"!=typeof e||Array.isArray(e))||Array.isArray(e)&&!e.length?r:(r[t]=!0,"string"==typeof e||"number"==typeof e||Array.isArray(e)?{...r,...$r(e,"",t)}:["","md","lg"].reduce(((r,n)=>Object.prototype.hasOwnProperty.call(e,n)?{...r,...$r(e[n],n?`${n}-`:"",t)}:r),r))}if("undefined"!=typeof Element&&!Element.prototype.matches){const e=Element.prototype;e.matches=e.matchesSelector||e.mozMatchesSelector||e.msMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector}window.gform=window.gform||{},window.gform.instances=window.gform.instances||{},window.gform.instances.filters=window.gform.instances.filters||[],window.gform.instances.filters;const{useState:Wr}=i();function qr(){return qr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},qr.apply(null,arguments)}const{Children:Hr,useEffect:Ur,useRef:Qr,useState:Vr}=i();(({activeIndex:e=0,children:t,childWrapperAttributes:r={},childWrapperClasses:n=[],customAttributes:o={},customClasses:a=[],duration:s=250,id:l=""})=>{const u=Qr([]),[d,h]=Vr(e),[p,f]=Vr(null),m=(e=>{const[t]=Wr((()=>e||Fr("id")));return t})(l);u.current&&u.current.length===Hr.count(t)||(u.current=Array.from({length:Hr.count(t)},(()=>null))),Ur((()=>{h(e),setTimeout((()=>f(e)),s)}),[e,s]);const g={className:c()(a),id:m,...o,style:{position:"relative",...o.style||{}}};return i().createElement("div",g,Hr.map(t,((e,t)=>{const o=[d,p].includes(t),a=`${m}-${t}`,l={transition:`opacity ${s}ms`};t===d?l.opacity=1:t===p&&(l.position="absolute",l.top="0",l.left="0",l.width="100%",l.opacity=0);const h={className:c()(n),id:a,style:l,...r};return i().createElement("div",qr({},h,{key:a,ref:e=>u.current[t]=e}),o&&e)})))}).propTypes={activeIndex:s().number,children:s().node,childWrapperAttributes:s().object,childWrapperClasses:s().oneOfType([s().string,s().array,s().object]),customAttributes:s().object,customClasses:s().oneOfType([s().string,s().array,s().object]),duration:s().number,id:s().string};const{useState:Yr,useEffect:Kr}=i(),{useState:Gr}=i(),{createContext:Xr,useContext:Jr}=i(),{createContext:Zr,useContext:en}=(Xr(null),i()),{createContext:tn,useContext:rn,useState:nn}=(Zr(null),i());tn(null);const{useCallback:on,useEffect:an,useRef:sn}=i();r(663),r(655),r(710),r(891);const{useCallback:ln,useState:cn}=i(),{useEffect:un}=i(),{Zp:dn}=e,{useEffect:hn,useState:pn}=i(),{useState:fn,useEffect:mn}=i();"undefined"!=typeof window&&window.document;const{useState:gn,useEffect:yn}=i();function vn(e){const[t,r]=gn(e);return yn((()=>{r(e)}),[e]),[t,r]}const bn=({children:e=null,customAttributes:t={},customClasses:r={},tagName:n="div"})=>{const o={className:c()({"gform-layout":!0},r),"data-testid":"gform-layout",...t},a=n;return i().createElement(a,o,e)};bn.propTypes={children:s().oneOfType([s().arrayOf(s().node),s().node]),customAttributes:s().object,customClasses:s().oneOfType([s().string,s().array,s().object]),tagName:s().string};const wn=bn;function xn(){return xn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},xn.apply(null,arguments)}const{forwardRef:kn}=i(),En=kn((({children:e=null,customAttributes:t={},customClasses:r={},Header:n=null,layoutTagName:o="div",PrimarySideBarChildren:a=null,SecondarySideBarChildren:s=null,simplebarContent:l=!0,simpleBarRef:u=null,...d},h)=>{const p={customClasses:c()({"gform-layout--modular-sidebar":!0},r),...t,ref:h,tagName:o};return i().createElement(wn,p,n&&i().createElement(n,xn({customClasses:["gform-modular-sidebar__header"]},d)),i().createElement("div",{className:"gform-modular-sidebar__body","data-testid":"gform-modular-sidebar-body"},a&&i().createElement("div",{className:"gform-modular-sidebar__primary-sidebar","data-testid":"gform-modular-sidebar-primary-sidebar"},i().createElement(a,d)),s&&i().createElement("div",{className:"gform-modular-sidebar__secondary-sidebar","data-testid":"gform-modular-sidebar-secondary-sidebar"},i().createElement(s,d)),i().createElement("div",{className:"gform-modular-sidebar__content","data-testid":"gform-modular-sidebar-content"},i().createElement(kr,{condition:l,wrapper:e=>i().createElement(R,{scrollableNodeProps:{ref:u}},e)},e))))}));En.propTypes={children:s().oneOfType([s().arrayOf(s().node),s().node]),customAttributes:s().object,customClasses:s().oneOfType([s().string,s().array,s().object]),Header:s().func,layoutTagName:s().string,PrimarySideBarChildren:s().func,SecondarySideBarChildren:s().func},En.displayName="ModularSidebar";const Cn=En,jn=window.wp.i18n,{forwardRef:On}=i(),_n={"metric-info":"mail","metric-success":"check-circle","metric-warn":"clock","metric-error":"x-circle","status-default":"question-mark-simple","status-locked":"lock","status-info":"information-simple","status-incorrect":"x-simple","status-correct":"checkmark-simple","status-error":"exclamation-simple"},Sn=On((({children:e=null,customAttributes:t={},customClasses:r=[],icon:n="",iconPrefix:o="gravity-component-icon",preset:a="",spacing:s=""},l)=>{!n.length&&_n[a]&&(n=_n[a]);const u={className:c()({[`${o}`]:!0,[`${o}--${n}`]:n.length>0,"gform-icon--preset-active":a.length>0,[`gform-icon-preset--${a}`]:a.length>0,...Br(s)},r),ref:l,...t};return i().createElement("span",u,e)}));Sn.propTypes={children:s().oneOfType([s().arrayOf(s().node),s().node]),customAttributes:s().object,customClasses:s().oneOfType([s().string,s().array,s().object]),icon:s().string,iconPrefix:s().string,spacing:s().oneOfType([s().string,s().number,s().array,s().object])},Sn.displayName="Icon";const An=Sn,Pn=({children:e=null,displayText:t=!0,loader:r=null,mask:n=!1,maskCustomAttributes:o={},maskCustomClasses:a=[],maskTheme:s="light",text:l="",textColor:u="#000",textCustomAttributes:d={},textCustomClasses:h=[]})=>{const p=n?{className:c()({"gform-loader__mask":!0,[`gform-loader__mask--theme-${s}`]:!0},a),...o}:{},f=l?{className:c()({"gform-loader__text":t,"gform-visually-hidden":!t},h),style:{color:u},...d}:{};return i().createElement(i().Fragment,null,i().createElement(kr,{condition:n,wrapper:e=>i().createElement("div",p,e)},i().createElement(kr,{condition:!n&&l&&t,wrapper:e=>i().createElement("span",{className:"gform-loader__inner"},e)},r,l&&i().createElement("span",f,l),e)))};Pn.propTypes={children:s().oneOfType([s().arrayOf(s().node),s().node]),displayText:s().bool,loader:s().node,mask:s().bool,maskCustomAttributes:s().object,maskCustomClasses:s().oneOfType([s().string,s().array,s().object]),maskTheme:s().string,text:s().string,textColor:s().string,textCustomAttributes:s().object,textCustomClasses:s().oneOfType([s().string,s().array,s().object])},Pn.displayName="Loader";const Mn=Pn;function Rn(){return Rn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Rn.apply(null,arguments)}const{forwardRef:Nn}=i(),Ln=Nn((({customAttributes:e={},customClasses:t=[],foreground:r="",lineWeight:n=2,loaderCustomAttributes:o={},size:a=40,spacing:s="",speed:l=2},u)=>{const d={className:c()({"gform-loader":!0,"gform-loader--ring":!0,...Br(s)},t),height:a,width:a,viewBox:"25 25 50 50",style:{animation:`gformLoaderRotate ${l}s linear infinite`,height:`${a}px`,width:`${a}px`},...e},h=50*n/a,p={animation:`animation: gformLoaderStretch calc(${l}s * 0.75) ease-in-out infinite`};r&&(p.stroke=r);const f={...o,loader:i().createElement("svg",Rn({},d,{ref:u}),i().createElement("circle",{cx:"50",cy:"50",r:"20",strokeWidth:h,style:p}))};return i().createElement(Mn,f)}));Ln.propTypes={customAttributes:s().object,customClasses:s().oneOfType([s().string,s().array,s().object]),foreground:s().string,lineWeight:s().number,loaderCustomAttributes:s().object,size:s().number,spacing:s().oneOfType([s().string,s().number,s().array,s().object]),speed:s().number},Ln.displayName="Loaders/RingLoader";const Tn=Ln,{forwardRef:Dn,useState:Fn,useRef:zn,useEffect:In}=i(),$n={"size-height-s":"size-text-xs","size-height-m":"size-text-sm","size-height-l":"size-text-sm","size-height-xl":"size-text-sm","size-height-xxl":"size-text-md"},Bn=Dn((({active:e=!1,activeText:t="",activeType:r="",ariaLabel:n="",children:o=null,circular:a=!1,customAttributes:s={},customClasses:l=[],disabled:u=!1,disableWhileActive:d=!0,icon:h="",iconAttributes:p={},iconPosition:f="",iconPrefix:m="gform-icon",label:g="",loaderProps:y={customClasses:"gform-button__loader",lineWeight:2,size:16},lockSize:v=!1,onClick:b=()=>{},size:w="size-r",spacing:x="",type:k="primary-new",width:E="auto"},C)=>{const j=["icon-white","icon-grey"].includes(k),[O,_]=Fn(null),[S,A]=Fn({width:"auto",height:"auto"}),P=zn();In((()=>{if(P.current&&v){const e=new IntersectionObserver((t=>{t.forEach((t=>{t.isIntersecting&&(A({width:P.current.offsetWidth,height:P.current.offsetHeight}),e.disconnect())}))}),{threshold:.1});e.observe(P.current),_(e)}return()=>{O&&O.disconnect()}}),[P,v]);const M={className:c()({"gform-button":!0,[`gform-button--${w}`]:!0,[`gform-button--${k}`]:!0,[`gform-button--width-${E}`]:!j,"gform-button--circular":!j&&a,"gform-button--activated":e,[`gform-button--active-type-${r}`]:r,"gform-button--loader-after":"loader"===r,"gform-button--icon-leading":!j&&h&&"leading"===f,"gform-button--icon-trailing":!j&&h&&"trailing"===f,...Br(x)},l),onClick:b,disabled:u||d&&e,ref:e=>{P.current=e,"function"==typeof C?C(e):C&&(C.current=e)},style:e&&v?{width:`${S.width}px`,height:`${S.height}px`}:{},...s};n&&(M["aria-label"]=n);const R={...p,customClasses:c()(["gform-button__icon"],p.customClasses||[]),icon:h,iconPrefix:m};return i().createElement("button",M,j&&h&&(()=>{const e=c()({"gform-button__text":!0,"gform-visually-hidden":!0});return i().createElement(i().Fragment,null,i().createElement(An,R),g&&i().createElement("span",{className:e},g))})()||(()=>{const n=$n[w],a=c()({"gform-button__text":!0,"gform-button__text--inactive":!0,[`gform-typography--${n}`]:0===w.indexOf("size-height-"),"gform-visually-hidden":j}),s=c()({"gform-button__text":!0,"gform-button__text--active":!0,[`gform-typography--${n}`]:0===w.indexOf("size-height-")}),l=t&&e;return i().createElement(i().Fragment,null,h&&(!g||"leading"===f)&&i().createElement(An,R),g&&!l&&i().createElement("span",{className:a},g),l&&i().createElement("span",{className:s},t),h&&"trailing"===f&&i().createElement(An,R),"loader"===r&&e&&i().createElement(Tn,y),o)})())}));Bn.propTypes={active:s().bool,activeText:s().string,activeType:s().oneOf(["loader"]),children:s().oneOfType([s().arrayOf(s().node),s().node]),circular:s().bool,customAttributes:s().object,customClasses:s().oneOfType([s().string,s().array,s().object]),disabled:s().bool,disableWhileActive:s().bool,icon:s().string,iconAttributes:s().object,iconPosition:s().oneOf(["leading","trailing"]),iconPrefix:s().string,label:s().string,loaderProps:s().object,lockSize:s().bool,onClick:s().func,size:s().string,spacing:s().oneOfType([s().string,s().number,s().array,s().object]),type:s().string,width:s().string},Bn.displayName="Button";const Wn=Bn;var qn,Hn=(qn=function(e,t){return qn=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},qn(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}qn(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),Un=function(e){function r(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={hasError:!1},t}return Hn(r,e),r.getDerivedStateFromError=function(e){return{hasError:!0,error:e}},r.prototype.componentDidCatch=function(e,t){console.error("Spellbook encountered an error:",e),console.error("Component stack:",t.componentStack)},r.prototype.render=function(){return this.state.hasError?(0,t.jsx)(Qn,{error:this.state.error}):this.props.children},r}(i().Component),Qn=function(e){var r=e.error;return(0,t.jsxs)("div",{className:"error-boundary",children:[(0,t.jsx)("h2",{className:"error-boundary__title",children:(0,jn.__)("Oops! Something went wrong.","spellbook")}),(0,t.jsx)("p",{className:"error-boundary__message",children:(0,jn.__)("We've encountered an unexpected error. Please try refreshing the page.","spellbook")}),r&&(0,t.jsxs)("div",{className:"error-boundary__details",children:[(0,t.jsx)("p",{className:"error-boundary__error-name",children:r.name}),(0,t.jsx)("p",{className:"error-boundary__error-message",children:r.message}),r.stack&&(0,t.jsx)("pre",{className:"error-boundary__stack-trace",children:r.stack})]}),(0,t.jsxs)("p",{className:"error-boundary__support",children:[(0,jn.__)("If this error persists, please contact","spellbook")," ",(0,t.jsx)("a",{href:"https://gravitywiz.com/support",target:"_blank",rel:"noopener noreferrer",className:"error-boundary__support-link",children:(0,jn.__)("Gravity Wiz Support","spellbook")}),(0,jn.__)(" for assistance.","spellbook")]}),(0,t.jsx)("div",{className:"error-boundary__actions",children:(0,t.jsx)(Wn,{onClick:function(){return window.location.reload()},variant:"primary",icon:"update",children:(0,jn.__)("Refresh Page","spellbook")})})]})};const Vn=Un;var Yn,Kn,Gn,Xn,Jn;function Zn(){return Zn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Zn.apply(null,arguments)}const ei=function(e){return n.createElement("svg",Zn({xmlns:"http://www.w3.org/2000/svg",width:48,height:46,fill:"none",viewBox:"0 0 48 46"},e),Yn||(Yn=n.createElement("path",{fill:"#fff",d:"m16.695 8.016-.406-1.554c-.03-.11-.185-.113-.216-.005l-.467 1.536a.11.11 0 0 1-.08.077l-1.553.406c-.11.03-.113.185-.005.216l1.536.467q.06.018.077.08l.406 1.553c.03.11.185.113.216.005l.467-1.536a.11.11 0 0 1 .08-.077l1.553-.406c.11-.03.113-.185.005-.216M38.081 5.119l2.83-.792c.201-.056.201-.34 0-.397l-2.827-.807a.2.2 0 0 1-.143-.142L37.149.15c-.057-.201-.34-.201-.397 0l-.808 2.828a.2.2 0 0 1-.142.142l-2.83.791c-.2.057-.2.34 0 .397l2.828.808c.07.02.124.074.142.142l.794 2.83c.056.2.34.2.397 0l.807-2.828a.2.2 0 0 1 .142-.142"})),Kn||(Kn=n.createElement("path",{stroke:"#fff",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.993,d:"m6.913 30.991-4.151 2.366 19.973 10.902 24.238-15.44-3.84-1.796"})),Gn||(Gn=n.createElement("path",{stroke:"#fff",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.993,d:"M2.762 21.671 27.805 8.17l19.168 8.964-24.238 15.44z"})),Xn||(Xn=n.createElement("path",{stroke:"#fff",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.993,d:"M2.695 33.286c-1.646-2.59-2.832-6.752.072-11.616M6.715 23.817v6.576l16.044 9.168 20.03-12.655v-6.777"})),Jn||(Jn=n.createElement("path",{fill:"#fff",fillRule:"evenodd",d:"M34.732 17.272a7.55 7.55 0 0 1-7.093-6.011 28.2 28.2 0 0 1-12.592 8.092l-6.64 2.072 5.956-.254a8.464 8.464 0 0 1 8.792 7.728A24.19 24.19 0 0 1 37.642 18.5l3.848-.963zm-6.697 3.393-2.536-.757a.33.33 0 0 0-.202.005l-2.625.909c-.19.064-.384-.047-.277-.155l1.524-1.53c.037-.037.043-.083.007-.117l-1.364-1.446c-.096-.105.113-.227.291-.173l2.536.757c.*************.201-.005l2.626-.909c.19-.064.384.047.276.155l-1.523 1.53c-.037.037-.043.084-.008.117l1.365 1.447c.096.104-.113.226-.291.172",clipRule:"evenodd"})))};var ti;function ri(){return ri=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ri.apply(null,arguments)}const ni=function(e){return n.createElement("svg",ri({xmlns:"http://www.w3.org/2000/svg",width:145,height:19,fill:"none",viewBox:"0 0 145 19"},e),ti||(ti=n.createElement("path",{fill:"#000",d:"M6.512 18.288c-3.552 0-5.76-1.632-6.408-4.464-.048-.192.048-.336.24-.384l2.712-.72c.192-.048.336.024.408.216.48 1.392 1.416 2.04 3 2.04 1.68 0 2.616-.72 2.616-1.824 0-1.008-.624-1.584-3.792-2.544C1.736 9.528.464 7.872.464 5.448.464 2.112 2.912.144 6.56.144c3.048 0 5.184 1.368 5.952 3.984.072.192-.024.36-.216.408l-2.664.792c-.192.048-.336 0-.432-.216-.48-1.152-1.272-1.656-2.616-1.656-1.368 0-2.4.528-2.4 1.704 0 .936.648 1.536 3.816 2.496 3.552 1.08 4.824 2.712 4.824 5.232 0 3.408-2.352 5.4-6.312 5.4M17.294 18c-.216 0-.336-.12-.336-.336V.768c0-.216.12-.336.336-.336h5.76c3.864 0 5.928 2.28 5.928 5.592v1.2c0 3.408-2.064 5.568-5.928 5.568h-2.352v4.872c0 .216-.12.336-.336.336zm3.408-8.544h2.064c1.848 0 2.472-.84 2.472-2.472v-.696c0-1.584-.624-2.496-2.472-2.496h-2.064zM33.517 18c-.216 0-.336-.12-.336-.336V.768c0-.216.12-.336.336-.336h10.128c.216 0 .336.12.336.336v2.688c0 .216-.12.336-.336.336h-6.72V7.44h6.024c.216 0 .336.12.336.336v2.568c0 .216-.12.336-.336.336h-6.024v3.96h6.768c.216 0 .336.12.336.336v2.688c0 .216-.12.336-.336.336zm15.45 0c-.216 0-.336-.12-.336-.336V.768c0-.216.12-.336.336-.336h3.072c.216 0 .336.12.336.336v13.776h6.6c.216 0 .336.12.336.336v2.784c0 .216-.12.336-.336.336zm14.442 0c-.216 0-.336-.12-.336-.336V.768c0-.216.12-.336.336-.336h3.072c.216 0 .336.12.336.336v13.776h6.6c.216 0 .336.12.336.336v2.784c0 .216-.12.336-.336.336zm14.442 0c-.216 0-.336-.12-.336-.336V.768c0-.216.12-.336.336-.336h6.672c3.312 0 5.184 1.656 5.184 4.512v.24c0 1.776-.96 3.12-2.472 3.624 1.896.432 3.024 1.896 3.024 4.008v.264c0 3.144-2.04 4.92-5.616 4.92zm3.408-3.24h2.952c1.464 0 2.304-.696 2.304-1.896v-.336c0-1.224-.84-1.92-2.304-1.92H81.26zm0-7.368h2.712c1.296 0 2.088-.672 2.088-1.776v-.192c0-1.104-.768-1.752-2.088-1.752H81.26zm19.897 10.896c-4.704 0-6.96-2.832-6.96-6.72V6.96c0-4.296 2.568-6.816 6.984-6.816 4.824 0 7.008 3.024 7.008 6.72v4.608c0 3.792-2.184 6.816-7.032 6.816m-3.216-6.816c0 1.92.816 3.36 3.24 3.36s3.264-1.44 3.264-3.36V6.96c0-1.92-.84-3.36-3.264-3.36s-3.24 1.44-3.24 3.36zm21.385 6.816c-4.704 0-6.96-2.832-6.96-6.72V6.96c0-4.296 2.568-6.816 6.984-6.816 4.824 0 7.008 3.024 7.008 6.72v4.608c0 3.792-2.184 6.816-7.032 6.816m-3.216-6.816c0 1.92.816 3.36 3.24 3.36s3.264-1.44 3.264-3.36V6.96c0-1.92-.84-3.36-3.264-3.36s-3.24 1.44-3.24 3.36zM131.373 18c-.216 0-.336-.12-.336-.336V.768c0-.216.12-.336.336-.336h3.072c.216 0 .336.12.336.336v7.32l5.688-7.464c.096-.144.24-.192.408-.192h3.36c.288 0 .36.192.192.408l-5.184 6.552 5.376 10.2c.12.216.024.408-.24.408h-3.432c-.168 0-.312-.072-.384-.24l-3.696-7.368-2.088 2.64v4.632c0 .216-.12.336-.336.336z"})))},ii=function(){return(0,t.jsxs)("div",{className:"spellbook-app__header gform-modular-sidebar__header",children:[(0,t.jsx)("div",{className:"spellbook-app__header-logo",children:(0,t.jsx)(ei,{width:46})}),(0,t.jsx)("div",{className:"spellbook-app__header-main",children:(0,t.jsx)(ni,{width:147})})]})},oi=function(e){var r=e.items,n=r.filter((function(e){return!e.bottom})),i=r.filter((function(e){return e.bottom})),o=function(e){return e.map((function(e){return(0,t.jsxs)("li",{className:"gform-router-nav-bar__item ".concat(e.isActive?"gform-router-nav-bar__item--active":""," spellbook-app__nav-bar-item spellbook-app__nav-bar-item--").concat(e.id),children:[(0,t.jsx)(gr,{"aria-labelledby":"gform-router-nav-bar__item-text--".concat(e.id),className:"gform-router-nav-bar__item-link gform-button gform-button--icon-white gform-button--size-height-m spellbook-app__nav-bar-item-link spellbook-app__nav-bar-item-link--".concat(e.id),to:e.path,children:"string"==typeof e.icon?(0,t.jsx)(An,{icon:e.icon,customClasses:"gform-router-nav-bar__item-icon"}):e.icon}),(0,t.jsx)("span",{className:"gform-text gform-text--color-port gform-typography--size-text-xs gform-typography--weight-medium gform-router-nav-bar__item-text spellbook-app__nav-bar-item-text",id:"gform-router-nav-bar__item-text--".concat(e.id),children:"Free Plugins"===e.label?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("span",{className:"nav-text-full",children:"Free Plugins"}),(0,t.jsx)("span",{className:"nav-text-short",children:"Free"})]}):e.label})]},e.id)}))};return(0,t.jsxs)("nav",{className:"gform-router-nav-bar gform-router-nav-bar--icon-button spellbook-app__nav-bar",children:[(0,t.jsx)("ul",{className:"gform-router-nav-bar__list",children:o(n)}),i.length>0&&(0,t.jsx)("ul",{className:"gform-router-nav-bar__list gform-router-nav-bar__list--bottom",children:o(i)})]})};var ai,si;function li(){return li=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},li.apply(null,arguments)}const ci=function(e){return n.createElement("svg",li({xmlns:"http://www.w3.org/2000/svg",width:249,height:294,fill:"none",viewBox:"0 0 249 294"},e),ai||(ai=n.createElement("g",{clipPath:"url(#1a)"},n.createElement("path",{fill:"#000",d:"M158.843 192.818c-15.975-7.949-31.702-32.533-58.325-32.655-37.662-.179-37.82 19.123-62.823 32.96l35.324 61.2a11.09 11.09 0 0 0 9.577 5.529l70.675-.001c3.94-.001 7.597-2.1 9.575-5.526l30.118-52.165s-17.981-1.297-34.121-9.342m-66.438 14.249c-4.393-2.536-5.9-8.161-3.356-12.568a9.185 9.185 0 0 1 12.555-3.364c4.394 2.537 5.914 8.169 3.377 12.563s-8.169 5.913-12.576 3.369m53.395 19.96-10.105 2.707a.74.74 0 0 0-.511.503l-2.987 10.027c-.22.705-1.234.696-1.415-.019l-2.708-10.105a.72.72 0 0 0-.495-.524l-10.027-2.987c-.712-.207-.704-1.221.019-1.415l10.105-2.708a.74.74 0 0 0 .511-.503l2.987-10.026c.22-.705 1.227-.684 1.428.026l2.688 10.11a.75.75 0 0 0 .503.512l10.026 2.986c.718.228.697 1.235-.019 1.416M160.754 157.599l10.105-2.708c.723-.194.731-1.208.019-1.415l-10.027-2.987a.74.74 0 0 1-.503-.511l-2.707-10.105c-.194-.723-1.208-.731-1.416-.019l-2.986 10.026a.74.74 0 0 1-.512.503l-10.104 2.708c-.723.194-.732 1.208-.019 1.415l10.026 2.987c.246.074.438.27.503.511l2.708 10.105c.193.723 1.207.731 1.415.019l2.987-10.026a.74.74 0 0 1 .511-.503"}),n.createElement("path",{stroke:"#000",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:14.706,d:"m145.401 86.332 19.823-34.336c4.059-7.03 13.059-9.441 20.089-5.382l21.026 12.14c7.031 4.058 9.442 13.058 5.383 20.088l-19.823 34.335a14.73 14.73 0 0 0 0 14.706l31.476 54.512a14.73 14.73 0 0 1 0 14.705l-46.971 81.356a14.73 14.73 0 0 1-12.735 7.352l-93.96.007a14.68 14.68 0 0 1-12.735-7.353l-46.961-81.367a14.73 14.73 0 0 1 0-14.706l46.97-81.355a14.73 14.73 0 0 1 12.736-7.353l62.946.003a14.73 14.73 0 0 0 12.736-7.352"}),n.createElement("path",{stroke:"#000",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:14.706,d:"m219.071 66.12-46.498-26.846 4.445-21.14c1.649-7.827 10.464-11.771 17.392-7.771l38.768 22.382c6.928 4 7.932 13.614 1.966 18.948l-16.085 14.419z"}))),si||(si=n.createElement("defs",null,n.createElement("clipPath",{id:"1a"},n.createElement("path",{fill:"#fff",d:"M0 0h249v294H0z"})))))};var ui,di;function hi(){return hi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},hi.apply(null,arguments)}const pi=function(e){return n.createElement("svg",hi({xmlns:"http://www.w3.org/2000/svg",width:249,height:294,fill:"none",viewBox:"0 0 249 294"},e),ui||(ui=n.createElement("g",{clipPath:"url(#0a)"},n.createElement("path",{fill:"#000",d:"M198.421 257.598c-20.785 14.578-45.93 24.014-73.022 24.014s-52.97-9.719-73.937-24.646c-6.108-4.36-9.32-11.716-8.304-19.138l2.063-15.043c22.516 17.606 50.54 27.541 79.746 27.541s56.23-9.569 78.497-26.576l2.928 13.097c1.764 7.904-1.348 16.125-7.987 20.768z"}),n.createElement("path",{stroke:"#000",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:14,d:"M156.419 16.998c-11.383-3.445-23.515-5.059-36.062-4.526C60.415 15.035 13.903 65.707 16.465 125.633c.999 23.314 9.286 44.598 22.533 61.772 20.851 26.942 54.017 43.684 90.628 42.119 36.861-1.581 68.646-21.334 87.034-50.273 11.549-18.106 17.84-39.823 16.841-62.904a108.2 108.2 0 0 0-5.375-29.455"}),n.createElement("path",{fill:"#000",d:"m211.368 52.328 22.682-6.357c1.614-.45 1.614-2.73 0-3.179l-22.649-6.457c-.549-.15-.982-.599-1.148-1.148l-6.357-22.682c-.449-1.614-2.729-1.614-3.178 0l-6.457 22.649c-.15.55-.599.982-1.149 1.148L170.43 42.66c-1.614.45-1.614 2.73 0 3.179l22.649 6.456c.549.15.982.6 1.148 1.149l6.357 22.682c.45 1.614 2.73 1.614 3.179 0l6.457-22.649c.15-.55.599-.982 1.148-1.148M158.205 89.986l15.526-4.344c1.099-.316 1.099-1.864 0-2.18l-15.509-4.426c-.383-.1-.666-.4-.782-.783l-4.344-15.526c-.316-1.098-1.864-1.098-2.18 0l-4.426 15.51c-.1.383-.4.682-.783.782l-15.526 4.343c-1.098.317-1.098 1.864 0 2.18l15.51 4.427c.383.1.665.4.782.782l4.343 15.526c.317 1.099 1.864 1.099 2.18 0l4.427-15.51c.1-.382.399-.682.782-.781M194.989 129.794l12.281-3.444c.866-.25.866-1.482 0-1.731l-12.264-3.495a.88.88 0 0 1-.616-.616l-3.445-12.281c-.249-.865-1.481-.865-1.73 0l-3.495 12.265a.89.89 0 0 1-.616.616l-12.281 3.444c-.866.25-.866 1.481 0 1.731l12.264 3.495c.3.083.533.316.616.615l3.445 12.282c.25.865 1.481.865 1.731 0l3.494-12.265a.89.89 0 0 1 .616-.616"}),n.createElement("path",{stroke:"#000",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:14,d:"M52.63 130.954c-3.029-28.939 10.534-55.782 33-71.092M109.942 48.946a78 78 0 0 1 10.717-1.897"}))),di||(di=n.createElement("defs",null,n.createElement("clipPath",{id:"0a"},n.createElement("path",{fill:"#fff",d:"M0 0h249v294H0z"})))))};var fi,mi;function gi(){return gi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},gi.apply(null,arguments)}const yi=function(e){return n.createElement("svg",gi({xmlns:"http://www.w3.org/2000/svg",width:249,height:294,fill:"none",viewBox:"0 0 249 294"},e),fi||(fi=n.createElement("g",{clipPath:"url(#2a)"},n.createElement("path",{stroke:"#000",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:12,d:"M219.716 175.036v88.34l-191.954-.029-.399-88.311M4.566 126.591l22.798-66.967h193.778l25.647 66.967"}),n.createElement("path",{fill:"#000",d:"M61.218 126.592c-.613 17.083-14.648 30.719-31.874 30.719s-31.275-13.636-31.888-30.719zM125.334 126.592c-.613 17.083-14.647 30.719-31.874 30.719s-31.26-13.636-31.873-30.719h63.761zM189.452 126.592c-.612 17.083-14.647 30.719-31.873 30.719-17.227 0-31.261-13.636-31.874-30.719h63.762zM253.569 126.592c-.612 17.083-14.647 30.719-31.873 30.719s-31.261-13.636-31.874-30.719h63.762zM195.495 239.154v-32.059c0-17.706-14.354-32.059-32.059-32.059-17.706 0-32.059 14.353-32.059 32.059v32.059zM88.203 212.837l17.925-4.973c1.268-.356 1.282-2.151 0-2.522l-17.868-5.143a1.32 1.32 0 0 1-.897-.912l-4.973-17.925c-.356-1.268-2.151-1.282-2.522 0l-5.144 17.868c-.128.441-.47.769-.912.897L55.889 205.1c-1.268.356-1.282 2.151 0 2.522l17.868 5.144c.441.128.769.47.897.911l4.973 17.925c.356 1.268 2.151 1.282 2.522 0l5.144-17.868c.128-.441.47-.769.911-.897"}))),mi||(mi=n.createElement("defs",null,n.createElement("clipPath",{id:"2a"},n.createElement("path",{fill:"#fff",d:"M0 0h249v294H0z"})))))},vi=window.wp.primitives,bi=(0,t.jsx)(vi.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,t.jsx)(vi.Path,{d:"M9 13.5a1.5 1.5 0 100-3 1.5 1.5 0 000 3zM9 16a4.002 4.002 0 003.8-2.75H15V16h2.5v-2.75H19v-2.5h-6.2A4.002 4.002 0 005 12a4 4 0 004 4z",fillRule:"evenodd",clipRule:"evenodd"})}),wi=window.wp.components;var xi,ki;function Ei(){return Ei=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ei.apply(null,arguments)}const Ci=function(e){return n.createElement("svg",Ei({xmlns:"http://www.w3.org/2000/svg",width:22,height:22,fill:"none",viewBox:"0 0 22 22"},e),xi||(xi=n.createElement("g",{clipPath:"url(#3a)"},n.createElement("path",{stroke:"#000",strokeLinecap:"round",strokeLinejoin:"round",d:"m7.701.792 1.954 4.954L14.609 7.7 9.655 9.654 7.7 14.608 5.747 9.654.793 7.7l4.954-1.954z"}),n.createElement("path",{fill:"#000",d:"m18.391 7.353 1.02 2.588L22 10.96l-2.587 1.02-1.02 2.588-1.021-2.587-2.588-1.02 2.588-1.021zM12.76 15.664l.896 2.272 2.272.896-2.272.896L12.76 22l-.896-2.272-2.272-.896 2.272-.896z"}))),ki||(ki=n.createElement("defs",null,n.createElement("clipPath",{id:"3a"},n.createElement("path",{fill:"#fff",d:"M0 0h22v22H0z"})))))};var ji,Oi,_i;function Si(){return Si=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Si.apply(null,arguments)}const Ai=function(e){return n.createElement("svg",Si({xmlns:"http://www.w3.org/2000/svg",width:24,height:23,fill:"none",viewBox:"0 0 24 23"},e),ji||(ji=n.createElement("path",{stroke:"#000",strokeLinecap:"round",strokeLinejoin:"round",d:"M1.422 9.583h21.291M12.803 19.885l-5.2-10.302 1.374-5.495h10.99l2.747 5.495zm0 0L1.422 9.583l.968-1.937"})),Oi||(Oi=n.createElement("path",{stroke:"#000",strokeLinecap:"round",strokeLinejoin:"round",d:"m12.803 19.885 3.73-10.302-1.1-5.495"})),_i||(_i=n.createElement("path",{fill:"#000",d:"m3.944.214.814 2.062 2.063.814-2.063.813-.814 2.063-.813-2.063-2.063-.813 2.063-.814zM20.193 16.462l.813 2.062 2.063.814-2.063.813-.814 2.063-.813-2.063-2.063-.813 2.063-.814z"})))},Pi=function(){var e=jt(),r=[{id:"all",label:"All",path:"/",icon:(0,t.jsx)(Ci,{width:30,height:30}),isActive:"/"===e.pathname},{id:"perks",label:"Perks",path:"/perks",icon:(0,t.jsx)(ci,{width:30,height:30}),isActive:"/"===e.pathname},{id:"connect",label:"Connect",path:"/connect",icon:(0,t.jsx)(pi,{width:30,height:30}),isActive:"/"===e.pathname},{id:"shop",label:"Shop",path:"/shop",icon:(0,t.jsx)(yi,{width:30,height:30}),isActive:"/shop"===e.pathname},{id:"free-plugins",label:"Free Plugins",path:"/free-plugins",icon:(0,t.jsx)(Ai,{width:34,height:34}),isActive:"/free-plugins"===e.pathname},{id:"license",label:"Licenses",path:"/licenses",bottom:!0,icon:(0,t.jsx)(wi.Icon,{icon:bi,size:30}),isActive:"/licenses"===e.pathname}];return(0,t.jsx)(oi,{items:r})},{forwardRef:Mi}=i(),Ri=Mi((({alignItems:e="center",children:t=null,columnSpacing:r=0,container:n,customAttributes:o={},customClasses:a=[],direction:s="row",elementType:l="list",item:u,justifyContent:d="center",rowSpacing:h=0,spacing:p="",type:f="fixed",width:m=0,wrap:g},y)=>{const v={className:c()({"gform-grid":!0,[`gform-grid--${l}`]:l,[`gform-grid--${f}`]:n,"gform-grid--wrap":n&&g,"gform-grid--container":n,"gform-grid--item":u,[`gform-grid--col-spacing-${r}`]:n&&r&&Number.isInteger(r),[`gform-grid--row-spacing-${h}`]:n&&h&&Number.isInteger(h),...Br(p)},a),ref:y,...o};n?v.style={alignItems:e,flexDirection:s,justifyContent:d}:m&&"fixed"!==f&&(v.style.flexBasis=100*m/12+"%");const b="list"===l;let w="div";return n&&b?w="ul":u&&b&&(w="li"),i().createElement(w,v,t)}));Ri.propTypes={alignItems:s().string,children:s().oneOfType([s().arrayOf(s().node),s().node]),columnSpacing:s().number,container:s().bool,customAttributes:s().object,customClasses:s().oneOfType([s().string,s().array,s().object]),direction:s().string,elementType:s().oneOf(["list","div"]),item:s().bool,justifyContent:s().string,rowSpacing:s().number,spacing:s().oneOfType([s().string,s().number,s().array,s().object]),type:s().oneOf(["fixed","fluid"]),width:s().number,wrap:s().bool},Ri.displayName="Grid";const Ni=Ri,{forwardRef:Li}=i(),Ti=Li((({children:e=null,customAttributes:t={},customClasses:r=[],display:n="block",setDisplay:o=!0,spacing:a="",tagName:s="div",unit:l="px",x:u=0,xProp:d="maxWidth",y:h=0,yProp:p="minHeight"},f)=>{const m={};o&&(m.display=n),u&&(m[d]=`${u}${l}`),h&&(m[p]=`${h}${l}`);const g={...m,...t.style||{}},y={className:c()({"gform-box":!0,...Br(a)},r),ref:f,...t,style:g},v=s;return i().createElement(v,y,e)}));Ti.displayName="Box";const Di=Ti;function Fi(e){return Array.isArray?Array.isArray(e):"[object Array]"===qi(e)}function zi(e){return"string"==typeof e}function Ii(e){return"number"==typeof e}function $i(e){return"object"==typeof e}function Bi(e){return null!=e}function Wi(e){return!e.trim().length}function qi(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}const Hi=Object.prototype.hasOwnProperty;class Ui{constructor(e){this._keys=[],this._keyMap={};let t=0;e.forEach((e=>{let r=Qi(e);this._keys.push(r),this._keyMap[r.id]=r,t+=r.weight})),this._keys.forEach((e=>{e.weight/=t}))}get(e){return this._keyMap[e]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function Qi(e){let t=null,r=null,n=null,i=1,o=null;if(zi(e)||Fi(e))n=e,t=Vi(e),r=Yi(e);else{if(!Hi.call(e,"name"))throw new Error("Missing name property in key");const a=e.name;if(n=a,Hi.call(e,"weight")&&(i=e.weight,i<=0))throw new Error((e=>`Property 'weight' in key '${e}' must be a positive integer`)(a));t=Vi(a),r=Yi(a),o=e.getFn}return{path:t,id:r,weight:i,src:n,getFn:o}}function Vi(e){return Fi(e)?e:e.split(".")}function Yi(e){return Fi(e)?e.join("."):e}var Ki={isCaseSensitive:!1,ignoreDiacritics:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(e,t)=>e.score===t.score?e.idx<t.idx?-1:1:e.score<t.score?-1:1,includeMatches:!1,findAllMatches:!1,minMatchCharLength:1,location:0,threshold:.6,distance:100,useExtendedSearch:!1,getFn:function(e,t){let r=[],n=!1;const i=(e,t,o)=>{if(Bi(e))if(t[o]){const a=e[t[o]];if(!Bi(a))return;if(o===t.length-1&&(zi(a)||Ii(a)||function(e){return!0===e||!1===e||function(e){return $i(e)&&null!==e}(e)&&"[object Boolean]"==qi(e)}(a)))r.push(function(e){return null==e?"":function(e){if("string"==typeof e)return e;let t=e+"";return"0"==t&&1/e==-1/0?"-0":t}(e)}(a));else if(Fi(a)){n=!0;for(let e=0,r=a.length;e<r;e+=1)i(a[e],t,o+1)}else t.length&&i(a,t,o+1)}else r.push(e)};return i(e,zi(t)?t.split("."):t,0),n?r:r[0]},ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1};const Gi=/[^ ]+/g;class Xi{constructor({getFn:e=Ki.getFn,fieldNormWeight:t=Ki.fieldNormWeight}={}){this.norm=function(e=1,t=3){const r=new Map,n=Math.pow(10,t);return{get(t){const i=t.match(Gi).length;if(r.has(i))return r.get(i);const o=1/Math.pow(i,.5*e),a=parseFloat(Math.round(o*n)/n);return r.set(i,a),a},clear(){r.clear()}}}(t,3),this.getFn=e,this.isCreated=!1,this.setIndexRecords()}setSources(e=[]){this.docs=e}setIndexRecords(e=[]){this.records=e}setKeys(e=[]){this.keys=e,this._keysMap={},e.forEach(((e,t)=>{this._keysMap[e.id]=t}))}create(){!this.isCreated&&this.docs.length&&(this.isCreated=!0,zi(this.docs[0])?this.docs.forEach(((e,t)=>{this._addString(e,t)})):this.docs.forEach(((e,t)=>{this._addObject(e,t)})),this.norm.clear())}add(e){const t=this.size();zi(e)?this._addString(e,t):this._addObject(e,t)}removeAt(e){this.records.splice(e,1);for(let t=e,r=this.size();t<r;t+=1)this.records[t].i-=1}getValueForItemAtKeyId(e,t){return e[this._keysMap[t]]}size(){return this.records.length}_addString(e,t){if(!Bi(e)||Wi(e))return;let r={v:e,i:t,n:this.norm.get(e)};this.records.push(r)}_addObject(e,t){let r={i:t,$:{}};this.keys.forEach(((t,n)=>{let i=t.getFn?t.getFn(e):this.getFn(e,t.path);if(Bi(i))if(Fi(i)){let e=[];const t=[{nestedArrIndex:-1,value:i}];for(;t.length;){const{nestedArrIndex:r,value:n}=t.pop();if(Bi(n))if(zi(n)&&!Wi(n)){let t={v:n,i:r,n:this.norm.get(n)};e.push(t)}else Fi(n)&&n.forEach(((e,r)=>{t.push({nestedArrIndex:r,value:e})}))}r.$[n]=e}else if(zi(i)&&!Wi(i)){let e={v:i,n:this.norm.get(i)};r.$[n]=e}})),this.records.push(r)}toJSON(){return{keys:this.keys,records:this.records}}}function Ji(e,t,{getFn:r=Ki.getFn,fieldNormWeight:n=Ki.fieldNormWeight}={}){const i=new Xi({getFn:r,fieldNormWeight:n});return i.setKeys(e.map(Qi)),i.setSources(t),i.create(),i}function Zi(e,{errors:t=0,currentLocation:r=0,expectedLocation:n=0,distance:i=Ki.distance,ignoreLocation:o=Ki.ignoreLocation}={}){const a=t/e.length;if(o)return a;const s=Math.abs(n-r);return i?a+s/i:s?1:a}const eo=32;function to(e){let t={};for(let r=0,n=e.length;r<n;r+=1){const i=e.charAt(r);t[i]=(t[i]||0)|1<<n-r-1}return t}const ro=String.prototype.normalize?e=>e.normalize("NFD").replace(/[\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u08D3-\u08E1\u08E3-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u09FE\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0AFA-\u0AFF\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B62\u0B63\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0C00-\u0C04\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0D00-\u0D03\u0D3B\u0D3C\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D82\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EB9\u0EBB\u0EBC\u0EC8-\u0ECD\u0F18\u0F19\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F\u109A-\u109D\u135D-\u135F\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u180B-\u180D\u1885\u1886\u18A9\u1920-\u192B\u1930-\u193B\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F\u1AB0-\u1ABE\u1B00-\u1B04\u1B34-\u1B44\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BE6-\u1BF3\u1C24-\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF2-\u1CF4\u1CF7-\u1CF9\u1DC0-\u1DF9\u1DFB-\u1DFF\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA880\uA881\uA8B4-\uA8C5\uA8E0-\uA8F1\uA8FF\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9E5\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F]/g,""):e=>e;class no{constructor(e,{location:t=Ki.location,threshold:r=Ki.threshold,distance:n=Ki.distance,includeMatches:i=Ki.includeMatches,findAllMatches:o=Ki.findAllMatches,minMatchCharLength:a=Ki.minMatchCharLength,isCaseSensitive:s=Ki.isCaseSensitive,ignoreDiacritics:l=Ki.ignoreDiacritics,ignoreLocation:c=Ki.ignoreLocation}={}){if(this.options={location:t,threshold:r,distance:n,includeMatches:i,findAllMatches:o,minMatchCharLength:a,isCaseSensitive:s,ignoreDiacritics:l,ignoreLocation:c},e=s?e:e.toLowerCase(),e=l?ro(e):e,this.pattern=e,this.chunks=[],!this.pattern.length)return;const u=(e,t)=>{this.chunks.push({pattern:e,alphabet:to(e),startIndex:t})},d=this.pattern.length;if(d>eo){let e=0;const t=d%eo,r=d-t;for(;e<r;)u(this.pattern.substr(e,eo),e),e+=eo;if(t){const e=d-eo;u(this.pattern.substr(e),e)}}else u(this.pattern,0)}searchIn(e){const{isCaseSensitive:t,ignoreDiacritics:r,includeMatches:n}=this.options;if(e=t?e:e.toLowerCase(),e=r?ro(e):e,this.pattern===e){let t={isMatch:!0,score:0};return n&&(t.indices=[[0,e.length-1]]),t}const{location:i,distance:o,threshold:a,findAllMatches:s,minMatchCharLength:l,ignoreLocation:c}=this.options;let u=[],d=0,h=!1;this.chunks.forEach((({pattern:t,alphabet:r,startIndex:p})=>{const{isMatch:f,score:m,indices:g}=function(e,t,r,{location:n=Ki.location,distance:i=Ki.distance,threshold:o=Ki.threshold,findAllMatches:a=Ki.findAllMatches,minMatchCharLength:s=Ki.minMatchCharLength,includeMatches:l=Ki.includeMatches,ignoreLocation:c=Ki.ignoreLocation}={}){if(t.length>eo)throw new Error("Pattern length exceeds max of 32.");const u=t.length,d=e.length,h=Math.max(0,Math.min(n,d));let p=o,f=h;const m=s>1||l,g=m?Array(d):[];let y;for(;(y=e.indexOf(t,f))>-1;){let e=Zi(t,{currentLocation:y,expectedLocation:h,distance:i,ignoreLocation:c});if(p=Math.min(e,p),f=y+u,m){let e=0;for(;e<u;)g[y+e]=1,e+=1}}f=-1;let v=[],b=1,w=u+d;const x=1<<u-1;for(let n=0;n<u;n+=1){let o=0,s=w;for(;o<s;)Zi(t,{errors:n,currentLocation:h+s,expectedLocation:h,distance:i,ignoreLocation:c})<=p?o=s:w=s,s=Math.floor((w-o)/2+o);w=s;let l=Math.max(1,h-s+1),y=a?d:Math.min(h+s,d)+u,k=Array(y+2);k[y+1]=(1<<n)-1;for(let o=y;o>=l;o-=1){let a=o-1,s=r[e.charAt(a)];if(m&&(g[a]=+!!s),k[o]=(k[o+1]<<1|1)&s,n&&(k[o]|=(v[o+1]|v[o])<<1|1|v[o+1]),k[o]&x&&(b=Zi(t,{errors:n,currentLocation:a,expectedLocation:h,distance:i,ignoreLocation:c}),b<=p)){if(p=b,f=a,f<=h)break;l=Math.max(1,2*h-f)}}if(Zi(t,{errors:n+1,currentLocation:h,expectedLocation:h,distance:i,ignoreLocation:c})>p)break;v=k}const k={isMatch:f>=0,score:Math.max(.001,b)};if(m){const e=function(e=[],t=Ki.minMatchCharLength){let r=[],n=-1,i=-1,o=0;for(let a=e.length;o<a;o+=1){let a=e[o];a&&-1===n?n=o:a||-1===n||(i=o-1,i-n+1>=t&&r.push([n,i]),n=-1)}return e[o-1]&&o-n>=t&&r.push([n,o-1]),r}(g,s);e.length?l&&(k.indices=e):k.isMatch=!1}return k}(e,t,r,{location:i+p,distance:o,threshold:a,findAllMatches:s,minMatchCharLength:l,includeMatches:n,ignoreLocation:c});f&&(h=!0),d+=m,f&&g&&(u=[...u,...g])}));let p={isMatch:h,score:h?d/this.chunks.length:1};return h&&n&&(p.indices=u),p}}class io{constructor(e){this.pattern=e}static isMultiMatch(e){return oo(e,this.multiRegex)}static isSingleMatch(e){return oo(e,this.singleRegex)}search(){}}function oo(e,t){const r=e.match(t);return r?r[1]:null}class ao extends io{constructor(e,{location:t=Ki.location,threshold:r=Ki.threshold,distance:n=Ki.distance,includeMatches:i=Ki.includeMatches,findAllMatches:o=Ki.findAllMatches,minMatchCharLength:a=Ki.minMatchCharLength,isCaseSensitive:s=Ki.isCaseSensitive,ignoreDiacritics:l=Ki.ignoreDiacritics,ignoreLocation:c=Ki.ignoreLocation}={}){super(e),this._bitapSearch=new no(e,{location:t,threshold:r,distance:n,includeMatches:i,findAllMatches:o,minMatchCharLength:a,isCaseSensitive:s,ignoreDiacritics:l,ignoreLocation:c})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(e){return this._bitapSearch.searchIn(e)}}class so extends io{constructor(e){super(e)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(e){let t,r=0;const n=[],i=this.pattern.length;for(;(t=e.indexOf(this.pattern,r))>-1;)r=t+i,n.push([t,r-1]);const o=!!n.length;return{isMatch:o,score:o?0:1,indices:n}}}const lo=[class extends io{constructor(e){super(e)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(e){const t=e===this.pattern;return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}},so,class extends io{constructor(e){super(e)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(e){const t=e.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}},class extends io{constructor(e){super(e)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(e){const t=!e.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,e.length-1]}}},class extends io{constructor(e){super(e)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(e){const t=!e.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,e.length-1]}}},class extends io{constructor(e){super(e)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(e){const t=e.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[e.length-this.pattern.length,e.length-1]}}},class extends io{constructor(e){super(e)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(e){const t=-1===e.indexOf(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,e.length-1]}}},ao],co=lo.length,uo=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,ho=new Set([ao.type,so.type]);const po=[];function fo(e,t){for(let r=0,n=po.length;r<n;r+=1){let n=po[r];if(n.condition(e,t))return new n(e,t)}return new no(e,t)}const mo="$and",go="$path",yo=e=>!(!e[mo]&&!e.$or),vo=e=>({[mo]:Object.keys(e).map((t=>({[t]:e[t]})))});function bo(e,t,{auto:r=!0}={}){const n=e=>{let i=Object.keys(e);const o=(e=>!!e[go])(e);if(!o&&i.length>1&&!yo(e))return n(vo(e));if((e=>!Fi(e)&&$i(e)&&!yo(e))(e)){const n=o?e[go]:i[0],a=o?e.$val:e[n];if(!zi(a))throw new Error((e=>`Invalid value for key ${e}`)(n));const s={keyId:Yi(n),pattern:a};return r&&(s.searcher=fo(a,t)),s}let a={children:[],operator:i[0]};return i.forEach((t=>{const r=e[t];Fi(r)&&r.forEach((e=>{a.children.push(n(e))}))})),a};return yo(e)||(e=vo(e)),n(e)}function wo(e,t){const r=e.matches;t.matches=[],Bi(r)&&r.forEach((e=>{if(!Bi(e.indices)||!e.indices.length)return;const{indices:r,value:n}=e;let i={indices:r,value:n};e.key&&(i.key=e.key.src),e.idx>-1&&(i.refIndex=e.idx),t.matches.push(i)}))}function xo(e,t){t.score=e.score}class ko{constructor(e,t={},r){this.options={...Ki,...t},this.options.useExtendedSearch,this._keyStore=new Ui(this.options.keys),this.setCollection(e,r)}setCollection(e,t){if(this._docs=e,t&&!(t instanceof Xi))throw new Error("Incorrect 'index' type");this._myIndex=t||Ji(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(e){Bi(e)&&(this._docs.push(e),this._myIndex.add(e))}remove(e=()=>!1){const t=[];for(let r=0,n=this._docs.length;r<n;r+=1){const i=this._docs[r];e(i,r)&&(this.removeAt(r),r-=1,n-=1,t.push(i))}return t}removeAt(e){this._docs.splice(e,1),this._myIndex.removeAt(e)}getIndex(){return this._myIndex}search(e,{limit:t=-1}={}){const{includeMatches:r,includeScore:n,shouldSort:i,sortFn:o,ignoreFieldNorm:a}=this.options;let s=zi(e)?zi(this._docs[0])?this._searchStringList(e):this._searchObjectList(e):this._searchLogical(e);return function(e,{ignoreFieldNorm:t=Ki.ignoreFieldNorm}){e.forEach((e=>{let r=1;e.matches.forEach((({key:e,norm:n,score:i})=>{const o=e?e.weight:null;r*=Math.pow(0===i&&o?Number.EPSILON:i,(o||1)*(t?1:n))})),e.score=r}))}(s,{ignoreFieldNorm:a}),i&&s.sort(o),Ii(t)&&t>-1&&(s=s.slice(0,t)),function(e,t,{includeMatches:r=Ki.includeMatches,includeScore:n=Ki.includeScore}={}){const i=[];return r&&i.push(wo),n&&i.push(xo),e.map((e=>{const{idx:r}=e,n={item:t[r],refIndex:r};return i.length&&i.forEach((t=>{t(e,n)})),n}))}(s,this._docs,{includeMatches:r,includeScore:n})}_searchStringList(e){const t=fo(e,this.options),{records:r}=this._myIndex,n=[];return r.forEach((({v:e,i:r,n:i})=>{if(!Bi(e))return;const{isMatch:o,score:a,indices:s}=t.searchIn(e);o&&n.push({item:e,idx:r,matches:[{score:a,value:e,norm:i,indices:s}]})})),n}_searchLogical(e){const t=bo(e,this.options),r=(e,t,n)=>{if(!e.children){const{keyId:r,searcher:i}=e,o=this._findMatches({key:this._keyStore.get(r),value:this._myIndex.getValueForItemAtKeyId(t,r),searcher:i});return o&&o.length?[{idx:n,item:t,matches:o}]:[]}const i=[];for(let o=0,a=e.children.length;o<a;o+=1){const a=e.children[o],s=r(a,t,n);if(s.length)i.push(...s);else if(e.operator===mo)return[]}return i},n=this._myIndex.records,i={},o=[];return n.forEach((({$:e,i:n})=>{if(Bi(e)){let a=r(t,e,n);a.length&&(i[n]||(i[n]={idx:n,item:e,matches:[]},o.push(i[n])),a.forEach((({matches:e})=>{i[n].matches.push(...e)})))}})),o}_searchObjectList(e){const t=fo(e,this.options),{keys:r,records:n}=this._myIndex,i=[];return n.forEach((({$:e,i:n})=>{if(!Bi(e))return;let o=[];r.forEach(((r,n)=>{o.push(...this._findMatches({key:r,value:e[n],searcher:t}))})),o.length&&i.push({idx:n,item:e,matches:o})})),i}_findMatches({key:e,value:t,searcher:r}){if(!Bi(t))return[];let n=[];if(Fi(t))t.forEach((({v:t,i,n:o})=>{if(!Bi(t))return;const{isMatch:a,score:s,indices:l}=r.searchIn(t);a&&n.push({score:s,key:e,value:t,idx:i,norm:o,indices:l})}));else{const{v:i,n:o}=t,{isMatch:a,score:s,indices:l}=r.searchIn(i);a&&n.push({score:s,key:e,value:i,norm:o,indices:l})}return n}}ko.version="7.1.0",ko.createIndex=Ji,ko.parseIndex=function(e,{getFn:t=Ki.getFn,fieldNormWeight:r=Ki.fieldNormWeight}={}){const{keys:n,records:i}=e,o=new Xi({getFn:t,fieldNormWeight:r});return o.setKeys(n),o.setIndexRecords(i),o},ko.config=Ki,ko.parseQuery=bo,function(...e){po.push(...e)}(class{constructor(e,{isCaseSensitive:t=Ki.isCaseSensitive,ignoreDiacritics:r=Ki.ignoreDiacritics,includeMatches:n=Ki.includeMatches,minMatchCharLength:i=Ki.minMatchCharLength,ignoreLocation:o=Ki.ignoreLocation,findAllMatches:a=Ki.findAllMatches,location:s=Ki.location,threshold:l=Ki.threshold,distance:c=Ki.distance}={}){this.query=null,this.options={isCaseSensitive:t,ignoreDiacritics:r,includeMatches:n,minMatchCharLength:i,findAllMatches:a,ignoreLocation:o,location:s,threshold:l,distance:c},e=t?e:e.toLowerCase(),e=r?ro(e):e,this.pattern=e,this.query=function(e,t={}){return e.split("|").map((e=>{let r=e.trim().split(uo).filter((e=>e&&!!e.trim())),n=[];for(let e=0,i=r.length;e<i;e+=1){const i=r[e];let o=!1,a=-1;for(;!o&&++a<co;){const e=lo[a];let r=e.isMultiMatch(i);r&&(n.push(new e(r,t)),o=!0)}if(!o)for(a=-1;++a<co;){const e=lo[a];let r=e.isSingleMatch(i);if(r){n.push(new e(r,t));break}}}return n}))}(this.pattern,this.options)}static condition(e,t){return t.useExtendedSearch}searchIn(e){const t=this.query;if(!t)return{isMatch:!1,score:1};const{includeMatches:r,isCaseSensitive:n,ignoreDiacritics:i}=this.options;e=n?e:e.toLowerCase(),e=i?ro(e):e;let o=0,a=[],s=0;for(let n=0,i=t.length;n<i;n+=1){const i=t[n];a.length=0,o=0;for(let t=0,n=i.length;t<n;t+=1){const n=i[t],{isMatch:l,indices:c,score:u}=n.search(e);if(!l){s=0,o=0,a.length=0;break}if(o+=1,s+=u,r){const e=n.constructor.type;ho.has(e)?a=[...a,...c]:a.push(c)}}if(o){let e={isMatch:!0,score:s/o};return r&&(e.indices=a),e}}return{isMatch:!1,score:1}}});var Eo=function(){return Eo=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},Eo.apply(this,arguments)},Co=function(e){var t=e.products,r=e.searchTerm,i=(0,n.useMemo)((function(){return{keys:[{name:"name",weight:2},{name:"sections.description",weight:1},{name:"categories",weight:1}],threshold:.3,includeMatches:!0,minMatchCharLength:3}}),[]),o=(0,n.useMemo)((function(){return new ko(Object.values(t),i)}),[t,i]),a=(0,n.useMemo)((function(){return r?Object.fromEntries(o.search(r).map((function(e){var t=e.item,r=e.matches;return[t.plugin_file,Eo(Eo({},t),{matches:r})]}))):t}),[r,t,o]);return{results:a,highlight:function(e,t,r){if(!t)return e;var n=t.filter((function(e){return"description"===r?"sections.description"===e.key:e.key===r}));if(!n.length)return e;var i=n.flatMap((function(e){return e.indices})).sort((function(e,t){return e[0]-t[0]})),o=e,a=0;return i.forEach((function(e){var t=e[0],r=e[1],n=o.slice(t+a,r+a+1),i='<mark class="spellbook-app__search-highlight">'.concat(n,"</mark>");o=o.slice(0,t+a)+i+o.slice(r+a+1),a+=i.length-n.length})),o}}},jo=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Oo="undefined"==typeof window||"Deno"in globalThis;function _o(){}function So(e){return"number"==typeof e&&e>=0&&e!==1/0}function Ao(e,t){return Math.max(e+(t||0)-Date.now(),0)}function Po(e,t){return"function"==typeof e?e(t):e}function Mo(e,t){return"function"==typeof e?e(t):e}function Ro(e,t){const{type:r="all",exact:n,fetchStatus:i,predicate:o,queryKey:a,stale:s}=e;if(a)if(n){if(t.queryHash!==Lo(a,t.options))return!1}else if(!Do(t.queryKey,a))return!1;if("all"!==r){const e=t.isActive();if("active"===r&&!e)return!1;if("inactive"===r&&e)return!1}return!("boolean"==typeof s&&t.isStale()!==s||i&&i!==t.state.fetchStatus||o&&!o(t))}function No(e,t){const{exact:r,status:n,predicate:i,mutationKey:o}=e;if(o){if(!t.options.mutationKey)return!1;if(r){if(To(t.options.mutationKey)!==To(o))return!1}else if(!Do(t.options.mutationKey,o))return!1}return!(n&&t.state.status!==n||i&&!i(t))}function Lo(e,t){return(t?.queryKeyHashFn||To)(e)}function To(e){return JSON.stringify(e,((e,t)=>$o(t)?Object.keys(t).sort().reduce(((e,r)=>(e[r]=t[r],e)),{}):t))}function Do(e,t){return e===t||typeof e==typeof t&&!(!e||!t||"object"!=typeof e||"object"!=typeof t)&&Object.keys(t).every((r=>Do(e[r],t[r])))}function Fo(e,t){if(e===t)return e;const r=Io(e)&&Io(t);if(r||$o(e)&&$o(t)){const n=r?e:Object.keys(e),i=n.length,o=r?t:Object.keys(t),a=o.length,s=r?[]:{};let l=0;for(let i=0;i<a;i++){const a=r?i:o[i];(!r&&n.includes(a)||r)&&void 0===e[a]&&void 0===t[a]?(s[a]=void 0,l++):(s[a]=Fo(e[a],t[a]),s[a]===e[a]&&void 0!==e[a]&&l++)}return i===a&&l===i?e:s}return t}function zo(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(e[r]!==t[r])return!1;return!0}function Io(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function $o(e){if(!Bo(e))return!1;const t=e.constructor;if(void 0===t)return!0;const r=t.prototype;return!!Bo(r)&&!!r.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype}function Bo(e){return"[object Object]"===Object.prototype.toString.call(e)}function Wo(e,t,r){return"function"==typeof r.structuralSharing?r.structuralSharing(e,t):!1!==r.structuralSharing?Fo(e,t):t}function qo(e,t,r=0){const n=[...e,t];return r&&n.length>r?n.slice(1):n}function Ho(e,t,r=0){const n=[t,...e];return r&&n.length>r?n.slice(0,-1):n}var Uo=Symbol();function Qo(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==Uo?e.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`))}var Vo=new class extends jo{#e;#t;#r;constructor(){super(),this.#r=e=>{if(!Oo&&window.addEventListener){const t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e((e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()}))}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){const e=this.isFocused();this.listeners.forEach((t=>{t(e)}))}isFocused(){return"boolean"==typeof this.#e?this.#e:"hidden"!==globalThis.document?.visibilityState}},Yo=e=>setTimeout(e,0),Ko=function(){let e=[],t=0,r=e=>{e()},n=e=>{e()},i=Yo;const o=n=>{t?e.push(n):i((()=>{r(n)}))};return{batch:o=>{let a;t++;try{a=o()}finally{t--,t||(()=>{const t=e;e=[],t.length&&i((()=>{n((()=>{t.forEach((e=>{r(e)}))}))}))})()}return a},batchCalls:e=>(...t)=>{o((()=>{e(...t)}))},schedule:o,setNotifyFunction:e=>{r=e},setBatchNotifyFunction:e=>{n=e},setScheduler:e=>{i=e}}}(),Go=new class extends jo{#n=!0;#t;#r;constructor(){super(),this.#r=e=>{if(!Oo&&window.addEventListener){const t=()=>e(!0),r=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",r)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#n!==e&&(this.#n=e,this.listeners.forEach((t=>{t(e)})))}isOnline(){return this.#n}};function Xo(){let e,t;const r=new Promise(((r,n)=>{e=r,t=n}));function n(e){Object.assign(r,e),delete r.resolve,delete r.reject}return r.status="pending",r.catch((()=>{})),r.resolve=t=>{n({status:"fulfilled",value:t}),e(t)},r.reject=e=>{n({status:"rejected",reason:e}),t(e)},r}function Jo(e){return Math.min(1e3*2**e,3e4)}function Zo(e){return"online"!==(e??"online")||Go.isOnline()}var ea=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function ta(e){return e instanceof ea}function ra(e){let t,r=!1,n=0,i=!1;const o=Xo(),a=()=>Vo.isFocused()&&("always"===e.networkMode||Go.isOnline())&&e.canRun(),s=()=>Zo(e.networkMode)&&e.canRun(),l=r=>{i||(i=!0,e.onSuccess?.(r),t?.(),o.resolve(r))},c=r=>{i||(i=!0,e.onError?.(r),t?.(),o.reject(r))},u=()=>new Promise((r=>{t=e=>{(i||a())&&r(e)},e.onPause?.()})).then((()=>{t=void 0,i||e.onContinue?.()})),d=()=>{if(i)return;let t;const o=0===n?e.initialPromise:void 0;try{t=o??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(l).catch((t=>{if(i)return;const o=e.retry??(Oo?0:3),s=e.retryDelay??Jo,l="function"==typeof s?s(n,t):s,h=!0===o||"number"==typeof o&&n<o||"function"==typeof o&&o(n,t);var p;!r&&h?(n++,e.onFail?.(n,t),(p=l,new Promise((e=>{setTimeout(e,p)}))).then((()=>a()?void 0:u())).then((()=>{r?c(t):d()}))):c(t)}))};return{promise:o,cancel:t=>{i||(c(new ea(t)),e.abort?.())},continue:()=>(t?.(),o),cancelRetry:()=>{r=!0},continueRetry:()=>{r=!1},canStart:s,start:()=>(s()?d():u().then(d),o)}}var na=class{#i;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),So(this.gcTime)&&(this.#i=setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(Oo?1/0:3e5))}clearGcTimeout(){this.#i&&(clearTimeout(this.#i),this.#i=void 0)}},ia=class extends na{#o;#a;#s;#l;#c;#u;#d;constructor(e){super(),this.#d=!1,this.#u=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#l=e.client,this.#s=this.#l.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#o=function(e){const t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,n=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?n??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#o,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#c?.promise}setOptions(e){this.options={...this.#u,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#s.remove(this)}setData(e,t){const r=Wo(this.state.data,e,this.options);return this.#h({data:r,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),r}setState(e,t){this.#h({type:"setState",state:e,setStateOptions:t})}cancel(e){const t=this.#c?.promise;return this.#c?.cancel(e),t?t.then(_o).catch(_o):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#o)}isActive(){return this.observers.some((e=>!1!==Mo(e.options.enabled,this)))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===Uo||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some((e=>e.getCurrentResult().isStale)):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!Ao(this.state.dataUpdatedAt,e)}onFocus(){const e=this.observers.find((e=>e.shouldFetchOnWindowFocus()));e?.refetch({cancelRefetch:!1}),this.#c?.continue()}onOnline(){const e=this.observers.find((e=>e.shouldFetchOnReconnect()));e?.refetch({cancelRefetch:!1}),this.#c?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#s.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter((t=>t!==e)),this.observers.length||(this.#c&&(this.#d?this.#c.cancel({revert:!0}):this.#c.cancelRetry()),this.scheduleGc()),this.#s.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#h({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#c)return this.#c.continueRetry(),this.#c.promise;if(e&&this.setOptions(e),!this.options.queryFn){const e=this.observers.find((e=>e.options.queryFn));e&&this.setOptions(e.options)}const r=new AbortController,n=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#d=!0,r.signal)})},i={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#l,state:this.state,fetchFn:()=>{const e=Qo(this.options,t),r={client:this.#l,queryKey:this.queryKey,meta:this.meta};return n(r),this.#d=!1,this.options.persister?this.options.persister(e,r,this):e(r)}};n(i),this.options.behavior?.onFetch(i,this),this.#a=this.state,"idle"!==this.state.fetchStatus&&this.state.fetchMeta===i.fetchOptions?.meta||this.#h({type:"fetch",meta:i.fetchOptions?.meta});const o=e=>{ta(e)&&e.silent||this.#h({type:"error",error:e}),ta(e)||(this.#s.config.onError?.(e,this),this.#s.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#c=ra({initialPromise:t?.initialPromise,fn:i.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{if(void 0!==e){try{this.setData(e)}catch(e){return void o(e)}this.#s.config.onSuccess?.(e,this),this.#s.config.onSettled?.(e,this.state.error,this),this.scheduleGc()}else o(new Error(`${this.queryHash} data is undefined`))},onError:o,onFail:(e,t)=>{this.#h({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#h({type:"pause"})},onContinue:()=>{this.#h({type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0}),this.#c.start()}#h(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,...oa(t.data,this.options),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const r=e.error;return ta(r)&&r.revert&&this.#a?{...this.#a,fetchStatus:"idle"}:{...t,error:r,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),Ko.batch((()=>{this.observers.forEach((e=>{e.onQueryUpdate()})),this.#s.notify({query:this,type:"updated",action:e})}))}};function oa(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Zo(t.networkMode)?"fetching":"paused",...void 0===e&&{error:null,status:"pending"}}}var aa=class extends jo{constructor(e,t){super(),this.options=t,this.#l=e,this.#p=null,this.#f=Xo(),this.options.experimental_prefetchInRender||this.#f.reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#l;#m=void 0;#g=void 0;#y=void 0;#v;#b;#f;#p;#w;#x;#k;#E;#C;#j;#O=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#m.addObserver(this),sa(this.#m,this.options)?this.#_():this.updateResult(),this.#S())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return la(this.#m,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return la(this.#m,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#A(),this.#P(),this.#m.removeObserver(this)}setOptions(e){const t=this.options,r=this.#m;if(this.options=this.#l.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof Mo(this.options.enabled,this.#m))throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#M(),this.#m.setOptions(this.options),t._defaulted&&!zo(this.options,t)&&this.#l.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#m,observer:this});const n=this.hasListeners();n&&ca(this.#m,r,this.options,t)&&this.#_(),this.updateResult(),!n||this.#m===r&&Mo(this.options.enabled,this.#m)===Mo(t.enabled,this.#m)&&Po(this.options.staleTime,this.#m)===Po(t.staleTime,this.#m)||this.#R();const i=this.#N();!n||this.#m===r&&Mo(this.options.enabled,this.#m)===Mo(t.enabled,this.#m)&&i===this.#j||this.#L(i)}getOptimisticResult(e){const t=this.#l.getQueryCache().build(this.#l,e),r=this.createResult(t,e);return n=r,!zo(this.getCurrentResult(),n)&&(this.#y=r,this.#b=this.options,this.#v=this.#m.state),r;var n}getCurrentResult(){return this.#y}trackResult(e,t){return new Proxy(e,{get:(e,r)=>(this.trackProp(r),t?.(r),Reflect.get(e,r))})}trackProp(e){this.#O.add(e)}getCurrentQuery(){return this.#m}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){const t=this.#l.defaultQueryOptions(e),r=this.#l.getQueryCache().build(this.#l,t);return r.fetch().then((()=>this.createResult(r,t)))}fetch(e){return this.#_({...e,cancelRefetch:e.cancelRefetch??!0}).then((()=>(this.updateResult(),this.#y)))}#_(e){this.#M();let t=this.#m.fetch(this.options,e);return e?.throwOnError||(t=t.catch(_o)),t}#R(){this.#A();const e=Po(this.options.staleTime,this.#m);if(Oo||this.#y.isStale||!So(e))return;const t=Ao(this.#y.dataUpdatedAt,e)+1;this.#E=setTimeout((()=>{this.#y.isStale||this.updateResult()}),t)}#N(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#m):this.options.refetchInterval)??!1}#L(e){this.#P(),this.#j=e,!Oo&&!1!==Mo(this.options.enabled,this.#m)&&So(this.#j)&&0!==this.#j&&(this.#C=setInterval((()=>{(this.options.refetchIntervalInBackground||Vo.isFocused())&&this.#_()}),this.#j))}#S(){this.#R(),this.#L(this.#N())}#A(){this.#E&&(clearTimeout(this.#E),this.#E=void 0)}#P(){this.#C&&(clearInterval(this.#C),this.#C=void 0)}createResult(e,t){const r=this.#m,n=this.options,i=this.#y,o=this.#v,a=this.#b,s=e!==r?e.state:this.#g,{state:l}=e;let c,u={...l},d=!1;if(t._optimisticResults){const i=this.hasListeners(),o=!i&&sa(e,t),a=i&&ca(e,r,t,n);(o||a)&&(u={...u,...oa(l.data,e.options)}),"isRestoring"===t._optimisticResults&&(u.fetchStatus="idle")}let{error:h,errorUpdatedAt:p,status:f}=u;c=u.data;let m=!1;if(void 0!==t.placeholderData&&void 0===c&&"pending"===f){let e;i?.isPlaceholderData&&t.placeholderData===a?.placeholderData?(e=i.data,m=!0):e="function"==typeof t.placeholderData?t.placeholderData(this.#k?.state.data,this.#k):t.placeholderData,void 0!==e&&(f="success",c=Wo(i?.data,e,t),d=!0)}if(t.select&&void 0!==c&&!m)if(i&&c===o?.data&&t.select===this.#w)c=this.#x;else try{this.#w=t.select,c=t.select(c),c=Wo(i?.data,c,t),this.#x=c,this.#p=null}catch(e){this.#p=e}this.#p&&(h=this.#p,c=this.#x,p=Date.now(),f="error");const g="fetching"===u.fetchStatus,y="pending"===f,v="error"===f,b=y&&g,w=void 0!==c,x={status:f,fetchStatus:u.fetchStatus,isPending:y,isSuccess:"success"===f,isError:v,isInitialLoading:b,isLoading:b,data:c,dataUpdatedAt:u.dataUpdatedAt,error:h,errorUpdatedAt:p,failureCount:u.fetchFailureCount,failureReason:u.fetchFailureReason,errorUpdateCount:u.errorUpdateCount,isFetched:u.dataUpdateCount>0||u.errorUpdateCount>0,isFetchedAfterMount:u.dataUpdateCount>s.dataUpdateCount||u.errorUpdateCount>s.errorUpdateCount,isFetching:g,isRefetching:g&&!y,isLoadingError:v&&!w,isPaused:"paused"===u.fetchStatus,isPlaceholderData:d,isRefetchError:v&&w,isStale:ua(e,t),refetch:this.refetch,promise:this.#f};if(this.options.experimental_prefetchInRender){const t=e=>{"error"===x.status?e.reject(x.error):void 0!==x.data&&e.resolve(x.data)},n=()=>{const e=this.#f=x.promise=Xo();t(e)},i=this.#f;switch(i.status){case"pending":e.queryHash===r.queryHash&&t(i);break;case"fulfilled":"error"!==x.status&&x.data===i.value||n();break;case"rejected":"error"===x.status&&x.error===i.reason||n()}}return x}updateResult(){const e=this.#y,t=this.createResult(this.#m,this.options);this.#v=this.#m.state,this.#b=this.options,void 0!==this.#v.data&&(this.#k=this.#m),zo(t,e)||(this.#y=t,this.#T({listeners:(()=>{if(!e)return!0;const{notifyOnChangeProps:t}=this.options,r="function"==typeof t?t():t;if("all"===r||!r&&!this.#O.size)return!0;const n=new Set(r??this.#O);return this.options.throwOnError&&n.add("error"),Object.keys(this.#y).some((t=>{const r=t;return this.#y[r]!==e[r]&&n.has(r)}))})()}))}#M(){const e=this.#l.getQueryCache().build(this.#l,this.options);if(e===this.#m)return;const t=this.#m;this.#m=e,this.#g=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#S()}#T(e){Ko.batch((()=>{e.listeners&&this.listeners.forEach((e=>{e(this.#y)})),this.#l.getQueryCache().notify({query:this.#m,type:"observerResultsUpdated"})}))}};function sa(e,t){return function(e,t){return!1!==Mo(t.enabled,e)&&void 0===e.state.data&&!("error"===e.state.status&&!1===t.retryOnMount)}(e,t)||void 0!==e.state.data&&la(e,t,t.refetchOnMount)}function la(e,t,r){if(!1!==Mo(t.enabled,e)){const n="function"==typeof r?r(e):r;return"always"===n||!1!==n&&ua(e,t)}return!1}function ca(e,t,r,n){return(e!==t||!1===Mo(n.enabled,e))&&(!r.suspense||"error"!==e.state.status)&&ua(e,r)}function ua(e,t){return!1!==Mo(t.enabled,e)&&e.isStaleByTime(Po(t.staleTime,e))}var da=n.createContext(void 0),ha=e=>{const t=n.useContext(da);if(e)return e;if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},pa=({client:e,children:r})=>(n.useEffect((()=>(e.mount(),()=>{e.unmount()})),[e]),(0,t.jsx)(da.Provider,{value:e,children:r}));var fa=n.createContext(function(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}());function ma(e,t){return"function"==typeof e?e(...t):!!e}function ga(){}var ya=n.createContext(!1),va=(ya.Provider,(e,t,r)=>t.fetchOptimistic(e).catch((()=>{r.clearReset()})));function ba(e,t){return function(e,t,r){const i=ha(r),o=n.useContext(ya),a=n.useContext(fa),s=i.defaultQueryOptions(e);i.getDefaultOptions().queries?._experimental_beforeQuery?.(s),s._optimisticResults=o?"isRestoring":"optimistic",(e=>{const t=e.staleTime;e.suspense&&(e.staleTime="function"==typeof t?(...e)=>Math.max(t(...e),1e3):Math.max(t??1e3,1e3),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3)))})(s),((e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))})(s,a),(e=>{n.useEffect((()=>{e.clearReset()}),[e])})(a);const l=!i.getQueryCache().get(s.queryHash),[c]=n.useState((()=>new t(i,s))),u=c.getOptimisticResult(s),d=!o&&!1!==e.subscribed;if(n.useSyncExternalStore(n.useCallback((e=>{const t=d?c.subscribe(Ko.batchCalls(e)):ga;return c.updateResult(),t}),[c,d]),(()=>c.getCurrentResult()),(()=>c.getCurrentResult())),n.useEffect((()=>{c.setOptions(s)}),[s,c]),((e,t)=>e?.suspense&&t.isPending)(s,u))throw va(s,c,a);if((({result:e,errorResetBoundary:t,throwOnError:r,query:n,suspense:i})=>e.isError&&!t.isReset()&&!e.isFetching&&n&&(i&&void 0===e.data||ma(r,[e.error,n])))({result:u,errorResetBoundary:a,throwOnError:s.throwOnError,query:i.getQueryCache().get(s.queryHash),suspense:s.suspense}))throw u.error;if(i.getDefaultOptions().queries?._experimental_afterQuery?.(s,u),s.experimental_prefetchInRender&&!Oo&&((e,t)=>e.isLoading&&e.isFetching&&!t)(u,o)){const e=l?va(s,c,a):i.getQueryCache().get(s.queryHash)?.promise;e?.catch(ga).finally((()=>{c.updateResult()}))}return s.notifyOnChangeProps?u:c.trackResult(u)}(e,aa,t)}var wa=class extends na{#D;#F;#c;constructor(e){super(),this.mutationId=e.mutationId,this.#F=e.mutationCache,this.#D=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#D.includes(e)||(this.#D.push(e),this.clearGcTimeout(),this.#F.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#D=this.#D.filter((t=>t!==e)),this.scheduleGc(),this.#F.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#D.length||("pending"===this.state.status?this.scheduleGc():this.#F.remove(this))}continue(){return this.#c?.continue()??this.execute(this.state.variables)}async execute(e){const t=()=>{this.#h({type:"continue"})};this.#c=ra({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(e,t)=>{this.#h({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#h({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#F.canRun(this)});const r="pending"===this.state.status,n=!this.#c.canStart();try{if(r)t();else{this.#h({type:"pending",variables:e,isPaused:n}),await(this.#F.config.onMutate?.(e,this));const t=await(this.options.onMutate?.(e));t!==this.state.context&&this.#h({type:"pending",context:t,variables:e,isPaused:n})}const i=await this.#c.start();return await(this.#F.config.onSuccess?.(i,e,this.state.context,this)),await(this.options.onSuccess?.(i,e,this.state.context)),await(this.#F.config.onSettled?.(i,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(i,null,e,this.state.context)),this.#h({type:"success",data:i}),i}catch(t){try{throw await(this.#F.config.onError?.(t,e,this.state.context,this)),await(this.options.onError?.(t,e,this.state.context)),await(this.#F.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,t,e,this.state.context)),t}finally{this.#h({type:"error",error:t})}}finally{this.#F.runNext(this)}}#h(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),Ko.batch((()=>{this.#D.forEach((t=>{t.onMutationUpdate(e)})),this.#F.notify({mutation:this,type:"updated",action:e})}))}},xa=class extends jo{#l;#y=void 0;#z;#I;constructor(e,t){super(),this.#l=e,this.setOptions(t),this.bindMethods(),this.#$()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){const t=this.options;this.options=this.#l.defaultMutationOptions(e),zo(this.options,t)||this.#l.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#z,observer:this}),t?.mutationKey&&this.options.mutationKey&&To(t.mutationKey)!==To(this.options.mutationKey)?this.reset():"pending"===this.#z?.state.status&&this.#z.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#z?.removeObserver(this)}onMutationUpdate(e){this.#$(),this.#T(e)}getCurrentResult(){return this.#y}reset(){this.#z?.removeObserver(this),this.#z=void 0,this.#$(),this.#T()}mutate(e,t){return this.#I=t,this.#z?.removeObserver(this),this.#z=this.#l.getMutationCache().build(this.#l,this.options),this.#z.addObserver(this),this.#z.execute(e)}#$(){const e=this.#z?.state??{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0};this.#y={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#T(e){Ko.batch((()=>{if(this.#I&&this.hasListeners()){const t=this.#y.variables,r=this.#y.context;"success"===e?.type?(this.#I.onSuccess?.(e.data,t,r),this.#I.onSettled?.(e.data,null,t,r)):"error"===e?.type&&(this.#I.onError?.(e.error,t,r),this.#I.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach((e=>{e(this.#y)}))}))}};function ka(e,t){const r=ha(t),[i]=n.useState((()=>new xa(r,e)));n.useEffect((()=>{i.setOptions(e)}),[i,e]);const o=n.useSyncExternalStore(n.useCallback((e=>i.subscribe(Ko.batchCalls(e))),[i]),(()=>i.getCurrentResult()),(()=>i.getCurrentResult())),a=n.useCallback(((e,t)=>{i.mutate(e,t).catch(ga)}),[i]);if(o.error&&ma(i.options.throwOnError,[o.error]))throw o.error;return{...o,mutate:a,mutateAsync:o.mutate}}const Ea=window.wp.apiFetch;var Ca=r.n(Ea);const ja=e=>{let t;const r=new Set,n=(e,n)=>{const i="function"==typeof e?e(t):e;if(!Object.is(i,t)){const e=t;t=(null!=n?n:"object"!=typeof i||null===i)?i:Object.assign({},t,i),r.forEach((r=>r(t,e)))}},i=()=>t,o={setState:n,getState:i,getInitialState:()=>a,subscribe:e=>(r.add(e),()=>r.delete(e))},a=t=e(n,i,o);return o},Oa=e=>e,_a=e=>{const t=(e=>e?ja(e):ja)(e),r=e=>function(e,t=Oa){const r=n.useSyncExternalStore(e.subscribe,(()=>t(e.getState())),(()=>t(e.getInitialState())));return n.useDebugValue(r),r}(t,e);return Object.assign(r,t),r};var Sa,Aa=function(e,t,r){if(r||2===arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))};const Pa=(Sa=function(e){return{notifications:[],showNotification:function(t,r){var n={id:Date.now(),message:t,type:r};e((function(e){return{notifications:Aa(Aa([],e.notifications,!0),[n],!1)}}))},removeNotification:function(t){e((function(e){return{notifications:e.notifications.filter((function(e){return e.id!==t}))}}))},forceLicenseRefresh:!1,forceProductRefresh:!1,setForceLicenseRefresh:function(t){return e({forceLicenseRefresh:t})},setForceProductRefresh:function(t){return e({forceProductRefresh:t})}}})?_a(Sa):_a;var Ma=function(){return Ma=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},Ma.apply(this,arguments)},Ra=function(e,t,r,n){return new(r||(r=Promise))((function(i,o){function a(e){try{l(n.next(e))}catch(e){o(e)}}function s(e){try{l(n.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((n=n.apply(e,t||[])).next())}))},Na=function(e,t){var r,n,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=s(0),a.throw=s(1),a.return=s(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;a&&(a=0,s[0]&&(o=0)),o;)try{if(r=1,n&&(i=2&s[0]?n.return:s[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,s[1])).done)return i;switch(n=0,i&&(s=[2&s[0],i.value]),s[0]){case 0:case 1:i=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,n=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!((i=(i=o.trys).length>0&&i[i.length-1])||6!==s[0]&&2!==s[0])){o=0;continue}if(3===s[0]&&(!i||s[1]>i[0]&&s[1]<i[3])){o.label=s[1];break}if(6===s[0]&&o.label<i[1]){o.label=i[1],i=s;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(s);break}i[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e],n=0}finally{r=i=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}},La=function(){var e=Pa((function(e){return e.forceLicenseRefresh}));return ba({queryKey:["licenses"],queryFn:function(t){return Ra(void 0,[t],void 0,(function(t){var r=t.signal;return Na(this,(function(t){return[2,Ca()({path:"/gwiz/v1/license".concat(e?"?force=1":""),signal:r})]}))}))},staleTime:1/0})},Ta=function(e){var t,r=La();return{data:null===(t=r.data)||void 0===t?void 0:t[e],isLoading:r.isLoading,error:r.error}},Da=function(e){var t=ha(),r=function(r,n){void 0===n&&(n=!1),t.setQueryData(["licenses"],(function(t){var i;if(!t)return t;if(null===r){var o=t,a=e;return o[a],function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(n=Object.getOwnPropertySymbols(e);i<n.length;i++)t.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]])}return r}(o,["symbol"==typeof a?a:a+""])}return Ma(Ma({},t),((i={})[e]=n?r:Ma(Ma({},t[e]),r),i))}))};return{validate:ka({mutationFn:function(t){return Ca()({path:"/gwiz/v1/license/".concat(e,"/validate"),method:"POST",data:{license_key:t}})},onSuccess:function(e){r(e.license_data),t.invalidateQueries({queryKey:["products"]})},onError:function(){}}),register:ka({mutationFn:function(t){return Ca()({path:"/gwiz/v1/license/".concat(e,"/products/").concat(t,"/register"),method:"POST"})},onSuccess:function(e){r(e.license_data),e.product&&t.setQueryData(["products"],(function(t){var r,n;if(!t)return t;for(var i=0,o=Object.entries(t);i<o.length;i++)for(var a=o[i],s=a[0],l=a[1],c=0,u=Object.entries(l);c<u.length;c++){var d=u[c],h=d[0],p=d[1];if(p.ID.toString()===e.product.id)return Ma(Ma({},t),((r={})[s]=Ma(Ma({},t[s]),((n={})[h]=Ma(Ma({},p),{is_registered:!0}),n)),r))}return t}))}}),deactivate:ka({mutationFn:function(){return Ca()({path:"/gwiz/v1/license/".concat(e,"/deactivate"),method:"POST"})},onSuccess:function(e){console.log("Deactivated license:",e),r(e.license_data,!0),t.invalidateQueries({queryKey:["products"]})}})}},Fa=function(){var e=ha(),t=ba({queryKey:["spellbook","registration"],queryFn:function(){return Ca()({path:"/gwiz/v1/spellbook/registration-status"})},staleTime:1/0}),r=ka({mutationFn:function(e){return Ca()({path:"/gwiz/v1/spellbook/register",method:"POST",data:e})},onSuccess:function(t){if(!t.success)throw new Error(t.message);e.invalidateQueries({queryKey:["spellbook","registration"]}),Pa.getState().showNotification(t.message,"success")},onError:function(e){Pa.getState().showNotification(e.message,"error")}});return{registrationStatus:t.data,isLoading:t.isLoading,register:r.mutate,isPending:r.isPending}},za=function(e,t){return"gc-google-sheets/gc-google-sheets.php"===e.plugin_file?function(e){var t=null==e?void 0:e.perk;return(null==t?void 0:t.gcgs_eligible)&&t.valid?t:null==e?void 0:e.connect}(t):null==t?void 0:t[e.type]},Ia=function(e,t){if("free"===e.type)return!1;var r=za(e,t);return null!==(null==r?void 0:r.registered_products)},$a=function(e,t){var r=za(e,t);return!!(null==r?void 0:r.registered_products)&&0===r.registered_products_limit},Ba=function(e,t){var r=za(e,t);return!("gc-google-sheets/gc-google-sheets.php"!==e.plugin_file||!(null==r?void 0:r.gcgs_eligible))||!!(null==r?void 0:r.registered_products)&&(Array.isArray(r.registered_products)?r.registered_products:Object.values(r.registered_products)).includes(e.ID.toString())},Wa=function(e,t,r){var n,i,o;if(!e.is_installed)return!1;if("free"===e.type)return!((null==r?void 0:r.is_registered)||(null===(n=null==t?void 0:t.perk)||void 0===n?void 0:n.key)||(null===(i=null==t?void 0:t.connect)||void 0===i?void 0:i.key)||(null===(o=null==t?void 0:t.shop)||void 0===o?void 0:o.key));var a=za(e,t);return("gc-google-sheets/gc-google-sheets.php"!==e.plugin_file||!(null==a?void 0:a.gcgs_eligible))&&Ia(e,t)&&!$a(e,t)&&!Ba(e,t)},qa=function(e){var t=La().data,r=Fa().registrationStatus;return Wa(e,t,r)};function Ha(){return Ha=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ha.apply(null,arguments)}const{forwardRef:Ua}=i(),Qa=Ua((({children:e=null,customAttributes:t={},customClasses:r={},spacing:n="",type:o="success"},a)=>{const s={className:c()({"gform-indicator":!0,"gform-indicator--dot":!0,[`gform-indicator--${o}`]:!0,...Br(n)},r),...t};return i().createElement("span",Ha({},s,{ref:a}),e)}));Qa.propTypes={children:s().oneOfType([s().arrayOf(s().node),s().node]),customAttributes:s().object,customClasses:s().oneOfType([s().string,s().array,s().object]),spacing:s().oneOfType([s().string,s().number,s().array,s().object]),type:s().string},Qa.displayName="Indicators/DotIndicator";const Va=Qa,{forwardRef:Ya,useEffect:Ka,useRef:Ga}=i(),Xa=Ya((({children:e=null,content:t="",customAttributes:r={},customClasses:n=[],editable:o=!1,id:a="",name:s="",onChange:l=()=>{},size:u="display-3xl",spacing:d="",tagName:h="h1",type:p="regular",useHiddenInput:f=!1,weight:m="semibold"},g)=>{const y=Ga(null),v=g||y,b=Ga(null),w={className:c()({"gform-heading":!0,"gform-text":!0,[`gform-typography--size-${u}`]:!0,[`gform-typography--weight-${m}`]:!0,[`gform-heading--${p}`]:!0,...Br(d)},n),ref:v,...r};o&&(w.contentEditable=!0,w.tabIndex=0,w.role="textbox",f||(s&&(w.name=s),a&&(w.id=a)),l||f||console.warn("Heading component: when editable is true and useHiddenInput is false, onChange prop is recommended for controlled behavior.")),Ka((()=>{if(o&&v.current){const e=v.current;e.textContent!==t&&(e.textContent=t);const r=()=>{const t=e.textContent;l&&l(t),f&&b.current&&(b.current.value=t)};return e.addEventListener("input",r),()=>e.removeEventListener("input",r)}}),[o,t,l,f,v]);const x=h;return o?i().createElement(i().Fragment,null,i().createElement(x,w),f&&i().createElement("input",{type:"hidden",ref:b,name:s,id:a,defaultValue:t})):i().createElement(x,w,t,e)}));Xa.propTypes={children:s().oneOfType([s().arrayOf(s().node),s().node]),content:s().string,customAttributes:s().object,customClasses:s().oneOfType([s().string,s().array,s().object]),editable:s().bool,id:s().string,name:s().string,onChange:s().func,size:s().string,spacing:s().oneOfType([s().string,s().number,s().array,s().object]),tagName:s().string,type:s().string,useHiddenInput:s().bool,weight:s().string},Xa.displayName="Heading";const Ja=Xa;function Za(){return Za=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Za.apply(null,arguments)}const{forwardRef:es}=i(),ts=es((({afterCard:e=null,baseClass:t=!0,beforeCard:r=null,children:n=null,customAttributes:o={},customClasses:a=[],spacing:s=0,style:l=""},u)=>{const d={className:c()({"gform-card":t,[`gform-card--${l}`]:l,...Br(s)},a),...o};return i().createElement("article",Za({},d,{ref:u}),r,n,e)}));ts.propTypes={afterCard:s().node,beforeCard:s().node,children:s().oneOfType([s().arrayOf(s().node),s().node]),customAttributes:s().object,customClasses:s().oneOfType([s().string,s().array,s().object]),spacing:s().oneOfType([s().string,s().number,s().array,s().object]),style:s().string},ts.displayName="Card";const rs=ts;function ns(){return ns=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ns.apply(null,arguments)}const{forwardRef:is}=i(),os=is((({afterTitle:e=null,customAttributes:t={},customClasses:r=[],description:n=null,disabled:o=!1,footerContent:a=[],footerGridAttributes:s={},headerContent:l=[],headerGridAttributes:u={},title:d=null,titleGridAttributes:h={}},p)=>{const f={customClasses:c()({"gform-card--disabled":o},r),...t,style:"integration"},m={container:!0,customClasses:["gform-card__top-container-header"],elementType:"div",justifyContent:"space-between",spacing:2,...u},g={container:!0,customClasses:["gform-card__bottom-container-footer"],elementType:"div",justifyContent:"space-between",type:1===a.length?"fluid":"fixed",...s},y={columnSpacing:2,container:!0,customClasses:["gform-card__top-container-title-container"],elementType:"div",justifyContent:"flex-start",rowSpacing:2,wrap:!0,...h};return i().createElement(rs,ns({},f,{ref:p}),i().createElement("div",{className:"gform-card__top-container"},!!l.length&&i().createElement(Ni,m,l.map(((e,t)=>i().createElement(Ni,{item:!0,customClasses:["gform-card__top-container-header-item"],elementType:"div",key:`integration-card__top-container-header-item--${Dr(d)}-${t}`},e)))),(d||e)&&i().createElement(Di,{customClasses:["gform-card__top-container-title-outer-container"],spacing:2},i().createElement(Ni,y,d&&i().createElement(Ni,{item:!0,customClasses:["gform-card__top-container-title"],elementType:"div"},d),e&&i().createElement(Ni,{item:!0,customClasses:["gform-card__top-container-after-title"],elementType:"div"},e))),n),i().createElement("div",{className:"gform-card__bottom-container"},!!a.length&&i().createElement(Ni,g,a.map(((e,t)=>i().createElement(Ni,{item:!0,elementType:"div",key:`integration-card__bottom-container-footer-item--${Dr(d)}-${t}`},e))))))}));os.propTypes={afterTitle:s().node,customAttributes:s().object,customClasses:s().oneOfType([s().string,s().array,s().object]),description:s().node,footerContent:s().arrayOf(s().node),footerGridAttributes:s().object,headerContent:s().arrayOf(s().node),headerGridAttributes:s().object,title:s().node,titleGridAttributes:s().object},os.displayName="Cards/IntegrationCard";const as=os,{forwardRef:ss}=i(),ls=ss((({children:e=null,content:t="",customAttributes:r={},customClasses:n=[],size:o="text-xs",spacing:a="",triangleTag:s=!1,triangleTagDirection:l="left",type:u="chathams",weight:d="semibold"},h)=>{const p={className:c()({"gform-tag":!0,[`gform-tag--type-${u}`]:!0,[`gform-typography--size-${o}`]:!0,[`gform-typography--weight-${d}`]:!0,[`gform-tag--triangle-${l}`]:s,...Br(a)},n),ref:h,...r};return i().createElement("span",p,s&&i().createElement("span",{className:"gform-tag__triangle"}),t,e)}));ls.propTypes={children:s().oneOfType([s().arrayOf(s().node),s().node]),content:s().string,customAttributes:s().object,customClasses:s().oneOfType([s().string,s().array,s().object]),size:s().string,spacing:s().oneOfType([s().string,s().number,s().array,s().object]),triangleTag:s().bool,triangleTagDirection:s().string,type:s().string,weight:s().string},ls.displayName="Tag";const cs=ls,{forwardRef:us,useEffect:ds,useRef:hs}=i(),ps=us((({asHtml:e=!1,children:t=null,color:r="port",content:n="",customAttributes:o={},customClasses:a=[],editable:s=!1,id:l="",name:u="",onChange:d=()=>{},size:h="text-md",spacing:p="",tagName:f="div",useHiddenInput:m=!1,weight:g="regular"},y)=>{const v=e&&!s,b=hs(null),w=y||b,x=hs(null),k={className:c()({"gform-text":!0,[`gform-text--color-${r}`]:!0,[`gform-typography--size-${h}`]:!0,[`gform-typography--weight-${g}`]:!0,...Br(p)},a),ref:w,...o};s&&(k.contentEditable=!0,k.tabIndex=0,k.role="textbox",m||(u&&(k.name=u),l&&(k.id=l)),d||m||console.warn("Text component: when editable is true and useHiddenInput is false, onChange prop is recommended for controlled behavior.")),v&&(k.dangerouslySetInnerHTML={__html:n}),ds((()=>{if(s&&w.current){const e=w.current;e.textContent!==n&&(e.textContent=n);const t=()=>{const t=e.textContent;d&&d(t),m&&x.current&&(x.current.value=t)};return e.addEventListener("input",t),()=>e.removeEventListener("input",t)}}),[s,n,d,m,w]);const E=f;return v?i().createElement(E,k):s?i().createElement(i().Fragment,null,i().createElement(E,k),m&&i().createElement("input",{type:"hidden",ref:x,name:u,id:l,defaultValue:n})):i().createElement(E,k,n,t)}));ps.propTypes={asHtml:s().bool,children:s().oneOfType([s().arrayOf(s().node),s().node]),color:s().string,content:s().string,customAttributes:s().object,customClasses:s().oneOfType([s().string,s().array,s().object]),editable:s().bool,id:s().string,name:s().string,onChange:s().func,size:s().string,spacing:s().oneOfType([s().string,s().number,s().array,s().object]),tagName:s().string,useHiddenInput:s().bool,weight:s().string},ps.displayName="Text";const fs=ps,{forwardRef:ms}=i(),gs=ms((({asHtml:e=!1,content:t="",customAttributes:r={},customClasses:n=[],id:o="",size:a="text-xs",spacing:s="",weight:l="regular"},u)=>{if(!t)return null;const d={className:c()({"gform-input-help-text":!0,[`gform-typography--size-${a}`]:!0,[`gform-typography--weight-${l}`]:!0,...Br(s)},n),id:o,ref:u,...r};return e&&(d.dangerouslySetInnerHTML={__html:t}),e?i().createElement("span",d):i().createElement("span",d,t)}));gs.propTypes={content:s().oneOfType([s().string,s().node]),customAttributes:s().object,customClasses:s().oneOfType([s().string,s().array,s().object]),id:s().string,size:s().string,spacing:s().oneOfType([s().string,s().number,s().array,s().object]),weight:s().string},gs.displayName="HelpText";const ys=gs;function vs(){return vs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},vs.apply(null,arguments)}const{useEffect:bs,useRef:ws,useState:xs,forwardRef:ks}=i(),Es=14,Cs=ks((({buffer:e=0,children:t=null,content:r="",contentAttributes:n={},customAttributes:o={},customClasses:a=[],icon:s="question-mark",iconPrefix:l="gravity-component-icon",iconPreset:u="",intentDelay:d=500,id:h="",maxWidth:p=0,position:f="top",spacing:m="",tagName:g="div",theme:y="chathams",tooltipCustomAttributes:v={},type:b="default"},w)=>{const x=h||Fr("tooltip"),k=ws(),[E,C]=xs(0),[j,O]=xs(!1),[_,S]=xs(!1),[A,P]=xs(!1),[M,R]=xs("top"),N=()=>{O(!0),k.current&&!E&&C(k.current.offsetWidth+1)},L=()=>{O(!1),_&&P(!1),T.cancel()},T=function(e,t={}){if("function"!=typeof e)throw new TypeError(`Expected the first argument to be a function, got \`${typeof e}\``);const{wait:r=0,maxWait:n=Number.Infinity,before:i=!1,after:o=!0}=t;if(!i&&!o)throw new Error("Both `before` and `after` are false, function wouldn't be called.");let a,s,l;const c=function(...t){const c=this,u=i&&!a;return clearTimeout(a),a=setTimeout((()=>{a=void 0,s&&(clearTimeout(s),s=void 0),o&&(l=e.apply(c,t))}),r),n>0&&n!==Number.Infinity&&!s&&(s=setTimeout((()=>{s=void 0,a&&(clearTimeout(a),a=void 0),o&&(l=e.apply(c,t))}),n)),u&&(l=e.apply(c,t)),l};return function(e,t,{ignoreNonConfigurable:r=!1}={}){const{name:n}=e;for(const n of Reflect.ownKeys(t))Mr(e,t,n,r);((e,t)=>{const r=Object.getPrototypeOf(t);r!==Object.getPrototypeOf(e)&&Object.setPrototypeOf(e,r)})(e,t),((e,t,r)=>{const n=""===r?"":`with ${r.trim()}() `,i=Nr.bind(null,n,t.toString());Object.defineProperty(i,"name",Tr),Object.defineProperty(e,"toString",{...Lr,value:i})})(e,t,n)}(c,e),c.cancel=()=>{a&&(clearTimeout(a),a=void 0),s&&(clearTimeout(s),s=void 0)},c}((()=>{S(!0),R(f),requestAnimationFrame((()=>{const t=z(e,f,k);R(t),P(!0)}))}),{wait:d});bs((()=>{j&&T()}),[j]);const D=(e,t,r,n,i,o)=>{const a=e*t;let s=0;return r>0&&(s+=r*t),n>0&&(s+=n*t),i>0&&(s+=i*e),o>0&&(s+=o*e),i>0&&r>0&&(s-=i*r),i>0&&n>0&&(s-=i*n),o>0&&r>0&&(s-=o*r),o>0&&n>0&&(s-=o*n),(a-s)/a},F=(e,t,r,n,i)=>{const o=n.innerWidth,a=n.innerHeight,s=i-r.top,l=r.bottom-(a-i),c=i-r.left,u=r.right-(o-i);let d,h,p,f,m,g,y,v;switch(e){case"top":switch(t){case"top":return!0;case"bottom":d=r.bottom+44,h=d+r.height,m=i-d,g=h-(a-i),y=c,v=u;break;case"left":d=r.bottom+Es+8-r.height/2,h=d+r.height,p=r.left+r.width/2-8-Es-r.width,f=p+r.width,m=i-d,g=h-(a-i),y=i-p,v=f-(o-i);break;case"right":d=r.bottom+Es+8-r.height/2,h=d+r.height,p=r.left+r.width/2+8+Es,f=p+r.width,m=i-d,g=h-(a-i),y=i-p,v=f-(o-i);break;default:return!1}break;case"bottom":switch(t){case"top":d=r.top-44-r.height,h=d+r.height,m=i-d,g=h-(a-i),y=c,v=u;break;case"bottom":return!0;case"left":d=r.top-Es-8-r.height/2,h=d+r.height,p=r.left+r.width/2-8-Es-r.width,f=p+r.width,m=i-d,g=h-(a-i),y=i-p,v=f-(o-i);break;case"right":d=r.top-Es-8-r.height/2,h=d+r.height,p=r.left+r.width/2+8+Es,f=p+r.width,m=i-d,g=h-(a-i),y=i-p,v=f-(o-i);break;default:return!1}break;case"left":switch(t){case"top":d=r.top+r.height/2-8-Es-r.height,h=d+r.height,p=r.right+Es+8-r.width/2,f=p+r.width,m=i-d,g=h-(a-i),y=i-p,v=f-(o-i);break;case"bottom":d=r.top+r.height/2+8+Es,h=d+r.height,p=r.right+Es+8-r.width/2,f=p+r.width,m=i-d,g=h-(a-i),y=i-p,v=f-(o-i);break;case"left":return!0;case"right":p=r.right+44,f=p+r.width,m=s,g=l,y=i-p,v=f-(o-i);break;default:return!1}break;case"right":switch(t){case"top":d=r.top+r.height/2-8-Es-r.height,h=d+r.height,p=r.left-Es-8-r.width/2,f=p+r.width,m=i-d,g=h-(a-i),y=i-p,v=f-(o-i);break;case"bottom":d=r.top+r.height/2+8+Es,h=d+r.height,p=r.left-Es-8-r.width/2,f=p+r.width,m=i-d,g=h-(a-i),y=i-p,v=f-(o-i);break;case"left":p=r.left-44-r.width,f=p+r.width,m=s,g=l,y=i-p,v=f-(o-i);break;case"right":return!0;default:return!1}break;default:return!1}return D(r.width,r.height,y,v,m,g)},z=(e,t,r)=>{if(!r.current)return t;const n=r.current.getBoundingClientRect(),i=r.current.ownerDocument.defaultView,o=i.innerWidth,a=i.innerHeight,s=e-n.top,l=n.bottom-(a-e),c=e-n.left,u=n.right-(o-e),d=D(n.width,n.height,c,u,s,l),h={};switch(t){case"top":h.top=d,h.bottom=F("top","bottom",n,i,e),h.left=F("top","left",n,i,e),h.right=F("top","right",n,i,e);break;case"bottom":h.top=F("bottom","top",n,i,e),h.bottom=d,h.left=F("bottom","left",n,i,e),h.right=F("bottom","right",n,i,e);break;case"left":h.top=F("left","top",n,i,e),h.bottom=F("left","bottom",n,i,e),h.left=d,h.right=F("left","right",n,i,e);break;case"right":h.top=F("right","top",n,i,e),h.bottom=F("right","bottom",n,i,e),h.left=F("right","left",n,i,e),h.right=d;break;default:return t}return Object.keys(h).reduce(((e,t)=>h[t]>h[e]?t:e),t)},I=({con:e="",cAttributes:t={size:"text-xs"}})=>{if(!e)return null;const{customClasses:r,...n}=t,o={customClasses:c()(["gform-tooltip__tooltip-content"],r),color:"white",size:"text-xs",...n};return i().createElement(fs,o,e)},$={className:c()({"gform-tooltip":!0,[`gform-tooltip--position-${M}`]:!0,[`gform-tooltip--theme-${y}`]:!0,[`gform-tooltip--type-${b}`]:!0,"gform-tooltip--initialized":!!E,"gform-tooltip--anim-in-ready":_,"gform-tooltip--anim-in-active":_&&A,...Br(m)},a),...o},B={className:"gform-tooltip__trigger","aria-describedby":x,type:"button",onBlur:L,onFocus:N,onMouseEnter:N,onMouseLeave:L},W={className:"gform-tooltip__tooltip",role:"tooltip",id:x,onTransitionEnd:()=>{A||S(!1)},...v},q={icon:s,iconPrefix:l,preset:u},H={};E&&(H.width=E+"px"),p&&(H.maxWidth=p+"px"),W.style=H;const U=g;return i().createElement(U,vs({},$,{ref:w}),i().createElement("button",B,i().createElement(An,q)),i().createElement("div",vs({ref:k},W),i().createElement(I,{con:r,cAttributes:n}),t,i().createElement("span",{className:"gform-tooltip__tooltip-arrow"})))}));Cs.propTypes={buffer:s().number,children:s().oneOfType([s().arrayOf(s().node),s().node]),content:s().string,contentAttributes:s().object,customAttributes:s().object,customClasses:s().oneOfType([s().string,s().array,s().object]),icon:s().string,iconPrefix:s().string,iconPreset:s().string,intentDelay:s().number,id:s().string,maxWidth:s().number,position:s().string,spacing:s().oneOfType([s().string,s().number,s().array,s().object]),theme:s().string,tooltipCustomAttributes:s().object,type:s().string},Cs.displayName="Tooltip";const js=Cs,{forwardRef:Os}=i(),_s=Os((({children:e=null,customAttributes:t={},customClasses:r=[],htmlFor:n="",icon:o="lock",iconPrefix:a="gravity-component-icon",isVisible:s=!0,label:l="",locked:u=!1,lockedMessage:d="",size:h="text-sm",spacing:p="",weight:f="medium"},m)=>{if(!l&&!e)return null;const g={className:c()({"gform-label":!0,[`gform-typography--size-${h}`]:!0,[`gform-typography--weight-${f}`]:!0,"gform-visually-hidden":!s,...Br(p)},r),htmlFor:n,ref:m,...t},y={content:d,icon:o,iconPrefix:a,iconPreset:"status-locked",maxWidth:300,position:"right",spacing:[0,0,0,2],tagName:"span",theme:"port"};return i().createElement("label",g,l,e,u&&i().createElement(js,y))}));_s.propTypes={children:s().oneOfType([s().arrayOf(s().node),s().node]),customAttributes:s().object,customClasses:s().oneOfType([s().string,s().array,s().object]),htmlFor:s().string,iconPrefix:s().string,isVisible:s().bool,label:s().string,locked:s().bool,lockedMessage:s().string,size:s().string,spacing:s().oneOfType([s().string,s().number,s().array,s().object]),weight:s().string},_s.displayName="Label";const Ss=_s,{useState:As,forwardRef:Ps}=i(),Ms=Ps((({ariaLabel:e="",children:t=null,customAttributes:r={},customClasses:n=[],disabled:o=!1,externalChecked:a=!1,externalControl:s=!1,helpTextAttributes:l={},helpTextWidth:u="auto",icons:d=!0,id:h="",initialChecked:p=!1,labelAttributes:f={},labelPosition:m="right",name:g="",onBlur:y=()=>{},onChange:v=()=>{},onFocus:b=()=>{},size:w="size-s",spacing:x="",theme:k="cosmos",width:E="auto",wrapperAttributes:C={},wrapperClasses:j=[],wrapperTagName:O="div"},_)=>{const[S,A]=As(p),[P,M]=vn(a),R=h||Fr("toggle"),N=`${R}-help-text`,L={...C,className:c()({"gform-toggle":!0,[`gform-toggle--theme-${k}`]:!0,[`gform-toggle--${w}`]:!0,[`gform-toggle--label-${m}`]:!0,[`gform-toggle--width-${E}`]:!0,[`gform-toggle--help-text-width-${u}`]:l.content,"gform-toggle--disabled":o,...Br(x)},j),ref:_},T={...r,checked:s?P:S,className:c()({"gform-toggle__toggle":!0,"gform-toggle__toggle--has-icons":d},n),disabled:o||!0===f?.locked,id:R,name:g,onBlur:y,onChange:e=>{const{checked:t}=e.target;s||A(t),setTimeout((()=>{v(t)}),150)},onFocus:b,type:"checkbox"};e&&(T["aria-label"]=e),l.content&&(T["aria-describedby"]=N),l.spacing||(l.spacing=[4,0,0,0]);const D={...f,customClasses:c()(["gform-toggle__label"],f.customClasses),htmlFor:R},F={...l,id:N},z=O;return i().createElement(z,L,i().createElement("input",T),i().createElement(kr,{condition:l.content&&f.label,wrapper:e=>i().createElement("div",{className:"gform-toggle__label-wrapper"},e)},i().createElement(Ss,D),l.content&&i().createElement(ys,F),t))}));Ms.propTypes={ariaLabel:s().string,children:s().oneOfType([s().arrayOf(s().node),s().node]),customAttributes:s().object,customClasses:s().oneOfType([s().string,s().array,s().object]),disabled:s().bool,externalChecked:s().bool,externalControl:s().bool,helpTextAttributes:s().object,helpTextWidth:s().oneOf(["auto","full"]),icons:s().bool,id:s().string,initialChecked:s().bool,labelAttributes:s().object,labelPosition:s().string,name:s().string,onBlur:s().func,onChange:s().func,onFocus:s().func,size:s().string,spacing:s().oneOfType([s().string,s().number,s().array,s().object]),theme:s().string,width:s().oneOf(["auto","full"]),wrapperAttributes:s().object,wrapperClasses:s().oneOfType([s().string,s().array,s().object]),wrapperTagName:s().string},Ms.displayName="Toggle";const Rs=Ms;var Ns,Ls,Ts,Ds;function Fs(){return Fs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Fs.apply(null,arguments)}const zs=function(e){return n.createElement("svg",Fs({xmlns:"http://www.w3.org/2000/svg",width:250,height:250,fill:"none",viewBox:"0 0 250 250"},e),Ns||(Ns=n.createElement("path",{fill:"#E9D5FC",d:"M0 0h250v250H0z"})),Ls||(Ls=n.createElement("rect",{width:156,height:156,x:47,y:47,fill:"#902EEF",rx:78})),Ts||(Ts=n.createElement("g",{clipPath:"url(#4a)"},n.createElement("path",{fill:"#fff",fillRule:"evenodd",d:"M105 79.865a4 4 0 0 1 4 4v29.792h32V83.865a4 4 0 0 1 8 0v29.792h9.279a4 4 0 0 1 4 4v23.296c0 19.568-15.077 35.615-34.248 37.158v42.754a4 4 0 0 1-8 0v-42.96c-18.24-2.429-32.312-18.047-32.312-36.952v-23.296a4 4 0 0 1 4-4H101V83.865a4 4 0 0 1 4-4m-9.281 41.792v19.296c0 16.171 13.109 29.28 29.28 29.28s29.28-13.109 29.28-29.28v-19.296z",clipRule:"evenodd"}))),Ds||(Ds=n.createElement("defs",null,n.createElement("clipPath",{id:"4a"},n.createElement("path",{fill:"#fff",d:"M47 47h156v156H47z"})))))},Is=function(e){var r=e.src,i=(0,n.useRef)(null),o=(0,n.useState)("500px"),a=o[0],s=o[1],l=(0,n.useState)(!0),c=l[0],u=l[1];return(0,n.useEffect)((function(){var e=function(){var e,t;if(null===(t=null===(e=i.current)||void 0===e?void 0:e.contentWindow)||void 0===t?void 0:t.document.body){var r=i.current.contentWindow.document.body.scrollHeight;s("".concat(r,"px"))}};i.current&&(i.current.onload=function(){e(),u(!1)});var t=setInterval(e,500);return function(){return clearInterval(t)}}),[]),(0,t.jsxs)("div",{style:{minHeight:"400px",position:"relative"},children:[c&&(0,t.jsx)("div",{style:{position:"absolute",width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center",padding:"20px"},children:(0,t.jsx)(Tn,{})}),(0,t.jsx)("iframe",{ref:i,src:r,style:{width:"100%",height:a,border:"none",overflow:"hidden",visibility:c?"hidden":"visible"},scrolling:"no"})]})};var $s=function(){return $s=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},$s.apply(this,arguments)},Bs=function(e,t,r){if(r||2===arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))},Ws=function(){var e=Pa((function(e){return e.forceProductRefresh}));return ba({queryKey:["products"],queryFn:function(t){return r=void 0,n=[t],o=function(t){var r,n,i=t.signal;return function(e,t){var r,n,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=s(0),a.throw=s(1),a.return=s(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;a&&(a=0,s[0]&&(o=0)),o;)try{if(r=1,n&&(i=2&s[0]?n.return:s[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,s[1])).done)return i;switch(n=0,i&&(s=[2&s[0],i.value]),s[0]){case 0:case 1:i=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,n=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!((i=(i=o.trys).length>0&&i[i.length-1])||6!==s[0]&&2!==s[0])){o=0;continue}if(3===s[0]&&(!i||s[1]>i[0]&&s[1]<i[3])){o.label=s[1];break}if(6===s[0]&&o.label<i[1]){o.label=i[1],i=s;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(s);break}i[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e],n=0}finally{r=i=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}}(this,(function(t){switch(t.label){case 0:return[4,Ca()({path:"/gwiz/v1/products".concat(e?"?force=1":""),signal:i})];case 1:return r=t.sent(),n=r.map((function(e){var t;return $s($s({},e),{name:null===(t=e.name)||void 0===t?void 0:t.replace(/&amp;/g,"&")})})),[2,n.reduce((function(e,t){return e[t.type]||(e[t.type]={}),e[t.type][t.plugin_file]=t,e}),{})]}}))},new((i=void 0)||(i=Promise))((function(e,t){function a(e){try{l(o.next(e))}catch(e){t(e)}}function s(e){try{l(o.throw(e))}catch(e){t(e)}}function l(t){var r;t.done?e(t.value):(r=t.value,r instanceof i?r:new i((function(e){e(r)}))).then(a,s)}l((o=o.apply(r,n||[])).next())}));var r,n,i,o},staleTime:1/0})};function qs(e,t){var r=new URL(e),n=window.location.hash.slice(2),i=n?"ui-".concat(n):"ui";return r.searchParams.set("utm_campaign","spellbook-plugin"),r.searchParams.set("utm_source",i),t.component&&r.searchParams.set("utm_medium",t.component),t.text&&r.searchParams.set("utm_content",t.text),t.meta&&r.searchParams.set("utm_term",t.meta),r.toString()}var Hs=function(e){var r=e.product,i=e.licenses,o=e.mutations,a=Da(r.type).register,s=qa(r),l=(0,n.useMemo)((function(){if("free"===r.type)return!s&&r.has_update?{text:o.update.isPending?(0,jn.__)("Updating...","spellbook"):(0,jn.__)("Update","spellbook"),onClick:function(){return o.update.mutate(r)}}:null;var e=za(r,i);return r.is_legacy_free_plugin?null:(null==e?void 0:e.valid)?function(e,t){if(!e.has_update)return!1;if("free"===e.type)return!0;var r=za(e,t);return!(!(null==r?void 0:r.valid)||Ia(e,t)&&!$a(e,t)&&!Ba(e,t))}(r,i)?{text:o.update.isPending?(0,jn.__)("Updating...","spellbook"):(0,jn.__)("Update","spellbook"),onClick:function(){return o.update.mutate(r)}}:Ba(r,i)?null:function(e,t){var r=za(e,t);return!!(null==r?void 0:r.valid)&&(!(Ia(e,t)&&!$a(e,t))||r.registered_products.length<r.registered_products_limit)}(r,i)?{text:a.isPending?(0,jn.__)("Registering...","spellbook"):r.has_update?(0,jn.__)("Register to Update","spellbook"):(0,jn.__)("Register","spellbook"),onClick:function(){return a.mutate(r.ID.toString())}}:{text:r.has_update?(0,jn.__)("Upgrade License to Update","spellbook"):(0,jn.__)("Upgrade License to Register","spellbook"),onClick:function(){return window.open(qs(e.upgrade_url,{component:"product-card",text:"upgrade-license",meta:"no-capacity"}),"_blank")}}:{text:r.has_update?(0,jn.__)("Enter License to Update","spellbook"):(0,jn.__)("Enter License to Register","spellbook"),onClick:function(){return window.location.hash="/licenses#".concat(r.type)}}}),[i,s,r,a.isPending,o.update.isPending]);return l?(0,t.jsx)("div",{className:"product-card__buttons",children:(0,t.jsx)(Wn,{onClick:l.onClick,type:"white",size:"size-sm",disabled:a.isPending||o.update.isPending,children:l.text})}):null},Us=(0,n.memo)((function(e){var r,i=e.product,o=(0,n.useState)(!1),a=o[0],s=o[1],l=(0,n.useState)(!1),c=l[0],u=l[1],d=(0,n.useState)(!1),h=d[0],p=d[1],f=i.is_installed,m=i.is_active,g=qa(i),y=("free"===i.type?{data:null}:La()).data,v=function(){var e=ha(),t=function(t,r){e.setQueryData(["products"],(function(e){var n,i;return $s($s({},e),((n={})[t.type]=$s($s({},e[t.type]),((i={})[t.plugin_file]=$s($s({},t),r),i)),n))}))};return{install:ka({mutationFn:function(e){return Ca()({path:"/gwiz/v1/products/".concat(e.slug,"/install"),method:"POST"})},onSuccess:function(r,n){t(n,r),"free"!==n.type&&(e.setQueryData(["licenses"],(function(e){var t,r=n.type;return e&&e[r]?$s($s({},e),((t={})[r]=$s($s({},e[r]),{registered_products:Bs(Bs([],e[r].registered_products,!0),[n.ID.toString()],!1)}),t)):e})),e.invalidateQueries({queryKey:["licenses"]})),Pa.getState().showNotification("Plugin installed successfully","success")}}),activate:ka({mutationFn:function(e){return Ca()({path:"/gwiz/v1/products/".concat(e.slug,"/activate"),method:"POST"})},onSuccess:function(e,r){t(r,e)}}),deactivate:ka({mutationFn:function(e){return Ca()({path:"/gwiz/v1/products/".concat(e.slug,"/deactivate"),method:"POST"})},onSuccess:function(e,r){t(r,e)}}),delete:ka({mutationFn:function(e){return Ca()({path:"/gwiz/v1/products/".concat(e.slug),method:"DELETE"})},onSuccess:function(e,r){t(r,e),Pa.getState().showNotification("Plugin deleted successfully","success")}}),uninstall:ka({mutationFn:function(e){return Ca()({path:"/gwiz/v1/products/".concat(e.slug,"/uninstall"),method:"POST"})},onSuccess:function(e,r){t(r,e),Pa.getState().showNotification("Plugin uninstalled successfully","success")}}),update:ka({mutationFn:function(e){return Ca()({path:"/gwiz/v1/products/".concat(e.slug,"/update"),method:"POST"})},onSuccess:function(e,r){t(r,e),Pa.getState().showNotification("Plugin updated successfully","success")}})}}(),b=function(){v.delete.mutate(i,{onSuccess:function(){return u(!1)}})},w=Co({products:(r={},r[i.plugin_file]=i,r),searchTerm:""}).highlight,x=(0,n.useMemo)((function(){return(0,t.jsx)(Ja,{customClasses:["gform-card__top-container-heading"],tagName:"h3",size:"text-md",weight:"medium",children:(0,t.jsx)("span",{dangerouslySetInnerHTML:{__html:w(i.name,i.matches,"name")}})})}),[i.name,i.matches]),k=(0,n.useMemo)((function(){return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"product-card__tags",children:[i.version&&(0,t.jsx)("button",{onClick:function(){return p(!0)},children:(0,t.jsx)(cs,{content:"v".concat(i.version),size:"text-xxs",type:"blue",customClasses:i.has_update&&i.is_installed?"version-tag--outdated":void 0,customAttributes:{style:{textTransform:"none"}}})}),i.has_update&&i.is_installed&&(0,t.jsx)("button",{onClick:function(){return p(!0)},children:(0,t.jsx)(cs,{content:"v".concat(i.new_version," AVAILABLE"),size:"text-xxs",type:"blue",customClasses:"version-tag--available",customAttributes:{style:{textTransform:"none"}}})}),g&&(0,t.jsx)(cs,{content:(0,jn.__)("Unregistered","spellbook"),size:"text-xxs",type:"upgrade",customClasses:"registration-required-tag"})]}),(0,t.jsxs)("div",{className:"product-card__content",children:[(0,t.jsx)(fs,{customClasses:["gform-card__top-container-description"],color:"comet",size:"text-sm",asHtml:!0,content:w(i.sections.description,i.matches,"description")}),f&&(0,t.jsx)(Hs,{product:i,licenses:y,mutations:v})]})]})}),[i.version,i.sections.description,i.matches,i.new_version,i.type,i.homepage,y,p,v]),E=(0,n.useMemo)((function(){var e;return(null===(e=i.icons)||void 0===e?void 0:e["1x"])?(0,t.jsx)("img",{src:i.icons["1x"],alt:"",className:"product-icon",loading:"lazy",width:64,height:64}):(0,t.jsx)(zs,{width:64,height:64})}),[i.icons]),C=[E],j=[],O=(0,t.jsxs)("div",{className:"product-card__header-buttons",children:[i.has_settings&&(0,t.jsx)(Wn,{customClasses:["gform-card__top-container-settings-button"],icon:"cog",iconPrefix:"gform-icon",label:(0,jn.__)("Settings","spellbook"),onClick:function(){return s(!0)},size:"size-height-s",type:"icon-white"}),i.documentation&&(0,t.jsx)(Wn,{customClasses:["gform-card__top-container-docs-button"],icon:"book",iconPrefix:"dashicons dashicons-book",label:(0,jn.__)("Documentation","spellbook"),onClick:function(){return window.open(qs(i.documentation,{component:"product-card",text:"documentation"}),"_blank")},size:"size-height-s",type:"icon-white"}),f&&!i.is_legacy_free_plugin&&(0,t.jsx)(Wn,{customClasses:["gform-card__top-container-delete-button"],icon:"trash",iconPrefix:"dashicons dashicons-trash",label:(0,jn.__)("Delete","spellbook"),onClick:function(){return u(!0)},size:"size-height-s",type:"icon-white"})]});C.push(O);var _=(0,t.jsxs)(Ni,{container:!0,elementType:"div",columnSpacing:2,children:[(0,t.jsx)(Ni,{item:!0,elementType:"div",children:i.is_legacy_free_plugin?(0,t.jsx)(fs,{size:"text-xs",spacing:"0 2 0 0",weight:"medium",color:"dark-red",children:(0,jn.__)("Legacy free plugin—delete to manage in Spellbook.","spellbook")}):(0,t.jsx)(fs,{size:"text-xs",spacing:"0 2 0 0",weight:"medium",children:f?m?(0,jn.__)("Active","spellbook"):(0,jn.__)("Inactive","spellbook"):(0,jn.__)("Not Installed","spellbook")})}),i.is_legacy_free_plugin?null:(0,t.jsx)(Ni,{item:!0,elementType:"div",children:(0,t.jsx)(Va,{type:f?m?"success":"warning":"error"})})]});if(j.push(_),!i.is_legacy_free_plugin)if(f)j.push((0,t.jsx)(Rs,{externalChecked:m,externalControl:!0,labelAttributes:{label:(0,jn.__)("Toggle Perk","spellbook"),isVisible:!1},onChange:function(){m?v.deactivate.mutate(i):v.activate.mutate(i)},disabled:v.activate.isPending||v.deactivate.isPending}));else{var S=za(i,y);"free"===i.type||(null==S?void 0:S.valid)?j.push((0,t.jsx)(Wn,{onClick:function(){v.install.mutate(i)},type:"primary",disabled:v.install.isPending,children:v.install.isPending?(0,jn.__)("Installing...","spellbook"):(0,jn.__)("Install","spellbook")})):j.push((0,t.jsx)("a",{href:qs("https://gravitywiz.com/pricing/",{component:"product-card",text:"buy-license"}),target:"_blank",style:{padding:"0.2rem 0",display:"block"},children:(0,jn.__)("Buy License","spellbook")}))}return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(as,{title:x,description:k,headerContent:C,footerContent:j}),a&&(0,t.jsx)(wi.Modal,{title:(0,jn.__)("Gravity Perks Settings","spellbook"),onRequestClose:function(){return s(!1)},className:"spellbook-modal spellbook-plugin-settings-modal",children:(0,t.jsx)(Is,{src:"/wp-admin/admin.php?page=gwp_perks&view=perk_settings&slug=".concat(i.plugin_file)})}),h&&(0,t.jsx)(wi.Modal,{title:(0,jn.__)("Changelog","spellbook"),onRequestClose:function(){return p(!1)},className:"changelog-modal",children:(0,t.jsx)("div",{children:(0,t.jsx)("div",{dangerouslySetInnerHTML:{__html:i.sections.changelog}})})}),c&&(0,t.jsxs)(wi.Modal,{title:(0,jn.__)("Delete Plugin","spellbook"),onRequestClose:function(){return u(!1)},className:"spellbook-modal spellbook-delete-modal",children:[(0,t.jsx)("p",{style:{margin:"0 0 1rem",fontSize:"14px"},children:i.can_uninstall?(0,jn.sprintf)((0,jn.__)("How would you like to delete %s?","spellbook"),i.name):(0,jn.sprintf)((0,jn.__)("Are you sure you want to delete %s?","spellbook"),i.name)}),(0,t.jsx)("div",{style:{display:"flex",alignItems:"center",gap:"1rem"},children:i.can_uninstall?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(Wn,{onClick:b,type:"white",disabled:v.delete.isPending||v.uninstall.isPending,children:v.delete.isPending?(0,jn.__)("Deleting...","spellbook"):(0,jn.__)("Delete (Keep Data)","spellbook")}),(0,t.jsx)(Wn,{onClick:function(){v.uninstall.mutate(i,{onSuccess:function(){return u(!1)}})},type:"amaranth-red",disabled:v.delete.isPending||v.uninstall.isPending,children:v.uninstall.isPending?(0,jn.__)("Uninstalling...","spellbook"):(0,jn.__)("Uninstall (Delete Data)","spellbook")})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(Wn,{onClick:b,type:"amaranth-red",disabled:v.delete.isPending,children:v.delete.isPending?(0,jn.__)("Deleting...","spellbook"):(0,jn.__)("Yes","spellbook")}),(0,t.jsx)(Wn,{onClick:function(){return u(!1)},type:"white",disabled:v.delete.isPending,children:(0,jn.__)("No","spellbook")})]})})]})]})}));const Qs=Us,Vs=function(e){var r=e.products;return e.type,(0,t.jsx)(Di,{spacing:6,children:(0,t.jsx)(Ni,{container:!0,elementType:"div",alignItems:"stretch",customClasses:["spellbook-app__product-card-grid"],justifyContent:"flex-start",columnSpacing:5,rowSpacing:6,children:Object.values(r).map((function(e){return(0,t.jsx)(Ni,{elementType:"div",item:!0,customClasses:["spellbook-app__product-card-grid-item","spellbook-app__product-card-grid-item--".concat(e.slug)],customAttributes:{"data-test-id":"product-card-".concat(e.slug)},children:(0,t.jsx)(Qs,{product:e})},e.plugin_file)}))})})},Ys=function(e){var r=e.title,n=e.description;return e.type,(0,t.jsxs)(Di,{spacing:4,children:[(0,t.jsx)(Ja,{size:"display-sm",weight:"semibold",spacing:1,content:r}),(0,t.jsx)(fs,{size:"text-sm",color:"comet",spacing:6,children:n})]})};var Ks;function Gs(){return Gs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Gs.apply(null,arguments)}const Xs=function(e){return n.createElement("svg",Gs({xmlns:"http://www.w3.org/2000/svg",width:88,height:134,fill:"none",viewBox:"0 0 88 134"},e),Ks||(Ks=n.createElement("path",{fill:"#902EEF",d:"M63.779 2.532c1.332-3.376 6.11-3.376 7.442 0l16.43 41.66a4 4 0 0 0 2.254 2.254l41.66 16.43c3.377 1.332 3.377 6.11 0 7.443l-41.66 16.43a4 4 0 0 0-2.254 2.254l-16.43 41.66c-1.332 3.376-6.11 3.376-7.442 0l-16.43-41.66a4 4 0 0 0-2.254-2.254L3.435 70.32c-3.377-1.332-3.377-6.11 0-7.443l41.66-16.43a4 4 0 0 0 2.253-2.253z"})))};var Js;function Zs(){return Zs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Zs.apply(null,arguments)}const el=function(e){return n.createElement("svg",Zs({xmlns:"http://www.w3.org/2000/svg",width:31,height:31,fill:"none",viewBox:"0 0 31 31"},e),Js||(Js=n.createElement("path",{fill:"#902EEF",d:"M13.64 1.815c.665-1.688 3.055-1.688 3.72 0l3.054 7.742a2 2 0 0 0 1.127 1.127l7.741 3.053c1.689.666 1.689 3.055 0 3.721l-7.741 3.054a2 2 0 0 0-1.127 1.126L17.36 29.38c-.665 1.689-3.055 1.689-3.72 0l-3.054-7.742a2 2 0 0 0-1.127-1.126l-7.742-3.054c-1.688-.666-1.688-3.055 0-3.72l7.742-3.054a2 2 0 0 0 1.127-1.127z"})))};const tl=function(){var e=Ta("perk"),r=e.data,i=e.isLoading,o=Ta("connect"),a=o.data,s=o.isLoading,l=Ta("shop"),c=l.data,u=l.isLoading,d=Fa(),h=d.register,p=d.isPending,f=d.registrationStatus,m=d.isLoading,g=(0,n.useState)(""),y=g[0],v=g[1],b=(0,n.useState)(""),w=b[0],x=b[1],k=(0,n.useState)(""),E=k[0],C=k[1],j=function(){var e,t,r,n=Da("perk").validate,i=Da("connect").validate,o=Da("shop").validate;return{mutate:function(e,t){return Ra(void 0,void 0,void 0,(function(){var r,a,s;return Na(this,(function(l){switch(l.label){case 0:n.reset(),i.reset(),o.reset(),l.label=1;case 1:return l.trys.push([1,3,,10]),[4,n.mutateAsync(e)];case 2:return l.sent(),null===(r=null==t?void 0:t.onSuccess)||void 0===r||r.call(t),[2];case 3:if("license_mismatch"!==l.sent().code)return[3,9];l.label=4;case 4:return l.trys.push([4,6,,9]),[4,i.mutateAsync(e)];case 5:return l.sent(),null===(a=null==t?void 0:t.onSuccess)||void 0===a||a.call(t),[2];case 6:return"license_mismatch"!==l.sent().code?[3,8]:[4,o.mutateAsync(e)];case 7:l.sent(),null===(s=null==t?void 0:t.onSuccess)||void 0===s||s.call(t),l.label=8;case 8:return[3,9];case 9:return[3,10];case 10:return[2]}}))}))},reset:function(){n.reset(),i.reset(),o.reset()},isPending:n.isPending||i.isPending||o.isPending,error:"license_mismatch"!==(null===(e=n.error)||void 0===e?void 0:e.code)?n.error:"license_mismatch"!==(null===(t=i.error)||void 0===t?void 0:t.code)?i.error:"license_mismatch"!==(null===(r=o.error)||void 0===r?void 0:r.code)?o.error:void 0}}(),O=j.mutate,_=j.isPending,S=j.error,A=j.reset;return i||s||u||m||(null==f?void 0:f.is_registered)||(null==r?void 0:r.valid)||(null==a?void 0:a.valid)||(null==c?void 0:c.valid)?null:(0,t.jsxs)("div",{className:"license-bar-all",children:[(0,t.jsxs)("div",{className:"spellbook-stars-background",children:[(0,t.jsx)("div",{className:"spellbook-star-large",children:(0,t.jsx)(Xs,{})}),(0,t.jsx)("div",{className:"spellbook-star-small",children:(0,t.jsx)(el,{})})]}),(0,t.jsxs)("div",{className:"spellbook-section",children:[(0,t.jsx)("h2",{className:"spellbook-section-title",children:"Got a license?"}),(0,t.jsxs)("form",{onSubmit:function(e){e.preventDefault(),E.trim()&&O(E,{onSuccess:function(){window.location.hash="/licenses"}})},className:"spellbook-form-container",children:[(0,t.jsxs)("div",{className:"spellbook-input-group",children:[(0,t.jsx)("input",{type:"text",className:"spellbook-form-input",value:E,onChange:function(e){C(e.target.value),A()},placeholder:"Enter license key"}),(0,t.jsx)("button",{type:"submit",className:"spellbook-form-button",disabled:_,children:_?"Activating...":"Activate license"})]}),(0,t.jsx)("div",{className:"spellbook-license-link",children:S?(0,t.jsx)("div",{className:"spellbook-error-message",children:S.message}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("span",{children:"Don't have a license yet? "}),(0,t.jsx)("a",{href:qs("https://gravitywiz.com/",{component:"license-bar-all",text:"get-license"}),target:"_blank",rel:"noopener noreferrer",children:"Get one now"})]})})]})]}),(0,t.jsx)("div",{className:"spellbook-vertical-divider"}),(0,t.jsxs)("div",{className:"spellbook-section",children:[(0,t.jsx)("h2",{className:"spellbook-section-title",children:"No license? Register for access to free plugins."}),(0,t.jsxs)("form",{onSubmit:function(e){return t=void 0,r=void 0,i=function(){return function(e,t){var r,n,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=s(0),a.throw=s(1),a.return=s(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;a&&(a=0,s[0]&&(o=0)),o;)try{if(r=1,n&&(i=2&s[0]?n.return:s[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,s[1])).done)return i;switch(n=0,i&&(s=[2&s[0],i.value]),s[0]){case 0:case 1:i=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,n=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!((i=(i=o.trys).length>0&&i[i.length-1])||6!==s[0]&&2!==s[0])){o=0;continue}if(3===s[0]&&(!i||s[1]>i[0]&&s[1]<i[3])){o.label=s[1];break}if(6===s[0]&&o.label<i[1]){o.label=i[1],i=s;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(s);break}i[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e],n=0}finally{r=i=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}}(this,(function(t){switch(t.label){case 0:return e.preventDefault(),y&&w?[4,h({name:y,email:w})]:[2];case 1:return t.sent(),[2]}}))},new((n=void 0)||(n=Promise))((function(e,o){function a(e){try{l(i.next(e))}catch(e){o(e)}}function s(e){try{l(i.throw(e))}catch(e){o(e)}}function l(t){var r;t.done?e(t.value):(r=t.value,r instanceof n?r:new n((function(e){e(r)}))).then(a,s)}l((i=i.apply(t,r||[])).next())}));var t,r,n,i},className:"spellbook-form-container",children:[(0,t.jsxs)("div",{className:"spellbook-input-group",children:[(0,t.jsx)("input",{type:"text",className:"spellbook-form-input",value:y,onChange:function(e){return v(e.target.value)},placeholder:"Name"}),(0,t.jsx)("input",{type:"email",className:"spellbook-form-input",value:w,onChange:function(e){return x(e.target.value)},placeholder:"Email Address"}),(0,t.jsx)("button",{type:"submit",className:"spellbook-form-button",disabled:p,children:p?"Registering...":"Get Access"})]}),(0,t.jsxs)("div",{className:"spellbook-license-link",children:[(0,t.jsx)("span",{children:"By submitting your email, you agree to our "}),(0,t.jsx)("a",{href:qs("https://gravitywiz.com/privacy-policy/",{component:"license-bar-all",text:"privacy-policy"}),target:"_blank",rel:"noopener noreferrer",children:"Privacy Policy"})]})]})]})]})};function rl(){return rl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},rl.apply(null,arguments)}const{useState:nl,forwardRef:il}=i(),ol=il((({actionButtonAttributes:e={label:""},borderStyle:t="default",clearable:r=!1,clearableButtonAttributes:n={},controlled:o=!1,customAttributes:a={},customClasses:s=[],disabled:l=!1,helpTextAttributes:u={size:"text-xs",weight:"regular"},helpTextPosition:d="below",iconAttributes:h={},id:p="",labelAttributes:f={size:"text-sm",weight:"medium"},name:m="",onBlur:g=()=>{},onChange:y=()=>{},onClear:v=()=>{},onKeyDown:b=()=>{},onFocus:w=()=>{},placeholder:x="",required:k=!1,requiredLabel:E={size:"text-sm",weight:"medium"},size:C="size-r",spacing:j="",textSecurity:O="none",theme:_="cosmos",type:S="text",value:A="",wrapperAttributes:P={},wrapperClasses:M=[],wrapperTagName:R="div"},N)=>{const[L,T]=nl(A),[D,F]=vn(A),z=o?D:L,I=p||Fr("input"),$=`${I}-help-text`,B=!(!h.icon&&!h.preset),W=e.label&&e.label.length>0,q={...P,className:c()({"gform-input-wrapper":!0,[`gform-input-wrapper--theme-${_}`]:!0,"gform-input-wrapper--input":!0,"gform-input-wrapper--with-action":W,"gform-input-wrapper--clearable":r,"gform-input-wrapper--disabled":l,...Br(j),"gform-input-wrapper--required":k,[`gform-input-wrapper--border-${t}`]:!0,"gform-input-wrapper--with-icon":B,[`gform-input-wrapper--text-security-${O}`]:"password"!==S&&"none"!==O},M),ref:N},H={...a,className:c()({"gform-input":!0,"gform-typography--size-text-sm":"cosmos"===_,[`gform-input--${C}`]:!0,[`gform-input--${S}`]:!0},s),disabled:l||!0===f?.locked,id:I,name:m,onBlur:g,onKeyDown:b,onChange:e=>{const{value:t}=e.target;T(t),F(t),y(t,e)},onFocus:w,type:S,value:z};x&&(H.placeholder=x),u.content&&(H["aria-describedby"]=$),k&&(H.required=!0);const U={...f,htmlFor:I},Q={...u,id:$},V={customClasses:c()(["gform-input-help-text--required"]),id:$,...E},Y={...h,customClasses:c()(["gform-input__icon"],h.customClasses||[])},K={icon:"circle-close",type:"unstyled",...n,customClasses:c()(["gform-input__clearable-button"],n.customClasses||[])},G={iconPosition:"leading",type:"white",...e,customClasses:c()(["gform-input__action-button"],e.customClasses||[]),onClick:t=>{e.onClick&&e.onClick(t,z)}},X=R;return i().createElement(X,q,i().createElement(Ss,U),k&&i().createElement(ys,V),"above"===d&&i().createElement(ys,Q),i().createElement(kr,{condition:W,wrapper:e=>i().createElement("div",{className:"gform-input__action-wrapper"},e,i().createElement(Wn,G))},i().createElement(kr,{condition:B,wrapper:e=>i().createElement("div",{className:"gform-input__wrapper"},e)},i().createElement("input",H),B&&i().createElement(An,Y),r&&z&&i().createElement(Wn,rl({},K,{onClick:()=>{T(""),F(""),v()}})))),"below"===d&&i().createElement(ys,Q))}));ol.propTypes={borderStyle:s().string,clearable:s().bool,clearableButtonAttributes:s().object,controlled:s().bool,customAttributes:s().object,customClasses:s().oneOfType([s().string,s().array,s().object]),disabled:s().bool,helpTextAttributes:s().object,helpTextPosition:s().string,iconAttributes:s().object,id:s().string,labelAttributes:s().object,name:s().string,onBlur:s().func,onChange:s().func,onClear:s().func,onKeyDown:s().func,onFocus:s().func,placeholder:s().string,required:s().bool,requiredLabel:s().object,size:s().string,spacing:s().oneOfType([s().string,s().number,s().array,s().object]),textSecurity:s().string,theme:s().string,type:s().string,value:s().string,wrapperAttributes:s().object,wrapperClasses:s().oneOfType([s().string,s().array,s().object]),wrapperTagName:s().string},ol.displayName="Input";const al=ol,{forwardRef:sl}=i(),ll=sl((({children:e=null,collapsible:t=!1,collapsibleIcon:r="accordion-arrow",collapsibleIconPrefix:n="gravity-component-icon",collapsed:o=!1,contentId:a="",customAttributes:s={},customClasses:l={},FooterContent:u=null,HeaderContent:d=null,i18n:h={},spacing:p="",tagName:f="div",...m},g)=>{const[y,v]=vn(o),{collapsedAriaLabel:b="",expandedAriaLabel:w=""}=h,x={className:c()({"gform-meta-box":!0,"gform-meta-box--is-collapsible":t,"gform-meta-box--is-collapsed":y,...Br(p)},l),...s,"data-testid":"gform-meta-box",ref:g},k={customAttributes:{"aria-controls":a||void 0,"aria-expanded":!y,"aria-label":y?b:w},customClasses:["gform-meta-box__toggle"],icon:r,iconAttributes:{customClasses:["gform-meta-box__toggle-icon"]},iconPrefix:n,onClick:()=>v(!y),type:"unstyled"},E=f;return i().createElement(E,x,d&&i().createElement("div",{className:"gform-meta-box__header","data-testid":"gform-meta-box-header"},i().createElement(kr,{condition:t,wrapper:e=>i().createElement(Wn,k,e)},i().createElement(d,m))),i().createElement("div",{className:"gform-meta-box__content",id:a||void 0,style:{display:y?"none":""}},e),u&&i().createElement("div",{className:"gform-meta-box__footer","data-testid":"gform-meta-box-footer"},i().createElement(u,m)))}));ll.propTypes={children:s().oneOfType([s().arrayOf(s().node),s().node]),customAttributes:s().object,customClasses:s().oneOfType([s().string,s().array,s().object]),FooterContent:s().func,HeaderContent:s().func,spacing:s().oneOfType([s().string,s().number,s().array,s().object]),tagName:s().string},ll.displayName="MetaBox";const cl=ll;var ul=[{id:"unregistered",label:(0,jn.__)("Unregistered","spellbook")},{id:"update-available",label:(0,jn.__)("Update Available","spellbook")}],dl=[{id:"active",label:(0,jn.__)("Activated","spellbook")},{id:"inactive",label:(0,jn.__)("Installed","spellbook")},{id:"not-installed",label:(0,jn.__)("Not Installed","spellbook")}],hl=function(e){return Object.values(e).some((function(e){return e.has_update&&e.is_installed}))},pl=function(e){var t=La().data,r=Fa().registrationStatus;return Object.values(e).some((function(e){return Wa(e,t,r)}))},fl=function(e,t,r){if(r||2===arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))};const ml=function(e){var r=e.activeFilter,i=e.onFilterChange,o=e.products,a=e.type,s=function(e){return Object.values(e).some((function(e){return e.is_active}))}(o),l=function(e){return Object.values(e).some((function(e){return e.is_installed&&!e.is_active}))}(o),c=pl(o),u=hl(o),d=function(e){return Object.values(e).some((function(e){return!e.is_installed}))}(o),h=La().data,p=Fa().registrationStatus,f=(0,n.useMemo)((function(){var e=dl.filter((function(e){switch(e.id){case"active":return s;case"inactive":return l;case"not-installed":return d;default:return!0}})),t=ul.filter((function(e){switch(e.id){case"update-available":return u;case"unregistered":return c;default:return!0}}));return fl(fl(fl([],function(e){return[{id:"all",label:"perk"===e?(0,jn.__)("All Perks","spellbook"):"connect"===e?(0,jn.__)("All Connections","spellbook"):"shop"===e?(0,jn.__)("All Plugins","spellbook"):"free"===e?(0,jn.__)("All Free Plugins","spellbook"):(0,jn.__)("All Plugins","spellbook")}]}(a),!0),t,!0),e,!0)}),[s,l,c,u,d]);return(0,t.jsx)("div",{className:"gform-tabs",children:(0,t.jsx)("div",{className:"gform-tabs__tablist",role:"tablist",children:f.map((function(e){return(0,t.jsxs)(Wn,{customClasses:{"gform-tabs__tab":!0,"gform-tabs__tab--active":r===e.id},onClick:function(){return i(e.id)},customAttributes:{"data-test-id":"filter-".concat(e.id),"aria-selected":r===e.id?"true":"false",role:"tab"},size:"size-height-m",type:"simplified",children:[e.label,"unregistered"===e.id&&c&&(0,t.jsx)("span",{className:"gform-tabs__tab-badge gform-tabs__tab-badge--unregistered",children:Object.values(o).filter((function(e){return Wa(e,h,p)})).length}),"update-available"===e.id&&u&&(0,t.jsx)("span",{className:"gform-tabs__tab-badge gform-tabs__tab-badge--update",children:Object.values(o).filter((function(e){return e.has_update&&e.is_installed})).length})]},e.id)}))})})},gl=function(e){var r=e.value,i=e.placeholder,o=e.onChange,a=e.activeFilter,s=e.onFilterChange,l=e.products,c=e.type,u=void 0===c?"all":c,d=(0,n.useRef)(null);return(0,n.useEffect)((function(){var e=function(e){var t,r;(e.metaKey||e.ctrlKey)&&"k"===e.key&&(e.preventDefault(),null===(t=d.current)||void 0===t||t.focus()),"Escape"===e.key&&document.activeElement===d.current&&(e.preventDefault(),null===(r=d.current)||void 0===r||r.blur())};return document.addEventListener("keydown",e),function(){return document.removeEventListener("keydown",e)}}),[]),(0,t.jsx)(cl,{HeaderContent:void 0,FooterContent:void 0,spacing:4,customClasses:["spellbook-app__search-bar"],children:(0,t.jsxs)("div",{className:"spellbook-app__search-filter-container",children:[(0,t.jsx)("div",{className:"spellbook-app__search-input",children:(0,t.jsx)(al,{name:"search",placeholder:i||"Search products...",value:r,onChange:o,customAttributes:{"data-test-id":"product-search","aria-label":"Search Products",ref:d}})}),void 0!==a&&s&&l&&(0,t.jsx)(ml,{activeFilter:a,onFilterChange:s,products:l,type:u})]})})};var yl=function(e){var t=e.products,r=e.searchTerm,i=e.activeFilter,o=Co({products:t,searchTerm:r}).results,a=pl(t),s=hl(t),l=La().data,c=Fa().registrationStatus,u=(0,n.useMemo)((function(){return"all"===i?o:Object.fromEntries(Object.entries(o).filter((function(e){e[0];var t=e[1];switch(i){case"active":return t.is_active;case"inactive":return!t.is_active&&t.is_installed;case"update-available":return t.has_update;case"not-installed":return!t.is_installed;case"unregistered":return Wa(t,l,c);default:return!0}})))}),[o,i,a,s,l,c]);return{results:u}},vl=function(){return vl=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},vl.apply(this,arguments)};const bl=function(){var e,r,i,o,a=Ws(),s=a.data,l=a.isLoading,c=a.error,u=(0,n.useState)(""),d=u[0],h=u[1],p=(0,n.useState)("all"),f=p[0],m=p[1],g=yl({products:null!==(e=null==s?void 0:s.perk)&&void 0!==e?e:{},searchTerm:d,activeFilter:f}).results,y=yl({products:null!==(r=null==s?void 0:s.connect)&&void 0!==r?r:{},searchTerm:d,activeFilter:f}).results,v=yl({products:null!==(i=null==s?void 0:s.shop)&&void 0!==i?i:{},searchTerm:d,activeFilter:f}).results,b=yl({products:null!==(o=null==s?void 0:s.free)&&void 0!==o?o:{},searchTerm:d,activeFilter:f}).results;return l?(0,t.jsx)(Tn,{}):c?(0,t.jsxs)("div",{children:["Error: ",c.message]}):(0,t.jsxs)("div",{className:"gravityperks-settings-app__perks",children:[(0,t.jsx)(tl,{}),(0,t.jsx)(gl,{value:d,onChange:h,placeholder:"Search ".concat(Object.values(null!=s?s:{}).reduce((function(e,t){return e+Object.keys(t).length}),0)," plugins"),activeFilter:f,onFilterChange:m,products:Object.entries(null!=s?s:{}).reduce((function(e,t){t[0];var r=t[1];return vl(vl({},e),r)}),{}),type:"all"}),Object.keys(g).length>0&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(Ys,{title:"Perks",description:"Install and manage your Gravity Perks. Each perk adds new functionality to Gravity Forms.",type:"perk"}),(0,t.jsx)(Vs,{products:g,type:"perk"})]}),Object.keys(y).length>0&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(Ys,{title:"Connect",description:"Integrate your forms with third-party services and applications.",type:"connect"}),(0,t.jsx)(Vs,{products:y,type:"connect"})]}),Object.keys(v).length>0&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(Ys,{title:"Shop",description:"Plugins that bring the flexibility of Gravity Forms into WooCommerce.",type:"shop"}),(0,t.jsx)(Vs,{products:v,type:"shop"})]}),Object.keys(b).length>0&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(Ys,{title:"Free Plugins",description:"Handy plugins that extend Gravity Forms with simple, focused functionality—no license required.",type:"free"}),(0,t.jsx)(Vs,{products:b,type:"free"})]})]})};var wl,xl;function kl(){return kl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},kl.apply(null,arguments)}const El=function(e){return n.createElement("svg",kl({xmlns:"http://www.w3.org/2000/svg",width:74,height:80,fill:"none",viewBox:"0 0 74 80"},e),wl||(wl=n.createElement("g",{clipPath:"url(#6a)"},n.createElement("path",{fill:"#00B36C",d:"M18.48 74.824c17.396 10.044 39.64 4.083 49.684-13.313s4.083-39.64-13.313-49.683C37.455 1.784 15.211 7.745 5.168 25.141-4.876 42.537 1.084 64.78 18.48 74.824",opacity:.08}),n.createElement("path",{fill:"#00B36C",d:"M46.375 43.894c-3.651-1.817-7.246-7.436-13.331-7.464-8.609-.04-8.645 4.371-14.36 7.534l8.074 13.989a2.53 2.53 0 0 0 2.19 1.263H45.1c.9 0 1.737-.48 2.189-1.263l6.884-11.924s-4.11-.296-7.8-2.135m-15.186 3.257a2.102 2.102 0 1 1 2.105-3.64 2.102 2.102 0 0 1-2.105 3.64m12.205 4.563-2.31.618a.17.17 0 0 0-.117.115l-.682 2.292c-.05.161-.283.16-.324-.004l-.619-2.31a.17.17 0 0 0-.113-.12l-2.292-.682c-.163-.048-.16-.28.005-.324l2.31-.619a.17.17 0 0 0 .116-.115l.683-2.291c.05-.162.28-.157.326.006l.614 2.31c.************.115.117l2.292.683c.164.052.16.282-.004.324M46.812 35.844l2.31-.62c.165-.043.166-.275.004-.323l-2.292-.682a.17.17 0 0 1-.115-.117l-.619-2.31c-.044-.165-.276-.167-.323-.004l-.683 2.292a.17.17 0 0 1-.117.114l-2.31.62c-.165.044-.167.276-.004.323l2.292.683a.17.17 0 0 1 .115.116l.619 2.31c.044.165.276.167.323.004l.683-2.291a.17.17 0 0 1 .117-.115"}),n.createElement("path",{stroke:"#071C26",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3.361,d:"m43.302 19.555 4.532-7.848a3.36 3.36 0 0 1 4.591-1.23l4.806 2.774a3.363 3.363 0 0 1 1.23 4.592l-4.53 7.848a3.37 3.37 0 0 0 0 3.361l7.194 12.46c.599 1.04.6 2.322 0 3.361L50.389 63.47a3.37 3.37 0 0 1-2.911 1.68l-21.476.002a3.36 3.36 0 0 1-2.911-1.68L12.356 44.871a3.37 3.37 0 0 1 0-3.361l10.736-18.596a3.37 3.37 0 0 1 2.91-1.68h14.388a3.37 3.37 0 0 0 2.911-1.68"}),n.createElement("path",{stroke:"#071C26",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3.361,d:"M60.142 14.934 49.513 8.798l1.016-4.832a2.69 2.69 0 0 1 3.976-1.776l8.86 5.116a2.688 2.688 0 0 1 .45 4.33l-3.676 3.296z"}))),xl||(xl=n.createElement("defs",null,n.createElement("clipPath",{id:"6a"},n.createElement("path",{fill:"#fff",d:"M0 0h73.277v80H0z"})))))};var Cl,jl,Ol,_l,Sl;function Al(){return Al=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Al.apply(null,arguments)}const Pl=function(e){return n.createElement("svg",Al({xmlns:"http://www.w3.org/2000/svg",width:80,height:80,fill:"none",viewBox:"0 0 80 80"},e),Cl||(Cl=n.createElement("path",{fill:"#FF3D3D",d:"M40 80c22.091 0 40-17.909 40-40S62.091 0 40 0 0 17.909 0 40s17.909 40 40 40",opacity:.1})),jl||(jl=n.createElement("path",{fill:"#FF3D3D",d:"M56.782 65.28c-4.75 3.332-10.498 5.488-16.69 5.488s-12.108-2.22-16.9-5.633a4.61 4.61 0 0 1-1.899-4.374l.472-3.439a29.6 29.6 0 0 0 18.227 6.295c6.676 0 12.853-2.187 17.943-6.074l.67 2.993a4.57 4.57 0 0 1-1.827 4.748z"})),Ol||(Ol=n.createElement("path",{stroke:"#071C26",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3.2,d:"M47.181 10.285A24.8 24.8 0 0 0 38.94 9.25c-13.701.586-24.333 12.169-23.747 25.866a24.78 24.78 0 0 0 5.15 14.12c4.766 6.157 12.347 9.984 20.715 9.626 8.426-.361 15.69-4.876 19.894-11.49a24.66 24.66 0 0 0 3.85-14.379 24.8 24.8 0 0 0-1.23-6.732"})),_l||(_l=n.createElement("path",{fill:"#FF3D3D",d:"m59.741 18.36 5.185-1.453c.369-.102.369-.623 0-.726l-5.177-1.476a.39.39 0 0 1-.263-.262l-1.453-5.185c-.102-.369-.623-.369-.726 0l-1.476 5.177a.39.39 0 0 1-.262.262l-5.185 1.454c-.369.102-.369.623 0 .726l5.177 1.476a.39.39 0 0 1 .263.262l1.453 5.185c.102.369.623.369.726 0l1.476-5.177a.39.39 0 0 1 .262-.263M47.59 26.969l3.549-.993a.259.259 0 0 0 0-.498l-3.545-1.012a.26.26 0 0 1-.18-.179l-.992-3.549a.259.259 0 0 0-.498 0l-1.012 3.545a.25.25 0 0 1-.179.179l-3.549.993a.259.259 0 0 0 0 .498l3.545 1.012a.26.26 0 0 1 .18.179l.992 3.548a.259.259 0 0 0 .498 0l1.012-3.545a.25.25 0 0 1 .179-.178M55.998 36.067l2.807-.787c.198-.057.198-.339 0-.396L56 34.086a.2.2 0 0 1-.14-.141l-.788-2.807c-.057-.198-.338-.198-.395 0l-.8 2.803a.2.2 0 0 1-.14.14l-2.807.788c-.198.057-.198.339 0 .396l2.803.798a.2.2 0 0 1 .141.141l.788 2.807c.057.198.338.198.395 0l.799-2.803a.2.2 0 0 1 .14-.14"})),Sl||(Sl=n.createElement("path",{stroke:"#071C26",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3.2,d:"M23.458 36.333c-.692-6.615 2.408-12.75 7.543-16.25M36.558 17.588a18 18 0 0 1 2.45-.434"})))};var Ml,Rl;function Nl(){return Nl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Nl.apply(null,arguments)}const Ll=function(e){return n.createElement("svg",Nl({xmlns:"http://www.w3.org/2000/svg",width:74,height:80,fill:"none",viewBox:"0 0 74 80"},e),Ml||(Ml=n.createElement("g",{clipPath:"url(#5a)"},n.createElement("path",{fill:"#00ADC6",d:"M18.542 74.895c17.396 10.044 39.64 4.084 49.684-13.312s4.083-39.64-13.313-49.684S15.273 7.816 5.23 25.212c-10.044 17.396-4.084 39.64 13.312 49.683",opacity:.08}),n.createElement("path",{stroke:"#071C26",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2.743,d:"M58.22 46.408V66.6l-43.874-.006-.092-20.186M9.044 35.335l5.21-15.307h44.293l5.862 15.307"}),n.createElement("path",{fill:"#00ADC6",d:"M21.993 35.335a7.287 7.287 0 0 1-7.286 7.022 7.29 7.29 0 0 1-7.289-7.022zM36.648 35.335a7.287 7.287 0 0 1-7.286 7.022 7.287 7.287 0 0 1-7.285-7.022h14.574zM51.303 35.335a7.287 7.287 0 0 1-7.285 7.022 7.287 7.287 0 0 1-7.286-7.022h14.575zM65.959 35.335a7.287 7.287 0 0 1-7.286 7.022 7.287 7.287 0 0 1-7.285-7.022h14.574zM52.685 61.064v-7.328a7.328 7.328 0 0 0-14.656 0v7.328zM28.16 55.049l4.098-1.137c.29-.081.293-.492 0-.576l-4.084-1.176a.3.3 0 0 1-.206-.209l-1.136-4.097c-.082-.29-.492-.293-.577 0l-1.175 4.084a.3.3 0 0 1-.209.206l-4.097 1.136c-.29.081-.293.492 0 .577l4.084 1.175a.3.3 0 0 1 .205.209l1.137 4.097c.082.29.492.293.576 0l1.176-4.084a.3.3 0 0 1 .209-.205"}))),Rl||(Rl=n.createElement("defs",null,n.createElement("clipPath",{id:"5a"},n.createElement("path",{fill:"#fff",d:"M0 0h73.277v80H0z"})))))},Tl=function(e){var r=e.type,n=e.width,i=void 0===n?74:n,o="perk"===r?El:"connect"===r?Pl:Ll;return(0,t.jsx)("div",{className:"license-box__icon",children:(0,t.jsx)(o,{width:i,height:"auto"})})},Dl=window.wp.element,Fl=(0,Dl.forwardRef)((function({icon:e,size:t=24,...r},n){return(0,Dl.cloneElement)(e,{width:t,height:t,...r,ref:n})})),zl=(0,t.jsx)(vi.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,t.jsx)(vi.Path,{d:"M12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4ZM12.75 8V13H11.25V8H12.75ZM12.75 14.5V16H11.25V14.5H12.75Z"})}),Il=function(e){var r=e.license,n=r.status,i=(r.extend_url,function(){switch(n){case"valid":return(0,jn.__)("Active","spellbook");case"item_name_mismatch":return(0,jn.__)("License Mismatch","spellbook");case"invalid":default:return(0,jn.__)("Invalid","spellbook");case"expired":return(0,jn.__)("Expired","spellbook");case"site_inactive":return(0,jn.__)("Site Inactive","spellbook")}}()),o="valid"===n?"success":"site_inactive"===n?"warning":"error";return"expired"===n?(0,t.jsxs)("div",{className:"license-status--expired",children:[(0,t.jsx)(Fl,{icon:zl}),(0,t.jsx)(fs,{size:"text-sm",weight:"medium",color:"error",children:i})]}):(0,t.jsx)(fs,{size:"text-sm",weight:"medium",color:o,children:i})},$l=function(e){var r=e.type,i=Ta(r),o=i.data,a=i.isLoading,s=Da(r).validate,l=(0,n.useState)(""),c=l[0],u=l[1];if(a)return(0,t.jsx)(Tn,{});if(!(null==o?void 0:o.key))return(0,t.jsxs)("div",{className:"license-bar-suite",children:[(0,t.jsxs)("div",{className:"spellbook-stars-background",children:[(0,t.jsx)("div",{className:"spellbook-star-large",children:(0,t.jsx)(Xs,{})}),(0,t.jsx)("div",{className:"spellbook-star-small",children:(0,t.jsx)(el,{})})]}),(0,t.jsxs)("div",{className:"spellbook-license",children:[(0,t.jsxs)("form",{onSubmit:function(e){e.preventDefault(),c.trim()&&s.mutate(c,{onSuccess:function(){window.location.hash="/licenses"}})},className:"spellbook-form-container",children:[(0,t.jsx)("input",{type:"text",className:"spellbook-form-input ".concat(s.error?"spellbook-form-input-error":""),value:c,onChange:function(e){u(e.target.value),s.reset()},placeholder:"Enter license key"}),(0,t.jsx)("button",{type:"submit",className:"spellbook-form-button",disabled:!c.trim()||s.isPending,children:s.isPending?"Activating...":"Activate license"}),s.error&&(0,t.jsx)("div",{className:"spellbook-error-message",children:s.error.message})]}),(0,t.jsxs)("div",{className:"spellbook-links",children:[(0,t.jsx)("a",{href:qs("https://gravitywiz.com/pricing",{component:"license-bar-suite",text:"buy-license"}),target:"_blank",rel:"noopener noreferrer",children:"Buy License"}),(0,t.jsx)("div",{className:"spellbook-vertical-divider"}),(0,t.jsx)("a",{href:"#/licenses",children:"View Licenses"})]})]})]});var d=o.upgrade_url,h=o.registered_products_limit,p=o.registered_products,f=o.type,m=function(){switch(r){case"perk":return"Gravity Perks";case"connect":return"Gravity Connect";case"shop":return"Gravity Shop"}}();return(0,t.jsxs)("div",{className:"license-bar-suite license-bar-suite--activated",children:[(0,t.jsx)("div",{className:"suite-icon suite-icon--".concat(r),children:(0,t.jsx)(Tl,{type:r,width:40})}),(0,t.jsx)("div",{className:"spellbook-title",children:"".concat(m," ").concat(f)}),(0,t.jsxs)("div",{className:"spellbook-usage",children:["perk"===r&&p&&(0,t.jsx)(t.Fragment,{children:"".concat(Object.entries(p).length,"/").concat(0==h?"∞":h," perks registered")}),"connect"===r&&p&&(0,t.jsx)(t.Fragment,{children:"".concat(Object.entries(p).length,"/").concat(0==h?"∞":h," connections registered")}),"expired"===o.status&&(0,t.jsx)(Il,{license:o})]}),(0,t.jsxs)("div",{className:"license-bar-suite__right",children:["expired"===o.status&&(0,t.jsx)("div",{className:"spellbook-renew",children:(0,t.jsx)("a",{href:qs(o.extend_url,{component:"license-bar-suite",text:"renew-license"}),target:"_blank",children:(0,jn.__)("Renew License","spellbook")})}),(0,t.jsx)("div",{className:"spellbook-upgrade",children:d&&(0,t.jsx)("a",{href:qs(d,{component:"license-bar-suite",text:"upgrade-license"}),target:"_blank",children:(0,jn.__)("Upgrade","spellbook")})}),(0,t.jsx)("button",{type:"button",className:"spellbook-link-button",onClick:function(){window.location.hash="/licenses"},children:(0,jn.__)("Manage License","spellbook")})]})]})},Bl=function(){var e,r,i=Ws(),o=i.data,a=i.isLoading,s=i.error,l=(0,n.useState)(""),c=l[0],u=l[1],d=(0,n.useState)("all"),h=d[0],p=d[1],f=yl({products:null!==(e=null==o?void 0:o.perk)&&void 0!==e?e:{},searchTerm:c,activeFilter:h}).results;return a?(0,t.jsx)(Tn,{}):s?(0,t.jsxs)("div",{children:["Error: ",s.message]}):(0,t.jsxs)("div",{className:"gravityperks-settings-app__perks",children:[(0,t.jsx)($l,{type:"perk"}),(0,t.jsx)(gl,{value:c,onChange:u,placeholder:"Search perks",activeFilter:h,onFilterChange:p,products:null!==(r=null==o?void 0:o.perk)&&void 0!==r?r:{},type:"perk"}),(0,t.jsx)(Ys,{title:"Perks",description:"Install and manage your Gravity Perks. Each perk adds new functionality to Gravity Forms.",type:"perk"}),Object.keys(f).length>0&&(0,t.jsx)(Vs,{products:f,type:"perk"})]})},Wl=function(){var e,r,i,o=Ws(),a=o.data,s=o.isLoading,l=o.error,c=(0,n.useState)(""),u=c[0],d=c[1],h=(0,n.useState)("all"),p=h[0],f=h[1],m=yl({products:null!==(e=null==a?void 0:a.shop)&&void 0!==e?e:{},searchTerm:u,activeFilter:p}).results;return s?(0,t.jsx)(Tn,{}):l?(0,t.jsxs)("div",{children:["Error: ",l.message]}):(0,t.jsxs)("div",{className:"gravityperks-settings-app__shop",children:[(0,t.jsx)($l,{type:"shop"}),Object.entries(null!==(r=null==a?void 0:a.shop)&&void 0!==r?r:{}).length>3&&(0,t.jsx)(gl,{value:u,onChange:d,placeholder:"Search plugins",activeFilter:p,onFilterChange:f,products:null!==(i=null==a?void 0:a.shop)&&void 0!==i?i:{},type:"shop"}),(0,t.jsx)(Ys,{title:"Shop",description:"Plugins that bring the flexibility of Gravity Forms into WooCommerce.",type:"shop"}),Object.keys(m).length>0&&(0,t.jsx)(Vs,{products:m,type:"shop"})]})},ql=function(){var e,r,i=Ws(),o=i.data,a=i.isLoading,s=i.error,l=(0,n.useState)(""),c=l[0],u=l[1],d=(0,n.useState)("all"),h=d[0],p=d[1],f=yl({products:null!==(e=null==o?void 0:o.connect)&&void 0!==e?e:{},searchTerm:c,activeFilter:h}).results;return a?(0,t.jsx)(Tn,{}):s?(0,t.jsxs)("div",{children:["Error: ",s.message]}):(0,t.jsxs)("div",{className:"gravityperks-settings-app__connect",children:[(0,t.jsx)($l,{type:"connect"}),(0,t.jsx)(gl,{value:c,onChange:u,placeholder:"Search connections",activeFilter:h,onFilterChange:p,products:null!==(r=null==o?void 0:o.connect)&&void 0!==r?r:{},type:"connect"}),(0,t.jsx)(Ys,{title:"Connect",description:"Integrate your forms with third-party services and applications.",type:"connect"}),Object.keys(f).length>0&&(0,t.jsx)(Vs,{products:f,type:"connect"})]})};function Hl(e){const t=Object.prototype.toString.call(e);return e instanceof Date||"object"==typeof e&&"[object Date]"===t?new e.constructor(+e):"number"==typeof e||"[object Number]"===t||"string"==typeof e||"[object String]"===t?new Date(e):new Date(NaN)}function Ul(e,t){return e instanceof Date?new e.constructor(t):new Date(t)}function Ql(e,t){const r=Hl(e);return isNaN(t)?Ul(e,NaN):t?(r.setDate(r.getDate()+t),r):r}const Vl={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function Yl(e){return(t={})=>{const r=t.width?String(t.width):e.defaultWidth;return e.formats[r]||e.formats[e.defaultWidth]}}const Kl={date:Yl({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:Yl({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:Yl({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},Gl={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function Xl(e){return(t,r)=>{let n;if("formatting"===(r?.context?String(r.context):"standalone")&&e.formattingValues){const t=e.defaultFormattingWidth||e.defaultWidth,i=r?.width?String(r.width):t;n=e.formattingValues[i]||e.formattingValues[t]}else{const t=e.defaultWidth,i=r?.width?String(r.width):e.defaultWidth;n=e.values[i]||e.values[t]}return n[e.argumentCallback?e.argumentCallback(t):t]}}function Jl(e){return(t,r={})=>{const n=r.width,i=n&&e.matchPatterns[n]||e.matchPatterns[e.defaultMatchWidth],o=t.match(i);if(!o)return null;const a=o[0],s=n&&e.parsePatterns[n]||e.parsePatterns[e.defaultParseWidth],l=Array.isArray(s)?function(e){for(let t=0;t<e.length;t++)if(e[t].test(a))return t}(s):function(e){for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&e[t].test(a))return t}(s);let c;return c=e.valueCallback?e.valueCallback(l):l,c=r.valueCallback?r.valueCallback(c):c,{value:c,rest:t.slice(a.length)}}}var Zl;const ec={code:"en-US",formatDistance:(e,t,r)=>{let n;const i=Vl[e];return n="string"==typeof i?i:1===t?i.one:i.other.replace("{{count}}",t.toString()),r?.addSuffix?r.comparison&&r.comparison>0?"in "+n:n+" ago":n},formatLong:Kl,formatRelative:(e,t,r,n)=>Gl[e],localize:{ordinalNumber:(e,t)=>{const r=Number(e),n=r%100;if(n>20||n<10)switch(n%10){case 1:return r+"st";case 2:return r+"nd";case 3:return r+"rd"}return r+"th"},era:Xl({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:Xl({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:Xl({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:Xl({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:Xl({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:(Zl={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)},(e,t={})=>{const r=e.match(Zl.matchPattern);if(!r)return null;const n=r[0],i=e.match(Zl.parsePattern);if(!i)return null;let o=Zl.valueCallback?Zl.valueCallback(i[0]):i[0];return o=t.valueCallback?t.valueCallback(o):o,{value:o,rest:e.slice(n.length)}}),era:Jl({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:Jl({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:Jl({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:Jl({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:Jl({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}};let tc={};function rc(){return tc}Math.pow(10,8);const nc=6048e5;function ic(e){const t=Hl(e);return t.setHours(0,0,0,0),t}function oc(e){const t=Hl(e),r=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return r.setUTCFullYear(t.getFullYear()),+e-+r}function ac(e){const t=Hl(e);return function(e,t){const r=ic(e),n=ic(t),i=+r-oc(r),o=+n-oc(n);return Math.round((i-o)/864e5)}(t,function(e){const t=Hl(e),r=Ul(e,0);return r.setFullYear(t.getFullYear(),0,1),r.setHours(0,0,0,0),r}(t))+1}function sc(e,t){const r=rc(),n=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??r.weekStartsOn??r.locale?.options?.weekStartsOn??0,i=Hl(e),o=i.getDay(),a=(o<n?7:0)+o-n;return i.setDate(i.getDate()-a),i.setHours(0,0,0,0),i}function lc(e){return sc(e,{weekStartsOn:1})}function cc(e){const t=Hl(e),r=t.getFullYear(),n=Ul(e,0);n.setFullYear(r+1,0,4),n.setHours(0,0,0,0);const i=lc(n),o=Ul(e,0);o.setFullYear(r,0,4),o.setHours(0,0,0,0);const a=lc(o);return t.getTime()>=i.getTime()?r+1:t.getTime()>=a.getTime()?r:r-1}function uc(e){const t=Hl(e),r=+lc(t)-+function(e){const t=cc(e),r=Ul(e,0);return r.setFullYear(t,0,4),r.setHours(0,0,0,0),lc(r)}(t);return Math.round(r/nc)+1}function dc(e,t){const r=Hl(e),n=r.getFullYear(),i=rc(),o=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??i.firstWeekContainsDate??i.locale?.options?.firstWeekContainsDate??1,a=Ul(e,0);a.setFullYear(n+1,0,o),a.setHours(0,0,0,0);const s=sc(a,t),l=Ul(e,0);l.setFullYear(n,0,o),l.setHours(0,0,0,0);const c=sc(l,t);return r.getTime()>=s.getTime()?n+1:r.getTime()>=c.getTime()?n:n-1}function hc(e,t){const r=Hl(e),n=+sc(r,t)-+function(e,t){const r=rc(),n=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??r.firstWeekContainsDate??r.locale?.options?.firstWeekContainsDate??1,i=dc(e,t),o=Ul(e,0);return o.setFullYear(i,0,n),o.setHours(0,0,0,0),sc(o,t)}(r,t);return Math.round(n/nc)+1}function pc(e,t){return(e<0?"-":"")+Math.abs(e).toString().padStart(t,"0")}const fc={y(e,t){const r=e.getFullYear(),n=r>0?r:1-r;return pc("yy"===t?n%100:n,t.length)},M(e,t){const r=e.getMonth();return"M"===t?String(r+1):pc(r+1,2)},d:(e,t)=>pc(e.getDate(),t.length),a(e,t){const r=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return r.toUpperCase();case"aaa":return r;case"aaaaa":return r[0];default:return"am"===r?"a.m.":"p.m."}},h:(e,t)=>pc(e.getHours()%12||12,t.length),H:(e,t)=>pc(e.getHours(),t.length),m:(e,t)=>pc(e.getMinutes(),t.length),s:(e,t)=>pc(e.getSeconds(),t.length),S(e,t){const r=t.length,n=e.getMilliseconds();return pc(Math.trunc(n*Math.pow(10,r-3)),t.length)}},mc={G:function(e,t,r){const n=e.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return r.era(n,{width:"abbreviated"});case"GGGGG":return r.era(n,{width:"narrow"});default:return r.era(n,{width:"wide"})}},y:function(e,t,r){if("yo"===t){const t=e.getFullYear(),n=t>0?t:1-t;return r.ordinalNumber(n,{unit:"year"})}return fc.y(e,t)},Y:function(e,t,r,n){const i=dc(e,n),o=i>0?i:1-i;return"YY"===t?pc(o%100,2):"Yo"===t?r.ordinalNumber(o,{unit:"year"}):pc(o,t.length)},R:function(e,t){return pc(cc(e),t.length)},u:function(e,t){return pc(e.getFullYear(),t.length)},Q:function(e,t,r){const n=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(n);case"QQ":return pc(n,2);case"Qo":return r.ordinalNumber(n,{unit:"quarter"});case"QQQ":return r.quarter(n,{width:"abbreviated",context:"formatting"});case"QQQQQ":return r.quarter(n,{width:"narrow",context:"formatting"});default:return r.quarter(n,{width:"wide",context:"formatting"})}},q:function(e,t,r){const n=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(n);case"qq":return pc(n,2);case"qo":return r.ordinalNumber(n,{unit:"quarter"});case"qqq":return r.quarter(n,{width:"abbreviated",context:"standalone"});case"qqqqq":return r.quarter(n,{width:"narrow",context:"standalone"});default:return r.quarter(n,{width:"wide",context:"standalone"})}},M:function(e,t,r){const n=e.getMonth();switch(t){case"M":case"MM":return fc.M(e,t);case"Mo":return r.ordinalNumber(n+1,{unit:"month"});case"MMM":return r.month(n,{width:"abbreviated",context:"formatting"});case"MMMMM":return r.month(n,{width:"narrow",context:"formatting"});default:return r.month(n,{width:"wide",context:"formatting"})}},L:function(e,t,r){const n=e.getMonth();switch(t){case"L":return String(n+1);case"LL":return pc(n+1,2);case"Lo":return r.ordinalNumber(n+1,{unit:"month"});case"LLL":return r.month(n,{width:"abbreviated",context:"standalone"});case"LLLLL":return r.month(n,{width:"narrow",context:"standalone"});default:return r.month(n,{width:"wide",context:"standalone"})}},w:function(e,t,r,n){const i=hc(e,n);return"wo"===t?r.ordinalNumber(i,{unit:"week"}):pc(i,t.length)},I:function(e,t,r){const n=uc(e);return"Io"===t?r.ordinalNumber(n,{unit:"week"}):pc(n,t.length)},d:function(e,t,r){return"do"===t?r.ordinalNumber(e.getDate(),{unit:"date"}):fc.d(e,t)},D:function(e,t,r){const n=ac(e);return"Do"===t?r.ordinalNumber(n,{unit:"dayOfYear"}):pc(n,t.length)},E:function(e,t,r){const n=e.getDay();switch(t){case"E":case"EE":case"EEE":return r.day(n,{width:"abbreviated",context:"formatting"});case"EEEEE":return r.day(n,{width:"narrow",context:"formatting"});case"EEEEEE":return r.day(n,{width:"short",context:"formatting"});default:return r.day(n,{width:"wide",context:"formatting"})}},e:function(e,t,r,n){const i=e.getDay(),o=(i-n.weekStartsOn+8)%7||7;switch(t){case"e":return String(o);case"ee":return pc(o,2);case"eo":return r.ordinalNumber(o,{unit:"day"});case"eee":return r.day(i,{width:"abbreviated",context:"formatting"});case"eeeee":return r.day(i,{width:"narrow",context:"formatting"});case"eeeeee":return r.day(i,{width:"short",context:"formatting"});default:return r.day(i,{width:"wide",context:"formatting"})}},c:function(e,t,r,n){const i=e.getDay(),o=(i-n.weekStartsOn+8)%7||7;switch(t){case"c":return String(o);case"cc":return pc(o,t.length);case"co":return r.ordinalNumber(o,{unit:"day"});case"ccc":return r.day(i,{width:"abbreviated",context:"standalone"});case"ccccc":return r.day(i,{width:"narrow",context:"standalone"});case"cccccc":return r.day(i,{width:"short",context:"standalone"});default:return r.day(i,{width:"wide",context:"standalone"})}},i:function(e,t,r){const n=e.getDay(),i=0===n?7:n;switch(t){case"i":return String(i);case"ii":return pc(i,t.length);case"io":return r.ordinalNumber(i,{unit:"day"});case"iii":return r.day(n,{width:"abbreviated",context:"formatting"});case"iiiii":return r.day(n,{width:"narrow",context:"formatting"});case"iiiiii":return r.day(n,{width:"short",context:"formatting"});default:return r.day(n,{width:"wide",context:"formatting"})}},a:function(e,t,r){const n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"aaa":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return r.dayPeriod(n,{width:"narrow",context:"formatting"});default:return r.dayPeriod(n,{width:"wide",context:"formatting"})}},b:function(e,t,r){const n=e.getHours();let i;switch(i=12===n?"noon":0===n?"midnight":n/12>=1?"pm":"am",t){case"b":case"bb":return r.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"bbb":return r.dayPeriod(i,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return r.dayPeriod(i,{width:"narrow",context:"formatting"});default:return r.dayPeriod(i,{width:"wide",context:"formatting"})}},B:function(e,t,r){const n=e.getHours();let i;switch(i=n>=17?"evening":n>=12?"afternoon":n>=4?"morning":"night",t){case"B":case"BB":case"BBB":return r.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"BBBBB":return r.dayPeriod(i,{width:"narrow",context:"formatting"});default:return r.dayPeriod(i,{width:"wide",context:"formatting"})}},h:function(e,t,r){if("ho"===t){let t=e.getHours()%12;return 0===t&&(t=12),r.ordinalNumber(t,{unit:"hour"})}return fc.h(e,t)},H:function(e,t,r){return"Ho"===t?r.ordinalNumber(e.getHours(),{unit:"hour"}):fc.H(e,t)},K:function(e,t,r){const n=e.getHours()%12;return"Ko"===t?r.ordinalNumber(n,{unit:"hour"}):pc(n,t.length)},k:function(e,t,r){let n=e.getHours();return 0===n&&(n=24),"ko"===t?r.ordinalNumber(n,{unit:"hour"}):pc(n,t.length)},m:function(e,t,r){return"mo"===t?r.ordinalNumber(e.getMinutes(),{unit:"minute"}):fc.m(e,t)},s:function(e,t,r){return"so"===t?r.ordinalNumber(e.getSeconds(),{unit:"second"}):fc.s(e,t)},S:function(e,t){return fc.S(e,t)},X:function(e,t,r){const n=e.getTimezoneOffset();if(0===n)return"Z";switch(t){case"X":return yc(n);case"XXXX":case"XX":return vc(n);default:return vc(n,":")}},x:function(e,t,r){const n=e.getTimezoneOffset();switch(t){case"x":return yc(n);case"xxxx":case"xx":return vc(n);default:return vc(n,":")}},O:function(e,t,r){const n=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+gc(n,":");default:return"GMT"+vc(n,":")}},z:function(e,t,r){const n=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+gc(n,":");default:return"GMT"+vc(n,":")}},t:function(e,t,r){return pc(Math.trunc(e.getTime()/1e3),t.length)},T:function(e,t,r){return pc(e.getTime(),t.length)}};function gc(e,t=""){const r=e>0?"-":"+",n=Math.abs(e),i=Math.trunc(n/60),o=n%60;return 0===o?r+String(i):r+String(i)+t+pc(o,2)}function yc(e,t){return e%60==0?(e>0?"-":"+")+pc(Math.abs(e)/60,2):vc(e,t)}function vc(e,t=""){const r=e>0?"-":"+",n=Math.abs(e);return r+pc(Math.trunc(n/60),2)+t+pc(n%60,2)}const bc=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},wc=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}},xc={p:wc,P:(e,t)=>{const r=e.match(/(P+)(p+)?/)||[],n=r[1],i=r[2];if(!i)return bc(e,t);let o;switch(n){case"P":o=t.dateTime({width:"short"});break;case"PP":o=t.dateTime({width:"medium"});break;case"PPP":o=t.dateTime({width:"long"});break;default:o=t.dateTime({width:"full"})}return o.replace("{{date}}",bc(n,t)).replace("{{time}}",wc(i,t))}},kc=/^D+$/,Ec=/^Y+$/,Cc=["D","DD","YY","YYYY"];function jc(e){if(!(t=e,t instanceof Date||"object"==typeof t&&"[object Date]"===Object.prototype.toString.call(t)||"number"==typeof e))return!1;var t;const r=Hl(e);return!isNaN(Number(r))}const Oc=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,_c=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Sc=/^'([^]*?)'?$/,Ac=/''/g,Pc=/[a-zA-Z]/;function Mc(e,t,r){const n=rc(),i=r?.locale??n.locale??ec,o=r?.firstWeekContainsDate??r?.locale?.options?.firstWeekContainsDate??n.firstWeekContainsDate??n.locale?.options?.firstWeekContainsDate??1,a=r?.weekStartsOn??r?.locale?.options?.weekStartsOn??n.weekStartsOn??n.locale?.options?.weekStartsOn??0,s=Hl(e);if(!jc(s))throw new RangeError("Invalid time value");let l=t.match(_c).map((e=>{const t=e[0];return"p"===t||"P"===t?(0,xc[t])(e,i.formatLong):e})).join("").match(Oc).map((e=>{if("''"===e)return{isToken:!1,value:"'"};const t=e[0];if("'"===t)return{isToken:!1,value:Rc(e)};if(mc[t])return{isToken:!0,value:e};if(t.match(Pc))throw new RangeError("Format string contains an unescaped latin alphabet character `"+t+"`");return{isToken:!1,value:e}}));i.localize.preprocessor&&(l=i.localize.preprocessor(s,l));const c={firstWeekContainsDate:o,weekStartsOn:a,locale:i};return l.map((n=>{if(!n.isToken)return n.value;const o=n.value;return(!r?.useAdditionalWeekYearTokens&&function(e){return Ec.test(e)}(o)||!r?.useAdditionalDayOfYearTokens&&function(e){return kc.test(e)}(o))&&function(e,t,r){const n=function(e,t,r){const n="Y"===e[0]?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${n} to the input \`${r}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(e,t,r);if(console.warn(n),Cc.includes(e))throw new RangeError(n)}(o,t,String(e)),(0,mc[o[0]])(s,o,i.localize,c)})).join("")}function Rc(e){const t=e.match(Sc);return t?t[1].replace(Ac,"'"):e}const Nc=function(e){var r,n,i=e.license,o=e.type;return(0,t.jsxs)("div",{className:"license-box__details",children:[(0,t.jsxs)("div",{className:"license-box__stat",children:[(0,t.jsx)(fs,{size:"text-sm",color:"comet",children:(0,jn.__)("License Key","spellbook")}),(0,t.jsx)(fs,{size:"text-sm",customClasses:"license-box__key-value",children:i.key?(0,t.jsxs)(t.Fragment,{children:[i.key.slice(0,2),(0,t.jsx)("span",{className:"license-box__key-dots-long",children:"•".repeat(26)}),(0,t.jsx)("span",{className:"license-box__key-dots-medium",children:"•".repeat(16)}),(0,t.jsx)("span",{className:"license-box__key-dots-short",children:"•".repeat(8)}),i.key.slice(-4)]}):(0,jn.__)("No License Key","spellbook")})]}),(0,t.jsxs)("div",{className:"license-box__stat",children:[(0,t.jsx)(fs,{size:"text-sm",color:"comet",children:(0,jn.__)("Status","spellbook")}),(0,t.jsx)(Il,{license:i}),"expired"===i.status&&(0,t.jsx)("a",{href:qs(i.extend_url,{component:"license-info",text:"renew-expired"}),target:"_blank",style:{fontWeight:"500",fontSize:"14px"},children:(0,jn.__)("Renew License","spellbook")})]}),(0,t.jsxs)("div",{className:"license-box__stat",children:[(0,t.jsx)(fs,{size:"text-sm",color:"comet",children:(0,jn.__)("Sites","spellbook")}),(0,t.jsx)(fs,{size:"text-sm",weight:"medium",color:i.site_count>=i.site_limit&&i.site_limit>0?"warning":void 0,children:0===i.site_limit?"".concat(i.site_count,"/∞"):"".concat(i.site_count,"/").concat(i.site_limit)})]}),"perk"===o?(0,t.jsxs)("div",{className:"license-box__stat",children:[(0,t.jsx)(fs,{size:"text-sm",color:"comet",children:"Perks"}),(0,t.jsxs)(fs,{size:"text-sm",weight:"medium",children:[Object.entries(i.registered_products).length,"/","pro"===i.type.toLowerCase()?"∞":"1"]})]}):"connect"===o?(0,t.jsxs)("div",{className:"license-box__stat",children:[(0,t.jsx)(fs,{size:"text-sm",color:"comet",children:"Connections"}),(0,t.jsxs)(fs,{size:"text-sm",weight:"medium",children:[Object.entries(i.registered_products).length,"/","pro"===i.type.toLowerCase()?"∞":"1"]})]}):null,(0,t.jsxs)("div",{className:"license-box__stat",children:[(0,t.jsx)(fs,{size:"text-sm",color:"comet",children:"expired"===i.status?(0,jn.__)("Expired","spellbook"):(0,jn.__)("Expires","spellbook")}),(0,t.jsx)(fs,{size:"text-sm",weight:"medium",color:i.expiration&&"lifetime"!==i.expiration&&(r=new Date(i.expiration),n=Ql(new Date,30),+Hl(r)<+Hl(n))?"warning":void 0,children:i.expiration?"lifetime"===i.expiration?(0,jn.__)("Never","spellbook"):Mc(new Date(i.expiration),"MMM d, yyyy"):(0,jn.__)("N/A","spellbook")})]})]})};const Lc=function(e){var r=e.type,i=e.license,o=Da(r).deactivate,a=(0,n.useCallback)((function(){var e=window.open(qs(i.upgrade_url,{component:"license-actions",text:"upgrade-license"}),"_blank");e&&e.focus()}),[i.upgrade_url]),s=(0,n.useCallback)((function(){var e=window.open(qs(i.manage_url,{component:"license-actions",text:"manage-license"}),"_blank");e&&e.focus()}),[i.manage_url]);return(0,t.jsxs)("div",{className:"license-box__actions",children:[(0,t.jsx)(Wn,{type:"white",label:o.isPending?(0,jn.__)("Deactivating...","spellbook"):(0,jn.__)("Deactivate","spellbook"),size:"size-r",onClick:function(){return e=void 0,t=void 0,n=function(){return function(e,t){var r,n,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=s(0),a.throw=s(1),a.return=s(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;a&&(a=0,s[0]&&(o=0)),o;)try{if(r=1,n&&(i=2&s[0]?n.return:s[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,s[1])).done)return i;switch(n=0,i&&(s=[2&s[0],i.value]),s[0]){case 0:case 1:i=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,n=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!((i=(i=o.trys).length>0&&i[i.length-1])||6!==s[0]&&2!==s[0])){o=0;continue}if(3===s[0]&&(!i||s[1]>i[0]&&s[1]<i[3])){o.label=s[1];break}if(6===s[0]&&o.label<i[1]){o.label=i[1],i=s;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(s);break}i[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e],n=0}finally{r=i=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}}(this,(function(e){switch(e.label){case 0:if(!window.confirm((0,jn.__)("Are you sure you want to deactivate this license?","spellbook")))return[3,4];e.label=1;case 1:return e.trys.push([1,3,,4]),[4,o.mutateAsync()];case 2:case 3:return e.sent(),[3,4];case 4:return[2]}}))},new((r=void 0)||(r=Promise))((function(i,o){function a(e){try{l(n.next(e))}catch(e){o(e)}}function s(e){try{l(n.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((n=n.apply(e,t||[])).next())}));var e,t,r,n},disabled:o.isPending}),(0,t.jsx)(Wn,{type:"white",label:(0,jn.__)("Manage","spellbook"),size:"size-r",onClick:s}),i.upgrade_url&&(0,t.jsx)(Wn,{type:"primary-new",label:(0,jn.__)("Upgrade","spellbook"),size:"size-r",onClick:a})]})};const Tc=function(e){var r=e.type,i=e.description,o=e.learnMoreUrl,a=e.buyLicenseUrl,s=e.isSimple,l=e.shouldRedirect,c=void 0===l||l,u=(0,n.useState)(""),d=u[0],h=u[1],p=Da(r).validate,f=p.isPending,m=p.error;return(0,t.jsxs)("div",{className:"license-box__empty",children:[(0,t.jsxs)("div",{className:"license-box__form",children:[(0,t.jsxs)("div",{className:"license-box__form-inputs",children:[(0,t.jsxs)("div",{className:"license-box__input-wrapper",children:[(0,t.jsx)(al,{name:"license_key",value:d,onChange:function(e){h(e),p.reset()},placeholder:(0,jn.__)("Enter License Key","spellbook"),size:"size-r"}),m&&(0,t.jsx)(fs,{size:"text-sm",customClasses:"license-box__error-message",children:m.message})]}),(0,t.jsx)(Wn,{type:"primary-new",label:f?(0,jn.__)("Validating...","spellbook"):(0,jn.__)("Activate License","spellbook"),size:"size-r",onClick:function(){return e=void 0,t=void 0,n=function(){return function(e,t){var r,n,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=s(0),a.throw=s(1),a.return=s(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;a&&(a=0,s[0]&&(o=0)),o;)try{if(r=1,n&&(i=2&s[0]?n.return:s[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,s[1])).done)return i;switch(n=0,i&&(s=[2&s[0],i.value]),s[0]){case 0:case 1:i=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,n=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!((i=(i=o.trys).length>0&&i[i.length-1])||6!==s[0]&&2!==s[0])){o=0;continue}if(3===s[0]&&(!i||s[1]>i[0]&&s[1]<i[3])){o.label=s[1];break}if(6===s[0]&&o.label<i[1]){o.label=i[1],i=s;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(s);break}i[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e],n=0}finally{r=i=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}}(this,(function(e){switch(e.label){case 0:if(!d.trim())return[2];e.label=1;case 1:return e.trys.push([1,3,,4]),[4,p.mutateAsync(d)];case 2:return e.sent(),c&&(window.location.hash="/licenses"),[3,4];case 3:return e.sent(),[3,4];case 4:return[2]}}))},new((r=void 0)||(r=Promise))((function(i,o){function a(e){try{l(n.next(e))}catch(e){o(e)}}function s(e){try{l(n.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}l((n=n.apply(e,t||[])).next())}));var e,t,r,n},disabled:f})]}),a&&!m&&(0,t.jsxs)(fs,{size:"text-sm",color:"comet",children:[(0,jn.__)("Don't have a license?","spellbook")," ",(0,t.jsx)("a",{href:qs(a,{component:"license-form",text:"buy-now"}),target:"_blank",rel:"noopener noreferrer",className:"gform-link",children:(0,jn.__)("Get one now","spellbook")})]})]}),!s&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"license-box__separator"}),(0,t.jsxs)("div",{className:"license-box__info",children:[(0,t.jsx)(fs,{size:"text-sm",color:"comet",children:i}),o&&(0,t.jsx)("a",{href:qs(o,{component:"license-form",text:"learn-more"}),target:"_blank",rel:"noopener noreferrer",className:"gform-link",children:(0,jn.__)("Learn more","spellbook")})]})]})]})};var Dc=function(e){var r=e.title,n=e.licenseType;return(0,t.jsx)(fs,{size:"text-lg",weight:"medium",children:n?"".concat(r," ").concat(n):r})};const Fc=function(e){var r=e.type,n=e.title,i=e.description,o=e.learnMoreUrl,a=e.buyLicenseUrl,s=Ta(r),l=s.data,c=s.isLoading,u=s.error,d=Da(r).validate;return u?(0,t.jsx)(cl,{HeaderContent:function(){return(0,t.jsx)(Dc,{title:n})},customClasses:"license-box--error",children:(0,t.jsx)(Di,{children:(0,t.jsxs)("div",{className:"license-box__content",children:[(0,t.jsx)(Tl,{type:r}),(0,t.jsx)("div",{className:"license-box__error",children:(0,t.jsx)(fs,{color:"error",children:(0,jn.__)("Failed to load license info.","spellbook")})})]})})}):c||d.isPending?(0,t.jsx)(cl,{HeaderContent:function(){return(0,t.jsx)(Dc,{title:n})},customClasses:"license-box--loading",children:(0,t.jsx)(Di,{children:(0,t.jsxs)("div",{className:"license-box__content",children:[(0,t.jsx)(Tl,{type:r}),(0,t.jsx)("div",{className:"license-box__loading",style:{display:"flex",alignItems:"center",paddingTop:"20px",justifyContent:"center"},children:(0,t.jsx)(Tn,{foreground:"#aaa"})})]})})}):l&&l.key?(0,t.jsx)(cl,{HeaderContent:function(){return(0,t.jsx)(Dc,{title:n,licenseType:l.type})},customClasses:"license-box--activated",children:(0,t.jsx)(Di,{children:(0,t.jsxs)("div",{className:"license-box__content",children:[(0,t.jsx)(Tl,{type:r}),(0,t.jsx)(Nc,{license:l,type:r}),(0,t.jsx)(Lc,{type:r,license:l})]})})}):(0,t.jsx)(cl,{HeaderContent:function(){return(0,t.jsx)(Dc,{title:n})},customClasses:"license-box--empty",children:(0,t.jsx)(Di,{children:(0,t.jsxs)("div",{className:"license-box__content",children:[(0,t.jsx)(Tl,{type:r}),(0,t.jsx)(Tc,{type:r,description:i,learnMoreUrl:o,buyLicenseUrl:a,shouldRedirect:!0})]})})})};const zc=function(){var e=function(){var e=ha(),t=Pa((function(e){return e.setForceLicenseRefresh})),r=Pa((function(e){return e.setForceProductRefresh})),i=Pa((function(e){return e.forceLicenseRefresh})),o=Pa((function(e){return e.forceProductRefresh}));return(0,n.useEffect)((function(){var n,a,s,l;n=void 0,a=void 0,l=function(){return function(e,t){var r,n,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=s(0),a.throw=s(1),a.return=s(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(s){return function(l){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;a&&(a=0,s[0]&&(o=0)),o;)try{if(r=1,n&&(i=2&s[0]?n.return:s[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,s[1])).done)return i;switch(n=0,i&&(s=[2&s[0],i.value]),s[0]){case 0:case 1:i=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,n=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!((i=(i=o.trys).length>0&&i[i.length-1])||6!==s[0]&&2!==s[0])){o=0;continue}if(3===s[0]&&(!i||s[1]>i[0]&&s[1]<i[3])){o.label=s[1];break}if(6===s[0]&&o.label<i[1]){o.label=i[1],i=s;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(s);break}i[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e],n=0}finally{r=i=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,l])}}}(this,(function(n){switch(n.label){case 0:return i?[4,e.resetQueries({queryKey:["licenses"]})]:[3,2];case 1:return n.sent(),t(!1),r(!0),[3,4];case 2:return o?[4,e.resetQueries({queryKey:["products"]})]:[3,4];case 3:n.sent(),r(!1),n.label=4;case 4:return[2]}}))},new((s=void 0)||(s=Promise))((function(e,t){function r(e){try{o(l.next(e))}catch(e){t(e)}}function i(e){try{o(l.throw(e))}catch(e){t(e)}}function o(t){var n;t.done?e(t.value):(n=t.value,n instanceof s?n:new s((function(e){e(n)}))).then(r,i)}o((l=l.apply(n,a||[])).next())}))}),[i,o,e,t,r]),function(){t(!0),r(!0)}}();return(0,t.jsxs)("div",{className:"licenses-page",children:[(0,t.jsxs)("div",{className:"licenses-page__header",style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"24px"},children:[(0,t.jsx)(Ja,{size:"display-sm",weight:"semibold",children:"Licenses"}),(0,t.jsx)(Wn,{type:"white",onClick:function(){e()},children:"Refresh Licenses"})]}),(0,t.jsxs)("div",{className:"licenses-page__grid",children:[(0,t.jsx)(Fc,{type:"perk",title:"Gravity Perks",description:"Enhance Gravity Forms with powerful features and customization options.",learnMoreUrl:"https://gravitywiz.com/gravity-perks",buyLicenseUrl:"https://gravitywiz.com/gravity-perks/pricing"}),(0,t.jsx)(Fc,{type:"connect",title:"Gravity Connect",description:"Integrate your forms with third-party services and applications.",learnMoreUrl:"https://gravitywiz.com/gravity-connect",buyLicenseUrl:"https://gravitywiz.com/gravity-connect/pricing"}),(0,t.jsx)(Fc,{type:"shop",title:"Gravity Shop",description:"Power up your WooCommerce store with advanced form integrations.",learnMoreUrl:"https://gravitywiz.com/gravity-shop",buyLicenseUrl:"https://gravitywiz.com/gravity-shop/pricing"})]})]})},Ic=function(){var e,r,i,o=Ws(),a=o.data,s=o.isLoading,l=o.error,c=(0,n.useState)(""),u=c[0],d=c[1],h=(0,n.useState)("all"),p=h[0],f=h[1],m=yl({products:null!==(e=null==a?void 0:a.free)&&void 0!==e?e:{},searchTerm:u,activeFilter:p}).results;return s?(0,t.jsx)(Tn,{}):l?(0,t.jsxs)("div",{children:["Error: ",l.message]}):(0,t.jsxs)("div",{className:"gravityperks-settings-app__free",children:[(0,t.jsx)(tl,{}),Object.entries(null!==(r=null==a?void 0:a.free)&&void 0!==r?r:{}).length>2&&(0,t.jsx)(gl,{value:u,onChange:d,placeholder:"Search free plugins",activeFilter:p,onFilterChange:f,products:null!==(i=null==a?void 0:a.free)&&void 0!==i?i:{},type:"free"}),(0,t.jsx)(Ys,{title:"Free Plugins",description:"Handy plugins that extend Gravity Forms with simple, focused functionality—no license required.",type:"free"}),Object.keys(m).length>0&&(0,t.jsx)(Vs,{products:m,type:"free"})]})};function $c(){return $c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$c.apply(null,arguments)}const{forwardRef:Bc,useEffect:Wc,useState:qc,createContext:Hc,useContext:Uc}=i(),Qc=Hc({}),Vc=Hc(null),Yc=Bc(((e,t)=>{const r={...Uc(Qc),...e},{ariaLive:n="polite",children:o=null,closeButtonAttributes:a={},closeButtonClasses:s=[],customAttributes:l={},customClasses:u=[],delay:d=5e3,errorIconAttributes:h={},errorIconClasses:p=[],id:f="",interactive:m=!1,message:g="",onDismiss:y=()=>{},spacing:v="",successIconAttributes:b={},successIconClasses:w=[],theme:x="cosmos",type:k="success",...E}=r,[C,j]=qc(!1);Wc((()=>{setTimeout((()=>{j(!0)}),50)}),[]),Wc((()=>{if(m)return;const e=setTimeout((()=>{j(!1)}),d),t=setTimeout((()=>{y(f)}),d+100);return()=>{clearTimeout(e),clearTimeout(t)}}),[m,d,f,y]);const O={className:c()({"gform-snackbar":!0,"gform-snackbar--react":!0,"gform-snackbar--interactive":m,"gform-snackbar--visible":C,[`gform-snackbar--theme-${x}`]:!0,[`gform-snackbar--type-${k}`]:!0,...Br(v)},u),style:{...l.style,"--gform-snackbar-animation-delay":`${C?0:d}ms`},"aria-live":n,ref:t,...l},_={customClasses:c()({"gform-snackbar__close":!0},s),iconPosition:"leading",type:"unstyled",...a},S={customClasses:c()({"gform-snackbar__type-icon":!0,"gform-snackbar__type-icon--error":!0},p),icon:"delete",iconPrefix:"gravity-component-icon",spacing:[0,3,0,0],...h},A={customClasses:c()({"gform-snackbar__type-icon":!0,"gform-snackbar__type-icon--success":!0},w),icon:"check",iconPrefix:"gravity-component-icon",spacing:[0,3,0,0],...b};return i().createElement("div",$c({},O,E),"error"===k&&i().createElement(An,S),"success"===k&&i().createElement(An,A),g,o,m&&i().createElement(Wn,$c({},_,{onClick:()=>{y(f),j(!1)}})))})),Kc=({children:e,defaultSettings:t={}})=>{const[r,n]=qc([]),o=e=>{n((t=>t.filter((t=>t.id!==e))))};return i().createElement(Qc.Provider,{value:t},i().createElement(Vc.Provider,{value:(e,r="success",i={})=>{const o={...t,type:r,...i},a=Math.random().toString(36).substring(7);n((t=>[...t,{id:a,message:e,...o}]))}},e,r.map((({id:e,message:t,type:r,...n},a)=>i().createElement(Yc,$c({customAttributes:{style:{"--gform-snackbar-index":a}},id:e,key:e,message:t,onDismiss:o,type:r},n))))))};Yc.propTypes={children:s().oneOfType([s().arrayOf(s().node),s().node]),customAttributes:s().object,customClasses:s().oneOfType([s().string,s().array,s().object]),delay:s().number,id:s().string,message:s().string,onDismiss:s().func,spacing:s().oneOfType([s().string,s().number,s().array,s().object]),theme:s().string,type:s().string},Yc.displayName="SnackBar";var Gc=r(795);function Xc(e){return n.createElement(Wt,{flushSync:Gc.flushSync,...e})}var Jc=function(){return null},Zc=class extends jo{constructor(e={}){super(),this.config=e,this.#B=new Map}#B;build(e,t,r){const n=t.queryKey,i=t.queryHash??Lo(n,t);let o=this.get(i);return o||(o=new ia({client:e,queryKey:n,queryHash:i,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(n)}),this.add(o)),o}add(e){this.#B.has(e.queryHash)||(this.#B.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const t=this.#B.get(e.queryHash);t&&(e.destroy(),t===e&&this.#B.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){Ko.batch((()=>{this.getAll().forEach((e=>{this.remove(e)}))}))}get(e){return this.#B.get(e)}getAll(){return[...this.#B.values()]}find(e){const t={exact:!0,...e};return this.getAll().find((e=>Ro(t,e)))}findAll(e={}){const t=this.getAll();return Object.keys(e).length>0?t.filter((t=>Ro(e,t))):t}notify(e){Ko.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}onFocus(){Ko.batch((()=>{this.getAll().forEach((e=>{e.onFocus()}))}))}onOnline(){Ko.batch((()=>{this.getAll().forEach((e=>{e.onOnline()}))}))}},eu=class extends jo{constructor(e={}){super(),this.config=e,this.#W=new Set,this.#q=new Map,this.#H=0}#W;#q;#H;build(e,t,r){const n=new wa({mutationCache:this,mutationId:++this.#H,options:e.defaultMutationOptions(t),state:r});return this.add(n),n}add(e){this.#W.add(e);const t=tu(e);if("string"==typeof t){const r=this.#q.get(t);r?r.push(e):this.#q.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#W.delete(e)){const t=tu(e);if("string"==typeof t){const r=this.#q.get(t);if(r)if(r.length>1){const t=r.indexOf(e);-1!==t&&r.splice(t,1)}else r[0]===e&&this.#q.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){const t=tu(e);if("string"==typeof t){const r=this.#q.get(t),n=r?.find((e=>"pending"===e.state.status));return!n||n===e}return!0}runNext(e){const t=tu(e);if("string"==typeof t){const r=this.#q.get(t)?.find((t=>t!==e&&t.state.isPaused));return r?.continue()??Promise.resolve()}return Promise.resolve()}clear(){Ko.batch((()=>{this.#W.forEach((e=>{this.notify({type:"removed",mutation:e})})),this.#W.clear(),this.#q.clear()}))}getAll(){return Array.from(this.#W)}find(e){const t={exact:!0,...e};return this.getAll().find((e=>No(t,e)))}findAll(e={}){return this.getAll().filter((t=>No(e,t)))}notify(e){Ko.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}resumePausedMutations(){const e=this.getAll().filter((e=>e.state.isPaused));return Ko.batch((()=>Promise.all(e.map((e=>e.continue().catch(_o))))))}};function tu(e){return e.options.scope?.id}function ru(e){return{onFetch:(t,r)=>{const n=t.options,i=t.fetchOptions?.meta?.fetchMore?.direction,o=t.state.data?.pages||[],a=t.state.data?.pageParams||[];let s={pages:[],pageParams:[]},l=0;const c=async()=>{let r=!1;const c=Qo(t.options,t.fetchOptions),u=async(e,n,i)=>{if(r)return Promise.reject();if(null==n&&e.pages.length)return Promise.resolve(e);const o={client:t.client,queryKey:t.queryKey,pageParam:n,direction:i?"backward":"forward",meta:t.options.meta};var a;a=o,Object.defineProperty(a,"signal",{enumerable:!0,get:()=>(t.signal.aborted?r=!0:t.signal.addEventListener("abort",(()=>{r=!0})),t.signal)});const s=await c(o),{maxPages:l}=t.options,u=i?Ho:qo;return{pages:u(e.pages,s,l),pageParams:u(e.pageParams,n,l)}};if(i&&o.length){const e="backward"===i,t={pages:o,pageParams:a},r=(e?iu:nu)(n,t);s=await u(t,r,e)}else{const t=e??o.length;do{const e=0===l?a[0]??n.initialPageParam:nu(n,s);if(l>0&&null==e)break;s=await u(s,e),l++}while(l<t)}return s};t.options.persister?t.fetchFn=()=>t.options.persister?.(c,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=c}}}function nu(e,{pages:t,pageParams:r}){const n=t.length-1;return t.length>0?e.getNextPageParam(t[n],t,r[n],r):void 0}function iu(e,{pages:t,pageParams:r}){return t.length>0?e.getPreviousPageParam?.(t[0],t,r[0],r):void 0}var ou,au=new class{#U;#F;#u;#Q;#V;#Y;#K;#G;constructor(e={}){this.#U=e.queryCache||new Zc,this.#F=e.mutationCache||new eu,this.#u=e.defaultOptions||{},this.#Q=new Map,this.#V=new Map,this.#Y=0}mount(){this.#Y++,1===this.#Y&&(this.#K=Vo.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#U.onFocus())})),this.#G=Go.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#U.onOnline())})))}unmount(){this.#Y--,0===this.#Y&&(this.#K?.(),this.#K=void 0,this.#G?.(),this.#G=void 0)}isFetching(e){return this.#U.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#F.findAll({...e,status:"pending"}).length}getQueryData(e){const t=this.defaultQueryOptions({queryKey:e});return this.#U.get(t.queryHash)?.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),r=this.#U.build(this,t),n=r.state.data;return void 0===n?this.fetchQuery(e):(e.revalidateIfStale&&r.isStaleByTime(Po(t.staleTime,r))&&this.prefetchQuery(t),Promise.resolve(n))}getQueriesData(e){return this.#U.findAll(e).map((({queryKey:e,state:t})=>[e,t.data]))}setQueryData(e,t,r){const n=this.defaultQueryOptions({queryKey:e}),i=this.#U.get(n.queryHash),o=i?.state.data,a=function(e,t){return"function"==typeof e?e(t):e}(t,o);if(void 0!==a)return this.#U.build(this,n).setData(a,{...r,manual:!0})}setQueriesData(e,t,r){return Ko.batch((()=>this.#U.findAll(e).map((({queryKey:e})=>[e,this.setQueryData(e,t,r)]))))}getQueryState(e){const t=this.defaultQueryOptions({queryKey:e});return this.#U.get(t.queryHash)?.state}removeQueries(e){const t=this.#U;Ko.batch((()=>{t.findAll(e).forEach((e=>{t.remove(e)}))}))}resetQueries(e,t){const r=this.#U;return Ko.batch((()=>(r.findAll(e).forEach((e=>{e.reset()})),this.refetchQueries({type:"active",...e},t))))}cancelQueries(e,t={}){const r={revert:!0,...t},n=Ko.batch((()=>this.#U.findAll(e).map((e=>e.cancel(r)))));return Promise.all(n).then(_o).catch(_o)}invalidateQueries(e,t={}){return Ko.batch((()=>(this.#U.findAll(e).forEach((e=>{e.invalidate()})),"none"===e?.refetchType?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))))}refetchQueries(e,t={}){const r={...t,cancelRefetch:t.cancelRefetch??!0},n=Ko.batch((()=>this.#U.findAll(e).filter((e=>!e.isDisabled())).map((e=>{let t=e.fetch(void 0,r);return r.throwOnError||(t=t.catch(_o)),"paused"===e.state.fetchStatus?Promise.resolve():t}))));return Promise.all(n).then(_o)}fetchQuery(e){const t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);const r=this.#U.build(this,t);return r.isStaleByTime(Po(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(_o).catch(_o)}fetchInfiniteQuery(e){return e.behavior=ru(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(_o).catch(_o)}ensureInfiniteQueryData(e){return e.behavior=ru(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return Go.isOnline()?this.#F.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#U}getMutationCache(){return this.#F}getDefaultOptions(){return this.#u}setDefaultOptions(e){this.#u=e}setQueryDefaults(e,t){this.#Q.set(To(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...this.#Q.values()],r={};return t.forEach((t=>{Do(e,t.queryKey)&&Object.assign(r,t.defaultOptions)})),r}setMutationDefaults(e,t){this.#V.set(To(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...this.#V.values()],r={};return t.forEach((t=>{Do(e,t.mutationKey)&&Object.assign(r,t.defaultOptions)})),r}defaultQueryOptions(e){if(e._defaulted)return e;const t={...this.#u.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=Lo(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===Uo&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#u.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#U.clear(),this.#F.clear()}}({defaultOptions:{queries:{staleTime:1/0,retry:!1},mutations:{onError:function(e){console.log({error:e});var t=function(e){return null!==e&&"object"==typeof e&&"error"in e&&"object"==typeof e.error&&"message"in e.error}(e)?e.error.message:function(e){return null!==e&&"object"==typeof e&&"code"in e&&"message"in e&&"string"==typeof e.message}(e)?e.message:(0,jn.__)("An unknown error occurred","spellbook");Pa.getState().showNotification(t,"error")}}}}),su=(ou=[{path:"/*",element:(0,t.jsx)((function(){var e=Pa(),r=e.notifications,i=e.removeNotification,o=Uc(Vc);return Ws(),Ta("perk"),Ta("connect"),Ta("shop"),(0,n.useEffect)((function(){r.length>0&&r.forEach((function(e){o(e.message,e.type,{delay:5e3,interactive:!1}),i(e.id)}))}),[r,o,i]),(0,t.jsx)("div",{className:"spellbook-app",children:(0,t.jsx)(Cn,{Header:ii,PrimarySideBarChildren:Pi,children:(0,t.jsx)("div",{className:"spellbook-app__content",children:(0,t.jsxs)(Qt,{children:[(0,t.jsx)(Ht,{path:"/",element:(0,t.jsx)(Vn,{children:(0,t.jsx)(bl,{})})}),(0,t.jsx)(Ht,{path:"/perks",element:(0,t.jsx)(Vn,{children:(0,t.jsx)(Bl,{})})}),(0,t.jsx)(Ht,{path:"/connect",element:(0,t.jsx)(Vn,{children:(0,t.jsx)(ql,{})})}),(0,t.jsx)(Ht,{path:"/shop",element:(0,t.jsx)(Vn,{children:(0,t.jsx)(Wl,{})})}),(0,t.jsx)(Ht,{path:"/licenses",element:(0,t.jsx)(Vn,{children:(0,t.jsx)(zc,{})})}),(0,t.jsx)(Ht,{path:"/free-plugins",element:(0,t.jsx)(Vn,{children:(0,t.jsx)(Ic,{})})}),(0,t.jsx)(Ht,{path:"*",element:(0,t.jsx)(Vn,{children:(0,t.jsx)("div",{children:"Not Found"})})})]})})})})}),{})}],function(e){const t=e.window?e.window:"undefined"!=typeof window?window:void 0,r=void 0!==t&&void 0!==t.document&&void 0!==t.document.createElement;F(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let n,i,o,a=e.mapRouteProperties||Se,s={},l=U(e.routes,a,void 0,s),c=e.basename||"/",u=e.dataStrategy||We,d={unstable_middleware:!1,...e.future},h=null,p=new Set,f=null,m=null,g=null,y=null!=e.hydrationData,v=Q(l,e.history.location,c),b=!1,w=null;if(null==v&&!e.patchRoutesOnNavigation){let t=tt(404,{pathname:e.history.location.pathname}),{matches:r,route:n}=et(l);v=r,w={[n.id]:t}}if(v&&!e.hydrationData&&we(v,l,e.history.location.pathname).active&&(v=null),v)if(v.some((e=>e.route.lazy)))i=!1;else if(v.some((e=>e.route.loader))){let t=e.hydrationData?e.hydrationData.loaderData:null,r=e.hydrationData?e.hydrationData.errors:null;if(r){let e=v.findIndex((e=>void 0!==r[e.route.id]));i=v.slice(0,e+1).every((e=>!Te(e.route,t,r)))}else i=v.every((e=>!Te(e.route,t,r)))}else i=!0;else{i=!1,v=[];let t=we(null,l,e.history.location.pathname);t.active&&t.matches&&(b=!0,v=t.matches)}let x,k,E={historyAction:e.history.action,location:e.history.location,matches:v,initialized:i,navigation:Ce,restoreScrollPosition:null==e.hydrationData&&null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||w,fetchers:new Map,blockers:new Map},C="POP",j=!1,O=!1,_=new Map,S=null,A=!1,P=!1,M=new Set,R=new Map,N=0,L=-1,T=new Map,D=new Set,I=new Map,B=new Map,W=new Set,Y=new Map,K=null;function G(e,t={}){E={...E,...e};let r=[],n=[];E.fetchers.forEach(((e,t)=>{"idle"===e.state&&(W.has(t)?r.push(t):n.push(t))})),W.forEach((e=>{E.fetchers.has(e)||R.has(e)||r.push(e)})),[...p].forEach((e=>e(E,{deletedFetchers:r,viewTransitionOpts:t.viewTransitionOpts,flushSync:!0===t.flushSync}))),r.forEach((e=>le(e))),n.forEach((e=>E.fetchers.delete(e)))}function X(t,r,{flushSync:i}={}){let o,a=null!=E.actionData&&null!=E.navigation.formMethod&&ct(E.navigation.formMethod)&&"loading"===E.navigation.state&&!0!==t.state?._isRedirect;o=r.actionData?Object.keys(r.actionData).length>0?r.actionData:null:a?E.actionData:null;let s=r.loaderData?Xe(E.loaderData,r.loaderData,r.matches||[],r.errors):E.loaderData,c=E.blockers;c.size>0&&(c=new Map(c),c.forEach(((e,t)=>c.set(t,Oe))));let u,d=!0===j||null!=E.navigation.formMethod&&ct(E.navigation.formMethod)&&!0!==t.state?._isRedirect;if(n&&(l=n,n=void 0),A||"POP"===C||("PUSH"===C?e.history.push(t,t.state):"REPLACE"===C&&e.history.replace(t,t.state)),"POP"===C){let e=_.get(E.location.pathname);e&&e.has(t.pathname)?u={currentLocation:E.location,nextLocation:t}:_.has(t.pathname)&&(u={currentLocation:t,nextLocation:E.location})}else if(O){let e=_.get(E.location.pathname);e?e.add(t.pathname):(e=new Set([t.pathname]),_.set(E.location.pathname,e)),u={currentLocation:E.location,nextLocation:t}}G({...r,actionData:o,loaderData:s,historyAction:C,location:t,initialized:!0,navigation:Ce,revalidation:"idle",restoreScrollPosition:be(t,r.matches||E.matches),preventScrollReset:d,blockers:c},{viewTransitionOpts:u,flushSync:!0===i}),C="POP",j=!1,O=!1,A=!1,P=!1,K?.resolve(),K=null}async function J(t,r,i){x&&x.abort(),x=null,C=t,A=!0===(i&&i.startUninterruptedRevalidation),function(e,t){if(f&&g){let r=ve(e,t);f[r]=g()}}(E.location,E.matches),j=!0===(i&&i.preventScrollReset),O=!0===(i&&i.enableViewTransition);let o=n||l,a=i&&i.overrideNavigation,s=i?.initialHydration&&E.matches&&E.matches.length>0&&!b?E.matches:Q(o,r,c),u=!0===(i&&i.flushSync);if(s&&E.initialized&&!P&&(d=E.location,h=r,d.pathname===h.pathname&&d.search===h.search&&(""===d.hash?""!==h.hash:d.hash===h.hash||""!==h.hash))&&!(i&&i.submission&&ct(i.submission.formMethod)))return void X(r,{matches:s},{flushSync:u});var d,h;let p=we(s,o,r.pathname);if(p.active&&p.matches&&(s=p.matches),!s){let{error:e,notFoundMatches:t,route:n}=ge(r.pathname);return void X(r,{matches:t,loaderData:{},errors:{[n.id]:e}},{flushSync:u})}x=new AbortController;let m,y=Ve(e.history,r,x.signal,i&&i.submission),v=new q(e.unstable_getContext?await e.unstable_getContext():void 0);if(i&&i.pendingError)m=[Ze(s).route.id,{type:"error",error:i.pendingError}];else if(i&&i.submission&&ct(i.submission.formMethod)){let t=await async function(e,t,r,n,i,o,a={}){ne();let s,l=function(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}(t,r);if(G({navigation:l},{flushSync:!0===a.flushSync}),o){let r=await xe(n,t.pathname,e.signal);if("aborted"===r.type)return{shortCircuited:!0};if("error"===r.type){let e=Ze(r.partialMatches).route.id;return{matches:r.partialMatches,pendingActionResult:[e,{type:"error",error:r.error}]}}if(!r.matches){let{notFoundMatches:e,error:r,route:n}=ge(t.pathname);return{matches:e,pendingActionResult:[n.id,{type:"error",error:r}]}}n=r.matches}let u=dt(n,t);if(u.route.action||u.route.lazy){let t=await te("action",e,[u],n,i,null);if(s=t[u.route.id],!s)for(let e of n)if(t[e.route.id]){s=t[e.route.id];break}if(e.signal.aborted)return{shortCircuited:!0}}else s={type:"error",error:tt(405,{method:e.method,pathname:t.pathname,routeId:u.route.id})};if(at(s)){let t;return t=a&&null!=a.replace?a.replace:Qe(s.response.headers.get("Location"),new URL(e.url),c)===E.location.pathname+E.location.search,await ee(e,s,!0,{submission:r,replace:t}),{shortCircuited:!0}}if(ot(s)){let e=Ze(n,u.route.id);return!0!==(a&&a.replace)&&(C="PUSH"),{matches:n,pendingActionResult:[e.route.id,s]}}return{matches:n,pendingActionResult:[u.route.id,s]}}(y,r,i.submission,s,v,p.active,{replace:i.replace,flushSync:u});if(t.shortCircuited)return;if(t.pendingActionResult){let[e,n]=t.pendingActionResult;if(ot(n)&&ye(n.error)&&404===n.error.status)return x=null,void X(r,{matches:t.matches,loaderData:{},errors:{[e]:n.error}})}s=t.matches||s,m=t.pendingActionResult,a=pt(r,i.submission),u=!1,p.active=!1,y=Ve(e.history,y.url,y.signal)}let{shortCircuited:w,matches:k,loaderData:_,errors:S}=await async function(t,r,i,o,a,s,u,d,h,p,f,m){let g=s||pt(r,u),y=u||d||ht(g),v=!A&&!p;if(a){if(v){let e=Z(m);G({navigation:g,...void 0!==e?{actionData:e}:{}},{flushSync:f})}let e=await xe(i,r.pathname,t.signal);if("aborted"===e.type)return{shortCircuited:!0};if("error"===e.type){let t=Ze(e.partialMatches).route.id;return{matches:e.partialMatches,loaderData:{},errors:{[t]:e.error}}}if(!e.matches){let{error:e,notFoundMatches:t,route:n}=ge(r.pathname);return{matches:t,loaderData:{},errors:{[n.id]:e}}}i=e.matches}let b=n||l,[w,k]=Le(e.history,E,i,y,r,!0===p,P,M,W,I,D,b,c,m);if(L=++N,0===w.length&&0===k.length){let e=de();return X(r,{matches:i,loaderData:{},errors:m&&ot(m[1])?{[m[0]]:m[1].error}:null,...Je(m),...e?{fetchers:new Map(E.fetchers)}:{}},{flushSync:f}),{shortCircuited:!0}}if(v){let e={};if(!a){e.navigation=g;let t=Z(m);void 0!==t&&(e.actionData=t)}k.length>0&&(e.fetchers=function(e){return e.forEach((e=>{let t=E.fetchers.get(e.key),r=ft(void 0,t?t.data:void 0);E.fetchers.set(e.key,r)})),new Map(E.fetchers)}(k)),G(e,{flushSync:f})}k.forEach((e=>{ce(e.key),e.controller&&R.set(e.key,e.controller)}));let C=()=>k.forEach((e=>ce(e.key)));x&&x.signal.addEventListener("abort",C);let{loaderResults:j,fetcherResults:O}=await re(i,w,k,t,o);if(t.signal.aborted)return{shortCircuited:!0};x&&x.signal.removeEventListener("abort",C),k.forEach((e=>R.delete(e.key)));let _=rt(j);if(_)return await ee(t,_.result,!0,{replace:h}),{shortCircuited:!0};if(_=rt(O),_)return D.add(_.key),await ee(t,_.result,!0,{replace:h}),{shortCircuited:!0};let{loaderData:S,errors:T}=Ge(E,i,j,m,k,O);p&&E.errors&&(T={...E.errors,...T});let F=de(),z=he(L);return{matches:i,loaderData:S,errors:T,...F||z||k.length>0?{fetchers:new Map(E.fetchers)}:{}}}(y,r,s,v,p.active,a,i&&i.submission,i&&i.fetcherSubmission,i&&i.replace,i&&!0===i.initialHydration,u,m);w||(x=null,X(r,{matches:k||s,...Je(m),loaderData:_,errors:S}))}function Z(e){return e&&!ot(e[1])?{[e[0]]:e[1].data}:E.actionData?0===Object.keys(E.actionData).length?null:E.actionData:void 0}async function ee(n,i,o,{submission:a,fetcherSubmission:s,preventScrollReset:l,replace:u}={}){i.response.headers.has("X-Remix-Revalidate")&&(P=!0);let d=i.response.headers.get("Location");F(d,"Expected a Location header on the redirect Response"),d=Qe(d,new URL(n.url),c);let h=$(E.location,d,{_isRedirect:!0});if(r){let r=!1;if(i.response.headers.has("X-Remix-Reload-Document"))r=!0;else if(_e.test(d)){const n=e.history.createURL(d);r=n.origin!==t.location.origin||null==se(n.pathname,c)}if(r)return void(u?t.location.replace(d):t.location.assign(d))}x=null;let p=!0===u||i.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:f,formAction:m,formEncType:g}=E.navigation;!a&&!s&&f&&m&&g&&(a=ht(E.navigation));let y=a||s;if(Ee.has(i.response.status)&&y&&ct(y.formMethod))await J(p,h,{submission:{...y,formAction:d},preventScrollReset:l||j,enableViewTransition:o?O:void 0});else{let e=pt(h,a);await J(p,h,{overrideNavigation:e,fetcherSubmission:s,preventScrollReset:l||j,enableViewTransition:o?O:void 0})}}async function te(e,t,r,n,i,o){let l,d={};try{l=await async function(e,t,r,n,i,o,a,s,l){let c=function(e,t){let r=e.map((e=>e.route.unstable_lazyMiddleware?async function(e,t){if(!e.unstable_lazyMiddleware)return;let r=t[e.id];if(F(r,"No route found in manifest"),r.unstable_middleware)z(!1,`Route "${r.id}" has a static property "unstable_middleware" defined. The "unstable_lazyMiddleware" function will be ignored.`);else{let t=await e.unstable_lazyMiddleware();if(!e.unstable_lazyMiddleware)return;r.unstable_middleware||(r.unstable_middleware=t)}r.unstable_lazyMiddleware=void 0}(e.route,t):void 0)).filter(Boolean);return r.length>0?Promise.all(r):void 0}(i,a),u=i.map((e=>e.route.lazy?async function(e,t,r){let n=r[e.id];if(F(n,"No route found in manifest"),!e.lazy)return;let i=$e.get(n);if(i)return void await i;let o=e.lazy().then((e=>{let r={};for(let t in e){let i=void 0!==n[t]&&"hasErrorBoundary"!==t;z(!i,`Route "${n.id}" has a static property "${t}" defined but its lazy function is also returning a value for this property. The lazy route property "${t}" will be ignored.`),z(!H.has(t),"Route property "+t+" is not a supported property to be returned from a lazy route function. This property will be ignored."),i||H.has(t)||(r[t]=e[t])}Object.assign(n,r),Object.assign(n,{...t(n),lazy:void 0})}));$e.set(n,o),await o}(e.route,s,a):void 0));c&&await c;let d=i.map(((e,i)=>{let o=u[i],a=n.some((t=>t.route.id===e.route.id));return{...e,shouldLoad:a,resolve:async n=>(n&&"GET"===r.method&&(e.route.lazy||e.route.loader)&&(a=!0),a?async function(e,t,r,n,i,o){let a,s,l=n=>{let a,l=new Promise(((e,t)=>a=t));s=()=>a(),t.signal.addEventListener("abort",s);let c=i=>"function"!=typeof n?Promise.reject(new Error(`You cannot call the handler for a route which defines a boolean "${e}" [routeId: ${r.route.id}]`)):n({request:t,params:r.params,context:o},...void 0!==i?[i]:[]),u=(async()=>{try{return{type:"data",result:await(i?i((e=>c(e))):c())}}catch(e){return{type:"error",result:e}}})();return Promise.race([u,l])};try{let i=r.route[e];if(n)if(i){let e,[t]=await Promise.all([l(i).catch((t=>{e=t})),n]);if(void 0!==e)throw e;a=t}else{if(await n,i=r.route[e],!i){if("action"===e){let e=new URL(t.url),n=e.pathname+e.search;throw tt(405,{method:t.method,pathname:n,routeId:r.route.id})}return{type:"data",result:void 0}}a=await l(i)}else{if(!i){let e=new URL(t.url);throw tt(404,{pathname:e.pathname+e.search})}a=await l(i)}}catch(e){return{type:"error",result:e}}finally{s&&t.signal.removeEventListener("abort",s)}return a}(t,r,e,o,n,l):Promise.resolve({type:"data",result:void 0}))}})),h=await e({matches:d,request:r,params:i[0].params,fetcherKey:o,context:l});try{await Promise.all(u)}catch(e){}return h}(u,e,t,r,n,o,s,a,i)}catch(e){return r.forEach((t=>{d[t.route.id]={type:"error",error:e}})),d}for(let[e,r]of Object.entries(l))if(it(r)){let i=r.result;d[e]={type:"redirect",response:Ue(i,t,e,n,c)}}else d[e]=await He(r);return d}async function re(t,r,n,i,o){let a=te("loader",i,r,t,o,null),s=Promise.all(n.map((async t=>{if(t.matches&&t.match&&t.controller){let r=(await te("loader",Ve(e.history,t.path,t.controller.signal),[t.match],t.matches,o,t.key))[t.match.route.id];return{[t.key]:r}}return Promise.resolve({[t.key]:{type:"error",error:tt(404,{pathname:t.path})}})})));return{loaderResults:await a,fetcherResults:(await s).reduce(((e,t)=>Object.assign(e,t)),{})}}function ne(){P=!0,I.forEach(((e,t)=>{R.has(t)&&M.add(t),ce(t)}))}function ie(e,t,r={}){E.fetchers.set(e,t),G({fetchers:new Map(E.fetchers)},{flushSync:!0===(r&&r.flushSync)})}function oe(e,t,r,n={}){let i=Ze(E.matches,t);le(e),G({errors:{[i.route.id]:r},fetchers:new Map(E.fetchers)},{flushSync:!0===(n&&n.flushSync)})}function ae(e){return B.set(e,(B.get(e)||0)+1),W.has(e)&&W.delete(e),E.fetchers.get(e)||je}function le(e){let t=E.fetchers.get(e);!R.has(e)||t&&"loading"===t.state&&T.has(e)||ce(e),I.delete(e),T.delete(e),D.delete(e),W.delete(e),M.delete(e),E.fetchers.delete(e)}function ce(e){let t=R.get(e);t&&(t.abort(),R.delete(e))}function ue(e){for(let t of e){let e=mt(ae(t).data);E.fetchers.set(t,e)}}function de(){let e=[],t=!1;for(let r of D){let n=E.fetchers.get(r);F(n,`Expected fetcher: ${r}`),"loading"===n.state&&(D.delete(r),e.push(r),t=!0)}return ue(e),t}function he(e){let t=[];for(let[r,n]of T)if(n<e){let e=E.fetchers.get(r);F(e,`Expected fetcher: ${r}`),"loading"===e.state&&(ce(r),T.delete(r),t.push(r))}return ue(t),t.length>0}function pe(e){E.blockers.delete(e),Y.delete(e)}function fe(e,t){let r=E.blockers.get(e)||Oe;F("unblocked"===r.state&&"blocked"===t.state||"blocked"===r.state&&"blocked"===t.state||"blocked"===r.state&&"proceeding"===t.state||"blocked"===r.state&&"unblocked"===t.state||"proceeding"===r.state&&"unblocked"===t.state,`Invalid blocker state transition: ${r.state} -> ${t.state}`);let n=new Map(E.blockers);n.set(e,t),G({blockers:n})}function me({currentLocation:e,nextLocation:t,historyAction:r}){if(0===Y.size)return;Y.size>1&&z(!1,"A router only supports one blocker at a time");let n=Array.from(Y.entries()),[i,o]=n[n.length-1],a=E.blockers.get(i);return a&&"proceeding"===a.state?void 0:o({currentLocation:e,nextLocation:t,historyAction:r})?i:void 0}function ge(e){let t=tt(404,{pathname:e}),r=n||l,{matches:i,route:o}=et(r);return{notFoundMatches:i,route:o,error:t}}function ve(e,t){return m&&m(e,t.map((e=>function(e,t){let{route:r,pathname:n,params:i}=e;return{id:r.id,pathname:n,params:i,data:t[r.id],handle:r.handle}}(e,E.loaderData))))||e.key}function be(e,t){if(f){let r=ve(e,t),n=f[r];if("number"==typeof n)return n}return null}function we(t,r,n){if(e.patchRoutesOnNavigation){if(!t)return{active:!0,matches:V(r,n,c,!0)||[]};if(Object.keys(t[0].params).length>0)return{active:!0,matches:V(r,n,c,!0)}}return{active:!1,matches:null}}async function xe(t,r,i,o){if(!e.patchRoutesOnNavigation)return{type:"success",matches:t};let u=t;for(;;){let t=null==n,d=n||l,h=s;try{await e.patchRoutesOnNavigation({signal:i,path:r,matches:u,fetcherKey:o,patch:(e,t)=>{i.aborted||ze(e,t,d,h,a)}})}catch(e){return{type:"error",error:e,partialMatches:u}}finally{t&&!i.aborted&&(l=[...l])}if(i.aborted)return{type:"aborted"};let p=Q(d,r,c);if(p)return{type:"success",matches:p};let f=V(d,r,c,!0);if(!f||u.length===f.length&&u.every(((e,t)=>e.route.id===f[t].route.id)))return{type:"success",matches:null};u=f}}return o={get basename(){return c},get future(){return d},get state(){return E},get routes(){return l},get window(){return t},initialize:function(){if(h=e.history.listen((({action:t,location:r,delta:n})=>{if(k)return k(),void(k=void 0);z(0===Y.size||null!=n,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let i=me({currentLocation:E.location,nextLocation:r,historyAction:t});if(i&&null!=n){let t=new Promise((e=>{k=e}));return e.history.go(-1*n),void fe(i,{state:"blocked",location:r,proceed(){fe(i,{state:"proceeding",proceed:void 0,reset:void 0,location:r}),t.then((()=>e.history.go(n)))},reset(){let e=new Map(E.blockers);e.set(i,Oe),G({blockers:e})}})}return J(t,r)})),r){!function(e,t){try{let r=e.sessionStorage.getItem(Ae);if(r){let e=JSON.parse(r);for(let[r,n]of Object.entries(e||{}))n&&Array.isArray(n)&&t.set(r,new Set(n||[]))}}catch(e){}}(t,_);let e=()=>function(e,t){if(t.size>0){let r={};for(let[e,n]of t)r[e]=[...n];try{e.sessionStorage.setItem(Ae,JSON.stringify(r))}catch(e){z(!1,`Failed to save applied view transitions in sessionStorage (${e}).`)}}}(t,_);t.addEventListener("pagehide",e),S=()=>t.removeEventListener("pagehide",e)}return E.initialized||J("POP",E.location,{initialHydration:!0}),o},subscribe:function(e){return p.add(e),()=>p.delete(e)},enableScrollRestoration:function(e,t,r){if(f=e,g=t,m=r||null,!y&&E.navigation===Ce){y=!0;let e=be(E.location,E.matches);null!=e&&G({restoreScrollPosition:e})}return()=>{f=null,g=null,m=null}},navigate:async function t(r,n){if("number"==typeof r)return void e.history.go(r);let i=Me(E.location,E.matches,c,r,n?.fromRouteId,n?.relative),{path:o,submission:a,error:s}=Re(!1,i,n),l=E.location,u=$(E.location,o,n&&n.state);u={...u,...e.history.encodeLocation(u)};let d=n&&null!=n.replace?n.replace:void 0,h="PUSH";!0===d?h="REPLACE":!1===d||null!=a&&ct(a.formMethod)&&a.formAction===E.location.pathname+E.location.search&&(h="REPLACE");let p=n&&"preventScrollReset"in n?!0===n.preventScrollReset:void 0,f=!0===(n&&n.flushSync),m=me({currentLocation:l,nextLocation:u,historyAction:h});m?fe(m,{state:"blocked",location:u,proceed(){fe(m,{state:"proceeding",proceed:void 0,reset:void 0,location:u}),t(r,n)},reset(){let e=new Map(E.blockers);e.set(m,Oe),G({blockers:e})}}):await J(h,u,{submission:a,pendingError:s,preventScrollReset:p,replace:n&&n.replace,enableViewTransition:n&&n.viewTransition,flushSync:f})},fetch:async function(t,r,i,o){ce(t);let a=!0===(o&&o.flushSync),s=n||l,u=Me(E.location,E.matches,c,i,r,o?.relative),d=Q(s,u,c),h=we(d,s,u);if(h.active&&h.matches&&(d=h.matches),!d)return void oe(t,r,tt(404,{pathname:u}),{flushSync:a});let{path:p,submission:f,error:m}=Re(!0,u,o);if(m)return void oe(t,r,m,{flushSync:a});let g=dt(d,p),y=new q(e.unstable_getContext?await e.unstable_getContext():void 0),v=!0===(o&&o.preventScrollReset);f&&ct(f.formMethod)?await async function(t,r,i,o,a,s,u,d,h,p){function f(e){if(!e.route.action&&!e.route.lazy){let e=tt(405,{method:p.formMethod,pathname:i,routeId:r});return oe(t,r,e,{flushSync:d}),!0}return!1}if(ne(),I.delete(t),!u&&f(o))return;let m=E.fetchers.get(t);ie(t,function(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}(p,m),{flushSync:d});let g=new AbortController,y=Ve(e.history,i,g.signal,p);if(u){let e=await xe(a,i,y.signal,t);if("aborted"===e.type)return;if("error"===e.type)return void oe(t,r,e.error,{flushSync:d});if(!e.matches)return void oe(t,r,tt(404,{pathname:i}),{flushSync:d});if(f(o=dt(a=e.matches,i)))return}R.set(t,g);let v=N,b=(await te("action",y,[o],a,s,t))[o.route.id];if(y.signal.aborted)return void(R.get(t)===g&&R.delete(t));if(W.has(t)){if(at(b)||ot(b))return void ie(t,mt(void 0))}else{if(at(b))return R.delete(t),L>v?void ie(t,mt(void 0)):(D.add(t),ie(t,ft(p)),ee(y,b,!1,{fetcherSubmission:p,preventScrollReset:h}));if(ot(b))return void oe(t,r,b.error)}let w=E.navigation.location||E.location,k=Ve(e.history,w,g.signal),j=n||l,O="idle"!==E.navigation.state?Q(j,E.navigation.location,c):E.matches;F(O,"Didn't find any matches after fetcher action");let _=++N;T.set(t,_);let S=ft(p,b.data);E.fetchers.set(t,S);let[A,z]=Le(e.history,E,O,p,w,!1,P,M,W,I,D,j,c,[o.route.id,b]);z.filter((e=>e.key!==t)).forEach((e=>{let t=e.key,r=E.fetchers.get(t),n=ft(void 0,r?r.data:void 0);E.fetchers.set(t,n),ce(t),e.controller&&R.set(t,e.controller)})),G({fetchers:new Map(E.fetchers)});let $=()=>z.forEach((e=>ce(e.key)));g.signal.addEventListener("abort",$);let{loaderResults:B,fetcherResults:q}=await re(O,A,z,k,s);if(g.signal.aborted)return;g.signal.removeEventListener("abort",$),T.delete(t),R.delete(t),z.forEach((e=>R.delete(e.key)));let H=rt(B);if(H)return ee(k,H.result,!1,{preventScrollReset:h});if(H=rt(q),H)return D.add(H.key),ee(k,H.result,!1,{preventScrollReset:h});let{loaderData:U,errors:V}=Ge(E,O,B,void 0,z,q);if(E.fetchers.has(t)){let e=mt(b.data);E.fetchers.set(t,e)}he(_),"loading"===E.navigation.state&&_>L?(F(C,"Expected pending action"),x&&x.abort(),X(E.navigation.location,{matches:O,loaderData:U,errors:V,fetchers:new Map(E.fetchers)})):(G({errors:V,loaderData:Xe(E.loaderData,U,O,V),fetchers:new Map(E.fetchers)}),P=!1)}(t,r,p,g,d,y,h.active,a,v,f):(I.set(t,{routeId:r,path:p}),await async function(t,r,n,i,o,a,s,l,c,u){let d=E.fetchers.get(t);ie(t,ft(u,d?d.data:void 0),{flushSync:l});let h=new AbortController,p=Ve(e.history,n,h.signal);if(s){let e=await xe(o,n,p.signal,t);if("aborted"===e.type)return;if("error"===e.type)return void oe(t,r,e.error,{flushSync:l});if(!e.matches)return void oe(t,r,tt(404,{pathname:n}),{flushSync:l});i=dt(o=e.matches,n)}R.set(t,h);let f=N,m=(await te("loader",p,[i],o,a,t))[i.route.id];if(R.get(t)===h&&R.delete(t),!p.signal.aborted){if(!W.has(t))return at(m)?L>f?void ie(t,mt(void 0)):(D.add(t),void await ee(p,m,!1,{preventScrollReset:c})):void(ot(m)?oe(t,r,m.error):ie(t,mt(m.data)));ie(t,mt(void 0))}}(t,r,p,g,d,y,h.active,a,v,f))},revalidate:function(){K||(K=function(){let e,t,r=new Promise(((n,i)=>{e=async e=>{n(e);try{await r}catch(e){}},t=async e=>{i(e);try{await r}catch(e){}}}));return{promise:r,resolve:e,reject:t}}()),ne(),G({revalidation:"loading"});let e=K.promise;return"submitting"===E.navigation.state?e:"idle"===E.navigation.state?(J(E.historyAction,E.location,{startUninterruptedRevalidation:!0}),e):(J(C||E.historyAction,E.navigation.location,{overrideNavigation:E.navigation,enableViewTransition:!0===O}),e)},createHref:t=>e.history.createHref(t),encodeLocation:t=>e.history.encodeLocation(t),getFetcher:ae,deleteFetcher:function(e){let t=(B.get(e)||0)-1;t<=0?(B.delete(e),W.add(e)):B.set(e,t),G({fetchers:new Map(E.fetchers)})},dispose:function(){h&&h(),S&&S(),p.clear(),x&&x.abort(),E.fetchers.forEach(((e,t)=>le(t))),E.blockers.forEach(((e,t)=>pe(t)))},getBlocker:function(e,t){let r=E.blockers.get(e)||Oe;return Y.get(e)!==t&&Y.set(e,t),r},deleteBlocker:pe,patchRoutes:function(e,t){let r=null==n;ze(e,t,n||l,s,a),r&&(l=[...l],G({}))},_internalFetchControllers:R,_internalSetRoutes:function(e){s={},n=U(e,a,void 0,s)}},o}({basename:undefined,unstable_getContext:undefined,future:undefined,history:function(e={}){return function(e,t,r,n={}){let{window:i=document.defaultView,v5Compat:o=!1}=n,a=i.history,s="POP",l=null,c=u();function u(){return(a.state||{idx:null}).idx}function d(){s="POP";let e=u(),t=null==e?null:e-c;c=e,l&&l({action:s,location:p.location,delta:t})}function h(e){let t="null"!==i.location.origin?i.location.origin:i.location.href,r="string"==typeof e?e:B(e);return r=r.replace(/ $/,"%20"),F(t,`No window.location.(origin|href) available to create URL for href: ${r}`),new URL(r,t)}null==c&&(c=0,a.replaceState({...a.state,idx:c},""));let p={get action(){return s},get location(){return e(i,a)},listen(e){if(l)throw new Error("A history only accepts one active listener");return i.addEventListener(D,d),l=e,()=>{i.removeEventListener(D,d),l=null}},createHref:e=>t(i,e),createURL:h,encodeLocation(e){let t=h(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){s="PUSH";let n=$(p.location,e,t);r&&r(n,e),c=u()+1;let d=I(n,c),h=p.createHref(n);try{a.pushState(d,"",h)}catch(e){if(e instanceof DOMException&&"DataCloneError"===e.name)throw e;i.location.assign(h)}o&&l&&l({action:s,location:p.location,delta:1})},replace:function(e,t){s="REPLACE";let n=$(p.location,e,t);r&&r(n,e),c=u();let i=I(n,c),d=p.createHref(n);a.replaceState(i,"",d),o&&l&&l({action:s,location:p.location,delta:0})},go:e=>a.go(e)};return p}((function(e,t){let{pathname:r="/",search:n="",hash:i=""}=W(e.location.hash.substring(1));return r.startsWith("/")||r.startsWith(".")||(r="/"+r),$("",{pathname:r,search:n,hash:i},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){let r=e.document.querySelector("base"),n="";if(r&&r.getAttribute("href")){let t=e.location.href,r=t.indexOf("#");n=-1===r?t:t.slice(0,r)}return n+"#"+("string"==typeof t?t:B(t))}),(function(e,t){z("/"===e.pathname.charAt(0),`relative pathnames are not supported in hash history.push(${JSON.stringify(t)})`)}),e)}({window:undefined}),hydrationData:function(){let e=window?.__staticRouterHydrationData;return e&&e.errors&&(e={...e,errors:fr(e.errors)}),e}(),routes:ou,mapRouteProperties:function(e){let t={hasErrorBoundary:e.hasErrorBoundary||null!=e.ErrorBoundary||null!=e.errorElement};return e.Component&&(e.element&&z(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(t,{element:n.createElement(e.Component),Component:void 0})),e.HydrateFallback&&(e.hydrateFallbackElement&&z(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(t,{hydrateFallbackElement:n.createElement(e.HydrateFallback),HydrateFallback:void 0})),e.ErrorBoundary&&(e.errorElement&&z(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(t,{errorElement:n.createElement(e.ErrorBoundary),ErrorBoundary:void 0})),t},dataStrategy:undefined,patchRoutesOnNavigation:undefined,window:undefined}).initialize()),lu=document.getElementById("gwiz-spellbook");lu&&(0,o.H)(lu).render((0,t.jsx)((function(){return(0,t.jsxs)(pa,{client:au,children:[(0,t.jsx)(Kc,{defaultSettings:{closeButtonAttributes:{icon:"x",iconPrefix:"gravity-component-icon"}},children:(0,t.jsx)(Xc,{router:su})}),(0,t.jsx)(Jc,{})]})}),{}))})()})();
//# sourceMappingURL=dashboard.js.map